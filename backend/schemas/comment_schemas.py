"""
Waline评论系统数据模式定义
用于与Waline服务器的数据交换和本地备份
"""

from pydantic import BaseModel, Field
from typing import Optional, List, Dict, Any, Union
from datetime import datetime
from enum import Enum


class CommentStatus(str, Enum):
    """评论状态枚举"""
    APPROVED = "approved"      # 已审核通过
    WAITING = "waiting"        # 等待审核
    SPAM = "spam"             # 垃圾评论
    DELETED = "deleted"       # 已删除


class CommentType(str, Enum):
    """评论类型枚举"""
    COMMENT = "comment"       # 普通评论
    REPLY = "reply"          # 回复评论
    ADMIN = "admin"          # 管理员评论


class UserLevel(str, Enum):
    """用户等级枚举"""
    LEVEL0 = "level0"        # 新手
    LEVEL1 = "level1"        # 探索者
    LEVEL2 = "level2"        # 贡献者
    LEVEL3 = "level3"        # 专家
    LEVEL4 = "level4"        # 大师
    LEVEL5 = "level5"        # 传奇
    ADMIN = "admin"          # 管理员


# === 基础数据模型 ===

class WalineUser(BaseModel):
    """Waline用户信息模型"""
    objectId: Optional[str] = None
    nick: str = Field(..., description="用户昵称")
    mail: str = Field(..., description="用户邮箱")
    link: Optional[str] = Field(None, description="用户网站")
    avatar: Optional[str] = Field(None, description="用户头像URL")
    type: Optional[str] = Field("comment", description="用户类型")
    label: Optional[str] = Field(None, description="用户标签")
    level: UserLevel = Field(UserLevel.LEVEL0, description="用户等级")
    mailMd5: Optional[str] = Field(None, description="邮箱MD5值")
    createdAt: Optional[datetime] = Field(None, description="注册时间")
    updatedAt: Optional[datetime] = Field(None, description="更新时间")


class WalineComment(BaseModel):
    """Waline评论数据模型"""
    objectId: Optional[str] = Field(None, description="评论ID")
    
    # 基本信息
    nick: str = Field(..., description="评论者昵称")
    mail: str = Field(..., description="评论者邮箱")
    link: Optional[str] = Field(None, description="评论者网站")
    comment: str = Field(..., description="评论内容")
    
    # 页面信息
    url: str = Field(..., description="评论页面URL")
    ua: Optional[str] = Field(None, description="用户代理")
    ip: Optional[str] = Field(None, description="IP地址")
    
    # 关联信息
    pid: Optional[str] = Field(None, description="父评论ID")
    rid: Optional[str] = Field(None, description="根评论ID")
    
    # 状态信息
    status: CommentStatus = Field(CommentStatus.APPROVED, description="评论状态")
    like: int = Field(0, description="点赞数")
    dislike: int = Field(0, description="点踩数")
    
    # 用户信息
    avatar: Optional[str] = Field(None, description="头像URL")
    type: CommentType = Field(CommentType.COMMENT, description="评论类型")
    level: UserLevel = Field(UserLevel.LEVEL0, description="用户等级")
    
    # 时间信息
    createdAt: Optional[datetime] = Field(None, description="创建时间")
    updatedAt: Optional[datetime] = Field(None, description="更新时间")
    
    # 子评论
    children: Optional[List['WalineComment']] = Field(None, description="子评论列表")


class WalinePageStats(BaseModel):
    """页面统计数据模型"""
    url: str = Field(..., description="页面URL")
    title: Optional[str] = Field(None, description="页面标题")
    
    # 统计数据
    time: int = Field(0, description="浏览量")
    comment: int = Field(0, description="评论数")
    like: int = Field(0, description="点赞数")
    dislike: int = Field(0, description="点踩数")
    
    # 反应统计
    reaction: List[int] = Field(default_factory=list, description="各类反应统计")
    
    # 时间信息
    createdAt: Optional[datetime] = Field(None, description="创建时间")
    updatedAt: Optional[datetime] = Field(None, description="更新时间")


# === API请求响应模型 ===

class CommentListRequest(BaseModel):
    """评论列表请求参数"""
    path: str = Field(..., description="页面路径")
    page: int = Field(1, ge=1, description="页码")
    pageSize: int = Field(10, ge=1, le=100, description="每页数量")
    sortBy: str = Field("createdAt", description="排序字段")
    order: str = Field("desc", description="排序方向")
    type: Optional[str] = Field(None, description="评论类型筛选")
    owner: Optional[str] = Field(None, description="作者筛选")


class CommentListResponse(BaseModel):
    """评论列表响应"""
    count: int = Field(..., description="总评论数")
    totalPages: int = Field(..., description="总页数")
    page: int = Field(..., description="当前页码")
    pageSize: int = Field(..., description="每页数量")
    data: List[WalineComment] = Field(..., description="评论列表")


class CommentCountRequest(BaseModel):
    """评论数量请求参数"""
    type: str = Field("count", description="请求类型")
    url: Optional[Union[str, List[str]]] = Field(None, description="页面URL")
    path: Optional[Union[str, List[str]]] = Field(None, description="页面路径")


class CommentCountResponse(BaseModel):
    """评论数量响应"""
    data: Union[int, List[int], Dict[str, int]] = Field(..., description="评论数量")


class PageViewRequest(BaseModel):
    """页面浏览量请求参数"""
    path: str = Field(..., description="页面路径")
    title: Optional[str] = Field(None, description="页面标题")
    action: str = Field("inc", description="操作类型: inc(增加), get(获取)")


class PageViewResponse(BaseModel):
    """页面浏览量响应"""
    time: int = Field(..., description="浏览量")
    path: str = Field(..., description="页面路径")


class RecentCommentsRequest(BaseModel):
    """最近评论请求参数"""
    count: int = Field(10, ge=1, le=50, description="获取数量")
    owner: Optional[str] = Field(None, description="作者筛选")


class RecentCommentsResponse(BaseModel):
    """最近评论响应"""
    data: List[WalineComment] = Field(..., description="最近评论列表")


# === 本地备份数据模型 ===

class LocalCommentBackup(BaseModel):
    """本地评论备份模型"""
    id: Optional[int] = Field(None, description="本地ID")
    waline_id: str = Field(..., description="Waline评论ID")
    
    # 评论内容
    content: str = Field(..., description="评论内容")
    author_name: str = Field(..., description="作者姓名")
    author_email: str = Field(..., description="作者邮箱")
    author_url: Optional[str] = Field(None, description="作者网站")
    
    # 页面信息
    page_url: str = Field(..., description="页面URL")
    page_title: Optional[str] = Field(None, description="页面标题")
    
    # 关联信息
    parent_id: Optional[str] = Field(None, description="父评论ID")
    root_id: Optional[str] = Field(None, description="根评论ID")
    
    # 状态信息
    status: CommentStatus = Field(..., description="评论状态")
    like_count: int = Field(0, description="点赞数")
    dislike_count: int = Field(0, description="点踩数")
    
    # 元数据
    ip_address: Optional[str] = Field(None, description="IP地址")
    user_agent: Optional[str] = Field(None, description="用户代理")
    
    # 时间信息
    created_at: datetime = Field(..., description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")
    backup_at: datetime = Field(..., description="备份时间")
    
    # JSON数据
    raw_data: Dict[str, Any] = Field(default_factory=dict, description="原始数据")


class LocalPageStatsBackup(BaseModel):
    """本地页面统计备份模型"""
    id: Optional[int] = Field(None, description="本地ID")
    page_url: str = Field(..., description="页面URL")
    page_title: Optional[str] = Field(None, description="页面标题")
    
    # 统计数据
    view_count: int = Field(0, description="浏览量")
    comment_count: int = Field(0, description="评论数")
    like_count: int = Field(0, description="点赞数")
    dislike_count: int = Field(0, description="点踩数")
    
    # 时间信息
    stats_date: datetime = Field(..., description="统计日期")
    backup_at: datetime = Field(..., description="备份时间")
    
    # JSON数据
    raw_data: Dict[str, Any] = Field(default_factory=dict, description="原始统计数据")


# === 数据同步模型 ===

class SyncTask(BaseModel):
    """数据同步任务模型"""
    task_id: str = Field(..., description="任务ID")
    task_type: str = Field(..., description="任务类型: comment, stats, user")
    status: str = Field("pending", description="任务状态: pending, running, completed, failed")
    
    # 任务参数
    pages: Optional[List[str]] = Field(None, description="要同步的页面列表")
    start_date: Optional[datetime] = Field(None, description="开始日期")
    end_date: Optional[datetime] = Field(None, description="结束日期")
    
    # 结果统计
    total_items: int = Field(0, description="总项目数")
    synced_items: int = Field(0, description="已同步项目数")
    failed_items: int = Field(0, description="失败项目数")
    
    # 时间信息
    created_at: datetime = Field(..., description="创建时间")
    started_at: Optional[datetime] = Field(None, description="开始时间")
    completed_at: Optional[datetime] = Field(None, description="完成时间")
    
    # 错误信息
    error_message: Optional[str] = Field(None, description="错误信息")
    error_details: Dict[str, Any] = Field(default_factory=dict, description="错误详情")


class SyncResult(BaseModel):
    """同步结果模型"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="结果消息")
    
    # 统计信息
    total_processed: int = Field(0, description="总处理数量")
    successful: int = Field(0, description="成功数量")
    failed: int = Field(0, description="失败数量")
    skipped: int = Field(0, description="跳过数量")
    
    # 详细结果
    details: List[Dict[str, Any]] = Field(default_factory=list, description="详细结果")
    errors: List[str] = Field(default_factory=list, description="错误列表")
    
    # 时间信息
    start_time: datetime = Field(..., description="开始时间")
    end_time: datetime = Field(..., description="结束时间")
    duration: float = Field(..., description="耗时(秒)")


# === 管理后台模型 ===

class CommentModerationRequest(BaseModel):
    """评论审核请求"""
    comment_id: str = Field(..., description="评论ID")
    action: str = Field(..., description="操作: approve, reject, spam, delete")
    reason: Optional[str] = Field(None, description="操作原因")


class CommentModerationResponse(BaseModel):
    """评论审核响应"""
    success: bool = Field(..., description="是否成功")
    message: str = Field(..., description="操作结果")
    comment: Optional[WalineComment] = Field(None, description="更新后的评论")


class SpamFilterRule(BaseModel):
    """垃圾评论过滤规则"""
    id: Optional[int] = Field(None, description="规则ID")
    name: str = Field(..., description="规则名称")
    rule_type: str = Field(..., description="规则类型: keyword, ip, email, url")
    pattern: str = Field(..., description="匹配模式")
    action: str = Field(..., description="处理动作: block, review, delete")
    enabled: bool = Field(True, description="是否启用")
    
    # 统计信息
    match_count: int = Field(0, description="匹配次数")
    
    # 时间信息
    created_at: datetime = Field(..., description="创建时间")
    updated_at: Optional[datetime] = Field(None, description="更新时间")


class CommentAnalytics(BaseModel):
    """评论分析数据"""
    # 时间范围
    start_date: datetime = Field(..., description="开始日期")
    end_date: datetime = Field(..., description="结束日期")
    
    # 基础统计
    total_comments: int = Field(0, description="总评论数")
    approved_comments: int = Field(0, description="已审核评论数")
    pending_comments: int = Field(0, description="待审核评论数")
    spam_comments: int = Field(0, description="垃圾评论数")
    
    # 用户统计
    unique_users: int = Field(0, description="独立用户数")
    registered_users: int = Field(0, description="注册用户数")
    anonymous_users: int = Field(0, description="匿名用户数")
    
    # 页面统计
    most_commented_pages: List[Dict[str, Any]] = Field(default_factory=list, description="评论最多的页面")
    active_pages: int = Field(0, description="有评论的页面数")
    
    # 时间分布
    daily_stats: List[Dict[str, Any]] = Field(default_factory=list, description="每日统计")
    hourly_distribution: List[int] = Field(default_factory=list, description="小时分布")
    
    # 互动统计
    total_likes: int = Field(0, description="总点赞数")
    total_dislikes: int = Field(0, description="总点踩数")
    engagement_rate: float = Field(0.0, description="互动率")


# 解决循环引用问题
WalineComment.model_rebuild()