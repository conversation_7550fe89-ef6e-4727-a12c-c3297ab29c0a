from pydantic import BaseModel, Field
from typing import Optional, Union
from datetime import datetime

class SystemConfigBase(BaseModel):
    key: str = Field(..., max_length=100, description="配置键名")
    value: Optional[str] = Field(None, description="配置值")
    description: Optional[str] = Field(None, max_length=255, description="配置描述")
    config_type: str = Field(default="string", description="配置类型：string, number, boolean, json")

class SystemConfigCreate(SystemConfigBase):
    pass

class SystemConfigUpdate(BaseModel):
    value: Optional[str] = Field(None, description="配置值")
    description: Optional[str] = Field(None, max_length=255, description="配置描述")
    config_type: Optional[str] = Field(None, description="配置类型")
    is_active: Optional[bool] = Field(None, description="是否启用")

class SystemConfigResponse(SystemConfigBase):
    id: int
    is_active: bool
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True

class WalineConfigResponse(BaseModel):
    server_url: str = Field(..., description="Waline服务器地址")
    lang: str = Field(default="zh-CN", description="语言设置")
    
class WalineConfigUpdate(BaseModel):
    server_url: Optional[str] = Field(None, description="Waline服务器地址")
    lang: Optional[str] = Field(None, description="语言设置")