from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete
from database.database import get_db
from models.system_config import SystemConfig
from schemas.system_config_schemas import (
    SystemConfigResponse, SystemConfigCreate, SystemConfigUpdate,
    WalineConfigResponse, WalineConfigUpdate
)
from utils.auth import get_current_user
from typing import List, Optional
import json
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/system-config", tags=["system-config"])

# 默认配置值
DEFAULT_CONFIGS = {
    "waline.server_url": {
        "value": "https://waline.jyaochen.cn",
        "description": "Waline评论系统服务器地址",
        "config_type": "string"
    },
    "waline.lang": {
        "value": "zh-CN",
        "description": "Waline语言设置",
        "config_type": "string"
    }
}

async def get_config_value(db: AsyncSession, key: str, default: Optional[str] = None) -> Optional[str]:
    """获取配置值"""
    result = await db.execute(
        select(SystemConfig).where(
            SystemConfig.key == key,
            SystemConfig.is_active == True
        )
    )
    config = result.scalar_one_or_none()
    return config.value if config else default

async def set_config_value(db: AsyncSession, key: str, value: str, description: str = None, config_type: str = "string"):
    """设置配置值"""
    result = await db.execute(
        select(SystemConfig).where(SystemConfig.key == key)
    )
    config = result.scalar_one_or_none()
    
    if config:
        # 更新现有配置
        await db.execute(
            update(SystemConfig)
            .where(SystemConfig.key == key)
            .values(
                value=value,
                description=description or config.description,
                config_type=config_type,
                is_active=True
            )
        )
    else:
        # 创建新配置
        new_config = SystemConfig(
            key=key,
            value=value,
            description=description,
            config_type=config_type,
            is_active=True
        )
        db.add(new_config)
    
    await db.commit()

@router.post("/init", summary="初始化系统配置")
async def init_system_configs(
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """初始化系统默认配置"""
    try:
        for key, config_data in DEFAULT_CONFIGS.items():
            await set_config_value(
                db=db,
                key=key,
                value=config_data["value"],
                description=config_data["description"],
                config_type=config_data["config_type"]
            )
        
        return {"message": "系统配置初始化成功"}
    except Exception as e:
        logger.error(f"初始化系统配置失败: {e}")
        raise HTTPException(status_code=500, detail="初始化系统配置失败")

@router.get("/", response_model=List[SystemConfigResponse], summary="获取所有系统配置")
async def get_all_configs(
    is_active: Optional[bool] = None,
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """获取所有系统配置"""
    query = select(SystemConfig)
    if is_active is not None:
        query = query.where(SystemConfig.is_active == is_active)
    
    result = await db.execute(query.order_by(SystemConfig.key))
    configs = result.scalars().all()
    return configs

@router.get("/{key}", response_model=SystemConfigResponse, summary="获取指定配置")
async def get_config(
    key: str,
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """获取指定配置"""
    result = await db.execute(
        select(SystemConfig).where(SystemConfig.key == key)
    )
    config = result.scalar_one_or_none()
    
    if not config:
        raise HTTPException(status_code=404, detail="配置不存在")
    
    return config

@router.post("/", response_model=SystemConfigResponse, summary="创建系统配置")
async def create_config(
    config: SystemConfigCreate,
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """创建新的系统配置"""
    # 检查配置是否已存在
    result = await db.execute(
        select(SystemConfig).where(SystemConfig.key == config.key)
    )
    existing_config = result.scalar_one_or_none()
    
    if existing_config:
        raise HTTPException(status_code=400, detail="配置已存在")
    
    new_config = SystemConfig(**config.model_dump())
    db.add(new_config)
    await db.commit()
    await db.refresh(new_config)
    
    return new_config

@router.put("/{key}", response_model=SystemConfigResponse, summary="更新系统配置")
async def update_config(
    key: str,
    config_update: SystemConfigUpdate,
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """更新系统配置"""
    result = await db.execute(
        select(SystemConfig).where(SystemConfig.key == key)
    )
    config = result.scalar_one_or_none()
    
    if not config:
        raise HTTPException(status_code=404, detail="配置不存在")
    
    update_data = config_update.model_dump(exclude_unset=True)
    
    await db.execute(
        update(SystemConfig)
        .where(SystemConfig.key == key)
        .values(**update_data)
    )
    
    await db.commit()
    await db.refresh(config)
    
    return config

@router.delete("/{key}", summary="删除系统配置")
async def delete_config(
    key: str,
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """删除系统配置"""
    result = await db.execute(
        select(SystemConfig).where(SystemConfig.key == key)
    )
    config = result.scalar_one_or_none()
    
    if not config:
        raise HTTPException(status_code=404, detail="配置不存在")
    
    await db.execute(
        delete(SystemConfig).where(SystemConfig.key == key)
    )
    
    await db.commit()
    
    return {"message": "配置删除成功"}

# Waline 专用配置接口
@router.get("/waline/config", response_model=WalineConfigResponse, summary="获取Waline配置")
async def get_waline_config(db: AsyncSession = Depends(get_db)):
    """获取Waline配置（公开接口）"""
    server_url = await get_config_value(db, "waline.server_url", DEFAULT_CONFIGS["waline.server_url"]["value"])
    lang = await get_config_value(db, "waline.lang", DEFAULT_CONFIGS["waline.lang"]["value"])
    
    return WalineConfigResponse(
        server_url=server_url,
        lang=lang
    )

@router.put("/waline/config", response_model=WalineConfigResponse, summary="更新Waline配置")
async def update_waline_config(
    config_update: WalineConfigUpdate,
    db: AsyncSession = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """更新Waline配置"""
    if config_update.server_url:
        await set_config_value(
            db=db,
            key="waline.server_url",
            value=config_update.server_url,
            description="Waline评论系统服务器地址",
            config_type="string"
        )
    
    if config_update.lang:
        await set_config_value(
            db=db,
            key="waline.lang",
            value=config_update.lang,
            description="Waline语言设置",
            config_type="string"
        )
    
    # 返回更新后的配置
    server_url = await get_config_value(db, "waline.server_url", DEFAULT_CONFIGS["waline.server_url"]["value"])
    lang = await get_config_value(db, "waline.lang", DEFAULT_CONFIGS["waline.lang"]["value"])
    
    return WalineConfigResponse(
        server_url=server_url,
        lang=lang
    )