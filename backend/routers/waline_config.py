from fastapi import APIRouter, HTTPException, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from database.database import get_db
from models.system_config import SystemConfig
from pydantic import BaseModel
from typing import Optional
import logging

logger = logging.getLogger(__name__)

router = APIRouter()

class WalineConfigRequest(BaseModel):
    server_url: str
    admin_url: Optional[str] = None

class WalineConfigResponse(BaseModel):
    server_url: str
    admin_url: str

async def get_waline_config(db: AsyncSession) -> dict:
    """获取Waline配置"""
    try:
        # 获取服务器地址
        server_result = await db.execute(
            select(SystemConfig).filter(SystemConfig.key == "waline_server_url")
        )
        server_config = server_result.scalar_one_or_none()

        # 获取管理界面地址
        admin_result = await db.execute(
            select(SystemConfig).filter(SystemConfig.key == "waline_admin_url")
        )
        admin_config = admin_result.scalar_one_or_none()
        
        server_url = server_config.value if server_config else "https://waline.jyaochen.cn"
        admin_url = admin_config.value if admin_config else f"{server_url}/ui"
        
        return {
            "server_url": server_url,
            "admin_url": admin_url
        }
    except Exception as e:
        logger.error(f"获取Waline配置失败: {e}")
        # 返回默认配置
        return {
            "server_url": "https://waline.jyaochen.cn",
            "admin_url": "https://waline.jyaochen.cn/ui"
        }

async def set_waline_config(db: AsyncSession, server_url: str, admin_url: Optional[str] = None):
    """设置Waline配置"""
    try:
        # 如果没有提供admin_url，自动生成
        if not admin_url:
            admin_url = f"{server_url.rstrip('/')}/ui"
        
        # 更新或创建服务器地址配置
        server_result = await db.execute(
            select(SystemConfig).filter(SystemConfig.key == "waline_server_url")
        )
        server_config = server_result.scalar_one_or_none()

        if server_config:
            server_config.value = server_url
        else:
            server_config = SystemConfig(
                key="waline_server_url",
                value=server_url,
                description="Waline评论系统服务器地址"
            )
            db.add(server_config)

        # 更新或创建管理界面地址配置
        admin_result = await db.execute(
            select(SystemConfig).filter(SystemConfig.key == "waline_admin_url")
        )
        admin_config = admin_result.scalar_one_or_none()

        if admin_config:
            admin_config.value = admin_url
        else:
            admin_config = SystemConfig(
                key="waline_admin_url",
                value=admin_url,
                description="Waline评论系统管理界面地址"
            )
            db.add(admin_config)

        await db.commit()
        return True
    except Exception as e:
        logger.error(f"设置Waline配置失败: {e}")
        await db.rollback()
        return False

@router.get("/waline/config", response_model=WalineConfigResponse)
async def get_waline_config_api(db: AsyncSession = Depends(get_db)):
    """获取Waline配置"""
    try:
        config = await get_waline_config(db)
        return WalineConfigResponse(**config)
    except Exception as e:
        logger.error(f"获取Waline配置API失败: {e}")
        raise HTTPException(status_code=500, detail="获取Waline配置失败")

@router.post("/waline/config")
async def set_waline_config_api(
    config: WalineConfigRequest,
    db: AsyncSession = Depends(get_db)
):
    """设置Waline配置"""
    try:
        success = await set_waline_config(
            db,
            config.server_url,
            config.admin_url
        )

        if success:
            return {"message": "Waline配置更新成功"}
        else:
            raise HTTPException(status_code=500, detail="Waline配置更新失败")
    except Exception as e:
        logger.error(f"设置Waline配置API失败: {e}")
        raise HTTPException(status_code=500, detail="设置Waline配置失败")

# 公共API，供前端使用
public_router = APIRouter()

@public_router.get("/public/waline/config")
async def get_public_waline_config(db: AsyncSession = Depends(get_db)):
    """获取公共Waline配置（供前端使用）"""
    try:
        config = await get_waline_config(db)
        # 只返回前端需要的服务器地址
        return {"server_url": config["server_url"]}
    except Exception as e:
        logger.error(f"获取公共Waline配置失败: {e}")
        # 返回默认配置
        return {"server_url": "https://waline.jyaochen.cn"}
