import React, { useState } from 'react';
import { Layout, Menu, theme, Breadcrumb, Button, Space, Avatar, Dropdown, MenuProps } from 'antd';
import { Outlet, Link, useNavigate, useLocation } from 'react-router-dom'; // Outlet 用于渲染子路由内容
// import ThemeToggle from '../components/ThemeToggle'; // 移除深色模式切换
import ThemeSettings from '../components/ThemeSettings';
import SmartSearch from '../components/SmartSearch';
import {
  DesktopOutlined,
  FileTextOutlined,
  PieChartOutlined,
  TeamOutlined,
  UserOutlined,
  LogoutOutlined,
  PictureOutlined,
  AppstoreOutlined, // Import category icon
  UnorderedListOutlined, // Icon for Article List
  ProjectOutlined, // Import Project icon
  SettingOutlined, // Import Settings icon
  UserOutlined as UserSettingOutlined, // Import User Settings icon
  BgColorsOutlined, // Import Theme icon
  LinkOutlined, // Import Social Links icon
  HomeOutlined, // Import Home icon
  LayoutOutlined, // Import Layout icon
  FormatPainterOutlined, // Import Advanced Theme icon
  TagsOutlined, // Import Tag icon
  ExperimentFilled, // Import Demo icon
  EditFilled, // Import Editor icon
  EditOutlined, // Import Edit icon
  FileFilled, // Import Template icon
  BulbFilled, // Import Test icon
  ControlOutlined, // Import Advanced Control icon
  IdcardOutlined, // Import About icon
  FolderOutlined, // Import Folder icon for template categories
  DatabaseOutlined, // Import Database icon for icon libraries
  AppstoreAddOutlined, // Import Category Add icon for icon categories
  FileImageOutlined, // Import File Image icon for icon metadata
  BarChartOutlined, // Import Bar Chart icon for icon statistics
  BookOutlined, // Import Book icon for education
  CarryOutOutlined, // Import CarryOut icon for career (替代BriefcaseOutlined)
  MenuOutlined, // Import Menu icon for navigation
  RocketOutlined, // Import Rocket icon for website versions
  HeartOutlined, // Import Heart icon for favorites
  SearchOutlined, // Import Search icon for SEO
  GlobalOutlined, // Import Global icon for SEO
  ToolOutlined, // Import Tool icon for SEO tools
  CloudServerOutlined, // Import CloudServer icon for domain config
  ExperimentOutlined, // Import Experiment icon for advanced SEO
  ApiOutlined, // Import API icon for health check
  SafetyOutlined, // Import Safety icon for permissions
  MonitorOutlined, // Import Monitor icon for system monitoring
  MessageOutlined // Import Message icon for comment management
} from '@ant-design/icons'; // 示例图标
import { useAuthStore, AuthState } from '../store/authStore'; // Import auth store and AuthState
import { message } from 'antd'; // Import message
import { usePermissions } from '../hooks/usePermissions'; // Import permissions hook
import { useAdminShortcuts } from '../hooks/useKeyboardShortcuts'; // Import admin shortcuts hook
import PermissionDebugger from '../components/PermissionDebugger';
import IconFavoritesDebugger from '../components/debug/IconFavoritesDebugger';

const { Header, Content, Footer, Sider } = Layout;

// 示例菜单项类型，后续会根据路由配置动态生成
type MenuItem = Required<React.ComponentProps<typeof Menu>>['items'][number];

function getItem(
  label: React.ReactNode,
  key: React.Key,
  icon?: React.ReactNode,
  children?: MenuItem[],
): MenuItem {
  return {
    key,
    icon,
    children,
    label,
  } as MenuItem;
}

// 动态生成菜单项的函数
const generateMenuItems = (permissions: any): MenuItem[] => {
  const items: MenuItem[] = [
    // 仪表盘
    getItem(<Link to="/dashboard">仪表盘</Link>, 'dashboard', <PieChartOutlined />),

    // 内容管理
    getItem('内容管理', 'content_group', <EditOutlined />, [
      getItem(<Link to="/articles">文章管理</Link>, 'articles', <UnorderedListOutlined />),
      getItem(<Link to="/articles/tags">标签管理</Link>, 'article-tags', <TagsOutlined />),
      getItem(<Link to="/content/templates">内容模板</Link>, 'content-templates', <FileFilled />),
      getItem(<Link to="/content/template-categories">模板分类</Link>, 'template-categories', <FolderOutlined />),
    ]),

    // 媒体管理
    getItem('媒体管理', 'media_group', <PictureOutlined />, [
      getItem(<Link to="/images">图片管理</Link>, 'images', <PictureOutlined />),
      getItem(<Link to="/gallery-manager">相册管理</Link>, 'gallery-manager', <AppstoreOutlined />),
    ]),

    // 评论管理
    getItem(<Link to="/comments">评论管理</Link>, 'comments', <MessageOutlined />),

    // 图标管理
    getItem('图标管理', 'icons_group', <AppstoreOutlined />, [
      getItem(<Link to="/icons/libraries">图标库</Link>, 'icon-libraries', <DatabaseOutlined />),
      getItem(<Link to="/icons/categories">图标分类</Link>, 'icon-categories', <AppstoreAddOutlined />),
      getItem(<Link to="/icons/favorites">收藏夹</Link>, 'icon-favorites', <HeartOutlined />),
    ]),

    // 个人信息
    getItem('个人信息', 'personal_group', <IdcardOutlined />, [
      getItem(<Link to="/my/personal-info">基本信息</Link>, 'my-personal-info', <UserSettingOutlined />),
      getItem(<Link to="/my/social-links">社交链接</Link>, 'my-social-links', <LinkOutlined />),
      getItem(<Link to="/my/about">关于我</Link>, 'my-about', <IdcardOutlined />),
    ]),

    // 站点配置
    getItem('站点配置', 'site_config_group', <SettingOutlined />, [
      getItem(<Link to="/site-settings/theme-config">主题配置</Link>, 'theme-config', <BgColorsOutlined />),
      getItem(<Link to="/site-settings/navigation">导航配置</Link>, 'site-navigation', <MenuOutlined />),
      getItem(<Link to="/site-settings/homepage-content">主页内容</Link>, 'homepage-content', <EditOutlined />),
      getItem(<Link to="/site-settings/pages-config">页面配置</Link>, 'pages-config', <FileTextOutlined />),
      getItem(<Link to="/site-settings/tech-stack-icons">技术栈图标</Link>, 'tech-stack-icons', <AppstoreOutlined />),
      getItem(<Link to="/site-settings/website-versions">网站版本</Link>, 'website-versions', <RocketOutlined />),
    ]),

    // SEO优化
    getItem('SEO优化', 'seo_group', <GlobalOutlined />, [
      getItem(<Link to="/site-settings/seo">SEO配置</Link>, 'seo-settings', <GlobalOutlined />),
      getItem(<Link to="/site-settings/seo-analysis">SEO分析</Link>, 'seo-analysis', <SearchOutlined />),
      getItem(<Link to="/site-settings/seo-tools">SEO工具</Link>, 'seo-tools', <ToolOutlined />),
      getItem(<Link to="/site-settings/domain-config">域名配置</Link>, 'domain-config', <CloudServerOutlined />),
      getItem(<Link to="/site-settings/advanced-seo">高级SEO</Link>, 'advanced-seo', <ExperimentOutlined />),
    ]),

    // 用户管理
    getItem('用户管理', 'users_group', <TeamOutlined />, [
      getItem(<Link to="/users">用户列表</Link>, 'users', <TeamOutlined />),
      getItem(<Link to="/permissions">权限管理</Link>, 'permissions', <SafetyOutlined />),
    ]),
  ];

  // 开发环境系统工具
  if (process.env.NODE_ENV === 'development') {
    items.push(
      getItem('系统工具', 'system_group', <ToolOutlined />, [
        getItem(<Link to="/system/api-health">API健康检查</Link>, 'api-health', <ApiOutlined />),
        getItem(<Link to="/system/monitor">系统监控</Link>, 'system-monitor', <MonitorOutlined />),
      ])
    );
  }

  return items;
};

const AdminLayout: React.FC = () => {
  const [collapsed, setCollapsed] = useState(false);
  const [themeSettingsOpen, setThemeSettingsOpen] = useState(false);
  const [searchOpen, setSearchOpen] = useState(false);
  const {
    token: { colorBgContainer, borderRadiusLG },
  } = theme.useToken();
  const navigate = useNavigate();
  const { clearAuth, user } = useAuthStore();
  const location = useLocation();
  const { permissions } = usePermissions();

  // 启用快捷键系统
  useAdminShortcuts({
    onSearch: () => setSearchOpen(true)
  });

  // 动态生成菜单项
  const menuItems = generateMenuItems(permissions);

  const handleLogout = () => {
    clearAuth();
    message.success('登出成功!');
    navigate('/login');
  };

  // Dropdown menu items
  const items: MenuProps['items'] = [
    {
      key: 'theme-settings',
      icon: <SettingOutlined />,
      label: '主题设置',
      onClick: () => setThemeSettingsOpen(true),
    },
    {
      type: 'divider',
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '登出',
      onClick: handleLogout,
    },
  ];

  return (
    <Layout className="admin-layout" style={{ minHeight: '100vh' }}>
      <Sider
        className="admin-sider"
        collapsible
        collapsed={collapsed}
        onCollapse={(value) => setCollapsed(value)}
        width={280}
        collapsedWidth={64}
      >
        <div className="admin-logo">
          {collapsed ? 'JY' : 'Jay-Yao Admin'}
        </div>
        <Menu
          className="admin-menu"
          theme="dark"
          defaultSelectedKeys={['dashboard']}
          mode="inline"
          items={menuItems}
        />
      </Sider>
      <Layout className="site-layout">
        <Header className="admin-header">
          <div></div>
          <Space>
            <Button
              type="text"
              icon={<SearchOutlined />}
              onClick={() => setSearchOpen(true)}
              style={{
                color: 'var(--text-secondary)',
                display: 'flex',
                alignItems: 'center',
                gap: 'var(--space-2)'
              }}
            >
              搜索
              <span style={{
                fontSize: 'var(--font-size-xs)',
                opacity: 0.7,
                background: 'var(--surface-secondary)',
                padding: '2px 6px',
                borderRadius: 'var(--radius-sm)',
                fontFamily: 'var(--font-family-mono)'
              }}>
                Ctrl+K
              </span>
            </Button>
            {/* 移除主题切换按钮 */}
            <Dropdown menu={{ items }} placement="bottomRight" arrow>
            <div className="user-dropdown">
              <Avatar
                size="small"
                icon={<UserOutlined />}
                style={{
                  background: 'var(--gradient-primary)',
                  marginRight: 'var(--space-2)'
                }}
              />
              <span style={{
                fontWeight: 'var(--font-weight-medium)',
                color: 'var(--text-primary)'
              }}>
                {user?.username ?? 'Admin'}
              </span>
            </div>
            </Dropdown>
          </Space>
        </Header>
        <Content className="admin-content">
          <Outlet />
        </Content>
        <Footer style={{
          textAlign: 'center',
          background: 'var(--surface-secondary)',
          color: 'var(--text-secondary)',
          padding: 'var(--space-4)',
          fontSize: 'var(--font-size-sm)'
        }}>
          Admin Panel ©{new Date().getFullYear()} Created by Jay-Yao
        </Footer>
      </Layout>

      {/* 主题设置面板 */}
      <ThemeSettings
        open={themeSettingsOpen}
        onClose={() => setThemeSettingsOpen(false)}
      />

      {/* 智能搜索 */}
      <SmartSearch
        open={searchOpen}
        onClose={() => setSearchOpen(false)}
      />

      {/* 调试器 - 仅在开发环境显示 */}
      {process.env.NODE_ENV === 'development' && (
        <>
          <PermissionDebugger />
          <IconFavoritesDebugger />
        </>
      )}
    </Layout>
  );
};

export default AdminLayout; 