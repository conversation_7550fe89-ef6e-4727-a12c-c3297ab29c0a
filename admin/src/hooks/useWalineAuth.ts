// Waline 认证 React Hook
import React from 'react';
import { useState, useEffect, useCallback } from 'react';
import { <PERSON>, Alert, Button, message } from 'antd';
import { walineAuthService, WalineAuthState, WalineLoginCredentials } from '../services/walineAuthService';
import { UserInfo } from '../services/walineService';

export interface UseWalineAuthReturn {
  // 认证状态
  authState: WalineAuthState;
  isAuthenticated: boolean;
  isLoading: boolean;
  
  // 用户信息
  userInfo: UserInfo | null;
  token: string | null;
  
  // 认证操作
  login: (credentials?: WalineLoginCredentials) => Promise<UserInfo>;
  logout: () => Promise<void>;
  refreshToken: () => Promise<void>;
  validateToken: () => Promise<boolean>;
  
  // 工具方法
  requireAuth: () => boolean;
  withAuth: <T extends any[]>(fn: (...args: T) => Promise<any>) => (...args: T) => Promise<any>;
}

export function useWalineAuth(): UseWalineAuthReturn {
  const [authState, setAuthState] = useState<WalineAuthState>(walineAuthService.getAuthState());
  const [isLoading, setIsLoading] = useState(false);

  // 监听认证状态变化
  useEffect(() => {
    const unsubscribe = walineAuthService.addListener((newState) => {
      setAuthState(newState);
    });

    return unsubscribe;
  }, []);

  // 登录
  const login = useCallback(async (credentials?: WalineLoginCredentials): Promise<UserInfo> => {
    setIsLoading(true);
    try {
      const userInfo = await walineAuthService.login(credentials);
      message.success('登录成功');
      return userInfo;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '登录失败';
      message.error(errorMessage);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // 登出
  const logout = useCallback(async (): Promise<void> => {
    setIsLoading(true);
    try {
      await walineAuthService.logout();
      message.success('已退出登录');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '登出失败';
      message.error(errorMessage);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // 刷新token
  const refreshToken = useCallback(async (): Promise<void> => {
    setIsLoading(true);
    try {
      await walineAuthService.refreshToken();
      message.success('Token已刷新');
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '刷新Token失败';
      message.error(errorMessage);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // 验证token
  const validateToken = useCallback(async (): Promise<boolean> => {
    try {
      return await walineAuthService.validateToken();
    } catch (error) {
      console.error('验证Token失败:', error);
      return false;
    }
  }, []);

  // 检查是否需要认证
  const requireAuth = useCallback((): boolean => {
    const isAuth = walineAuthService.isAuthenticated();
    if (!isAuth) {
      message.warning('请先登录Waline管理员账户');
    }
    return isAuth;
  }, []);

  // 包装需要认证的函数
  const withAuth = useCallback(<T extends any[]>(
    fn: (...args: T) => Promise<any>
  ) => {
    return async (...args: T) => {
      if (!requireAuth()) {
        throw new Error('需要先登录');
      }
      return await fn(...args);
    };
  }, [requireAuth]);

  return {
    // 认证状态
    authState,
    isAuthenticated: authState.isAuthenticated,
    isLoading,
    
    // 用户信息
    userInfo: authState.userInfo,
    token: authState.token,
    
    // 认证操作
    login,
    logout,
    refreshToken,
    validateToken,
    
    // 工具方法
    requireAuth,
    withAuth
  };
}

// 高阶组件：需要认证的组件包装器
import React from 'react';
import { Spin, Alert, Button } from 'antd';

interface WithAuthProps {
  fallback?: React.ReactNode;
  showLoginButton?: boolean;
  onLogin?: () => void;
}

export function withWalineAuth<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  options: WithAuthProps = {}
) {
  const WithAuthComponent: React.FC<P> = (props) => {
    const { isAuthenticated, isLoading, login } = useWalineAuth();
    const { fallback, showLoginButton = true, onLogin } = options;

    if (isLoading) {
      return (
        <div style={{ display: 'flex', justifyContent: 'center', padding: '50px' }}>
          <Spin size="large" tip="加载中..." />
        </div>
      );
    }

    if (!isAuthenticated) {
      if (fallback) {
        return <>{fallback}</>;
      }

      return (
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <Alert
            message="需要认证"
            description="请先登录Waline管理员账户以访问此功能"
            type="warning"
            showIcon
            style={{ marginBottom: '20px' }}
          />
          {showLoginButton && (
            <Button
              type="primary"
              onClick={onLogin || (() => login())}
              loading={isLoading}
            >
              立即登录
            </Button>
          )}
        </div>
      );
    }

    return <WrappedComponent {...props} />;
  };

  WithAuthComponent.displayName = `withWalineAuth(${WrappedComponent.displayName || WrappedComponent.name})`;

  return WithAuthComponent;
}

// 认证状态显示组件
export const WalineAuthStatus: React.FC<{
  showUserInfo?: boolean;
  showActions?: boolean;
}> = ({ showUserInfo = true, showActions = true }) => {
  const { isAuthenticated, userInfo, login, logout, isLoading } = useWalineAuth();

  if (!isAuthenticated) {
    return (
      <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
        <span style={{ color: '#ff4d4f' }}>未登录</span>
        {showActions && (
          <Button size="small" type="link" onClick={() => login()} loading={isLoading}>
            登录
          </Button>
        )}
      </div>
    );
  }

  return (
    <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
      <span style={{ color: '#52c41a' }}>已登录</span>
      {showUserInfo && userInfo && (
        <span style={{ color: '#666' }}>
          ({userInfo.display_name || userInfo.email || '管理员'})
        </span>
      )}
      {showActions && (
        <Button size="small" type="link" onClick={logout} loading={isLoading}>
          退出
        </Button>
      )}
    </div>
  );
};
