/*!******************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./src/styles/prism.css ***!
  \******************************************************************************************************************************************************************************************************************************************************************/
pre[class*='language-'] {
  color: #f4f4f5;
}

.token.tag,
.token.class-name,
.token.selector,
.token.selector .class,
.token.selector.class,
.token.function {
  color: #f472b6;
}

.token.attr-name,
.token.keyword,
.token.rule,
.token.pseudo-class,
.token.important {
  color: #d4d4d8;
}

.token.module {
  color: #f472b6;
}

.token.attr-value,
.token.class,
.token.string,
.token.property {
  color: #5eead4;
}

.token.punctuation,
.token.attr-equals {
  color: #71717a;
}

.token.unit,
.language-css .token.function {
  color: #bae6fd;
}

.token.comment,
.token.operator,
.token.combinator {
  color: #a1a1aa;
}

/*!*******************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./src/styles/red-dot-animations.css ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************/
/* 红点奖级别的高级动画效果 */

/* 3D透视和变换 */
.perspective-1000 {
  perspective: 1000px;
}

.preserve-3d {
  transform-style: preserve-3d;
}

.backface-hidden {
  backface-visibility: hidden;
}

/* 高级光晕动画 */
@keyframes glow-pulse {
  0%, 100% {
    opacity: 0.5;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
}

@keyframes glow-rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

@keyframes pulse-slow {
  0%, 100% {
    opacity: 0.4;
  }
  50% {
    opacity: 0.8;
  }
}

@keyframes gradient-shift {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes particle-float {
  0% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100px) rotate(180deg);
    opacity: 0;
  }
}

/* 高级阴影系统 */
.shadow-3xl {
  box-shadow: 
    0 35px 60px -12px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

.shadow-glow {
  box-shadow: 
    0 0 20px rgba(59, 130, 246, 0.3),
    0 0 40px rgba(59, 130, 246, 0.2),
    0 0 80px rgba(59, 130, 246, 0.1);
}

/* 玻璃态效果 */
.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: blur(20px);
          backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.glass-effect-dark {
  background: rgba(0, 0, 0, 0.1);
  -webkit-backdrop-filter: blur(20px);
          backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* 渐变文字效果 */
.gradient-text {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* 动态边框效果 */
.animated-border {
  position: relative;
  background: linear-gradient(45deg, transparent, transparent);
}

.animated-border::before {
  content: '';
  position: absolute;
  inset: 0;
  padding: 2px;
  background: linear-gradient(45deg, #667eea, #764ba2, #667eea);
  border-radius: inherit;
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
          mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
          mask-composite: exclude;
  animation: gradient-shift 3s ease infinite;
  background-size: 200% 200%;
}

/* 粒子效果 */
.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: radial-gradient(circle, rgba(59, 130, 246, 0.8), transparent);
  border-radius: 50%;
  animation: particle-float 4s linear infinite;
}

.particle:nth-child(1) { animation-delay: 0s; left: 10%; }
.particle:nth-child(2) { animation-delay: 0.5s; left: 20%; }
.particle:nth-child(3) { animation-delay: 1s; left: 30%; }
.particle:nth-child(4) { animation-delay: 1.5s; left: 40%; }
.particle:nth-child(5) { animation-delay: 2s; left: 50%; }
.particle:nth-child(6) { animation-delay: 2.5s; left: 60%; }
.particle:nth-child(7) { animation-delay: 3s; left: 70%; }
.particle:nth-child(8) { animation-delay: 3.5s; left: 80%; }

/* 高级hover效果 */
.card-hover-effect {
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.card-hover-effect:hover {
  transform: translateY(-8px) scale(1.02) rotateX(5deg);
  box-shadow: 
    0 25px 50px -12px rgba(0, 0, 0, 0.25),
    0 0 0 1px rgba(255, 255, 255, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
}

/* 磁性效果 */
.magnetic-effect {
  transition: transform 0.2s ease-out;
}

/* 涟漪效果 */
@keyframes ripple {
  0% {
    transform: scale(0);
    opacity: 1;
  }
  100% {
    transform: scale(4);
    opacity: 0;
  }
}

.ripple-effect {
  position: relative;
  overflow: hidden;
}

.ripple-effect::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.5);
  transform: translate(-50%, -50%);
  transition: width 0.6s, height 0.6s;
}

.ripple-effect:active::before {
  width: 300px;
  height: 300px;
}

/* 柔和的呼吸灯效果 - 减少视觉疲劳 */
@keyframes breathing-soft {
  0%, 100% {
    transform: scale(1);
    opacity: 0.9;
  }
  50% {
    transform: scale(1.02);
    opacity: 1;
  }
}

.breathing-effect {
  animation: breathing-soft 4s ease-in-out infinite;
}

/* 自定义焦点样式 */
.custom-focus {
  outline: none;
  position: relative;
}

.custom-focus:focus-within::after {
  content: '';
  position: absolute;
  inset: -3px;
  border-radius: inherit;
  background: linear-gradient(45deg, var(--primary), var(--primary-foreground));
  z-index: -1;
  opacity: 0.6;
  filter: blur(6px);
  animation: focus-pulse 2s ease-in-out infinite;
}

@keyframes focus-pulse {
  0%, 100% {
    opacity: 0.4;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.02);
  }
}

/* 流体动画 */
@keyframes fluid-1 {
  0%, 100% { transform: translate(0px, 0px) scale(1); }
  33% { transform: translate(30px, -30px) scale(1.1); }
  66% { transform: translate(-20px, 20px) scale(0.9); }
}

@keyframes fluid-2 {
  0%, 100% { transform: translate(0px, 0px) rotate(0deg); }
  50% { transform: translate(-20px, -20px) rotate(180deg); }
}

.fluid-bg-1 {
  animation: fluid-1 20s ease-in-out infinite;
}

.fluid-bg-2 {
  animation: fluid-2 15s ease-in-out infinite reverse;
}

/* 高级渐变背景 */
.premium-gradient {
  background: linear-gradient(
    135deg,
    rgba(59, 130, 246, 0.1) 0%,
    rgba(139, 92, 246, 0.1) 25%,
    rgba(6, 182, 212, 0.1) 50%,
    rgba(16, 185, 129, 0.1) 75%,
    rgba(245, 158, 11, 0.1) 100%
  );
  background-size: 200% 200%;
  animation: gradient-shift 8s ease infinite;
}

/* 工具类 */
.animate-glow-pulse {
  animation: glow-pulse 2s ease-in-out infinite;
}

.animate-pulse-slow {
  animation: pulse-slow 3s ease-in-out infinite;
}

.animate-float {
  animation: float 3s ease-in-out infinite;
}

.animate-shimmer {
  animation: shimmer 2s ease-in-out infinite;
}

/* 响应式优化 */
@media (max-width: 640px) {
  .card-hover-effect:hover {
    transform: translateY(-2px) scale(1.005);
  }

  .shadow-3xl {
    box-shadow:
      0 15px 30px -8px rgba(0, 0, 0, 0.15),
      0 0 0 1px rgba(255, 255, 255, 0.05);
  }

  /* 移动端优化动画 */
  .animate-glow-pulse,
  .animate-pulse-slow,
  .breathing-effect {
    animation-duration: 4s;
  }

  /* 移动端粒子效果优化 */
  .particle {
    width: 2px;
    height: 2px;
  }
}

@media (min-width: 641px) and (max-width: 1024px) {
  .card-hover-effect:hover {
    transform: translateY(-6px) scale(1.015);
  }
}

@media (min-width: 1025px) {
  .card-hover-effect:hover {
    transform: translateY(-8px) scale(1.02) rotateX(2deg);
  }
}

/* 暗色模式优化 */
@media (prefers-color-scheme: dark) {
  .card-hover-effect {
    box-shadow:
      0 25px 50px -12px rgba(0, 0, 0, 0.4),
      0 0 0 1px rgba(16, 185, 129, 0.2);
  }

  .shadow-3xl {
    box-shadow:
      0 35px 60px -12px rgba(0, 0, 0, 0.5),
      0 0 0 1px rgba(16, 185, 129, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.05);
  }
}

/* 绿色主题增强 */
.green-theme-glow {
  box-shadow:
    0 0 20px rgba(16, 185, 129, 0.3),
    0 0 40px rgba(16, 185, 129, 0.2),
    0 0 80px rgba(16, 185, 129, 0.1);
}

.green-theme-border {
  border: 1px solid rgba(16, 185, 129, 0.3);
}

.green-theme-bg {
  background: linear-gradient(135deg,
    rgba(16, 185, 129, 0.05) 0%,
    rgba(5, 150, 105, 0.03) 100%);
}

/* 减少动画（用户偏好） */
@media (prefers-reduced-motion: reduce) {
  .card-hover-effect,
  .animate-glow-pulse,
  .animate-pulse-slow,
  .animate-float,
  .animate-shimmer,
  .breathing-effect,
  .fluid-bg-1,
  .fluid-bg-2 {
    animation: none;
    transition: none;
  }
}

/*!*******************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./src/styles/gallery-animations.css ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************/
/* 高亮动画效果 */
@keyframes highlight-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(var(--primary-rgb), 0.7);
    transform: scale(1);
  }
  50% {
    box-shadow: 0 0 0 10px rgba(var(--primary-rgb), 0);
    transform: scale(1.03);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(var(--primary-rgb), 0);
    transform: scale(1);
  }
}

.highlight-pulse {
  animation: highlight-pulse 1.5s ease-in-out;
}

.highlight-entry .timeline-card {
  border-color: hsl(var(--primary)) !important;
  box-shadow: 0 0 15px rgba(var(--primary-rgb), 0.3) !important;
  transform: translateY(-4px) scale(1.02) !important;
}

/* 平滑过渡动画 */
@keyframes fade-slide-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-slide-up {
  animation: fade-slide-up 0.5s ease-out forwards;
}

/* 页面过渡动画 */
.page-transition-enter {
  opacity: 0;
  transform: translateY(10px);
}

.page-transition-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 0.3s, transform 0.3s;
}

.page-transition-exit {
  opacity: 1;
  transform: translateY(0);
}

.page-transition-exit-active {
  opacity: 0;
  transform: translateY(-10px);
  transition: opacity 0.3s, transform 0.3s;
}

/* 隐藏滚动条 */
.scrollbar-hide {
  -ms-overflow-style: none;  /* IE and Edge */
  scrollbar-width: none;  /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;  /* Chrome, Safari and Opera */
}

/* 传送带式滚动动画 */
@keyframes conveyor-up {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-100%);
  }
}

@keyframes conveyor-down {
  0% {
    transform: translateY(-100%);
  }
  100% {
    transform: translateY(0);
  }
}

.conveyor-scroll-up {
  animation: conveyor-up 20s linear infinite;
}

.conveyor-scroll-down {
  animation: conveyor-down 20s linear infinite;
}

/* 卡片悬停时暂停动画 */
.conveyor-container:hover .conveyor-scroll-up,
.conveyor-container:hover .conveyor-scroll-down {
  animation-play-state: paused;
}

/* 卡片网格动画 */
@keyframes card-float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-5px);
  }
}

.card-float {
  animation: card-float 3s ease-in-out infinite;
}

.card-float:nth-child(2) {
  animation-delay: 0.5s;
}

.card-float:nth-child(3) {
  animation-delay: 1s;
}

.card-float:nth-child(4) {
  animation-delay: 1.5s;
}

.card-float:nth-child(5) {
  animation-delay: 2s;
}

.card-float:nth-child(6) {
  animation-delay: 2.5s;
}

/*!*************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./src/styles/waline-fixed.css ***!
  \*************************************************************************************************************************************************************************************************************************************************************************/
/* Waline Fixed - 彻底修复版本 */

/* === 基础Waline样式（内联） === */
/* 移除导入，使用自定义样式覆盖 */

/* === 全局修复和重置 === */
.waline-container-premium {
  margin: 2rem 0;
  width: 100%;
  position: relative;
  font-family: inherit;
}

/* 确保Waline容器正确显示 */
.waline-wrapper-premium {
  width: 100%;
  max-width: none;
  background: linear-gradient(
    135deg,
    hsl(var(--background) / 0.95) 0%,
    hsl(var(--background) / 0.98) 50%,
    hsl(var(--background) / 0.95) 100%
  );
  border: 1px solid hsl(var(--border) / 0.3);
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 
    0 10px 30px -5px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(255, 255, 255, 0.05);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.waline-wrapper-premium:hover {
  transform: translateY(-2px);
  box-shadow: 
    0 20px 40px -5px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.1);
  border-color: hsl(var(--primary) / 0.3);
}

/* === 修复Waline组件布局问题 === */

/* 确保所有Waline元素正确继承样式 */
[data-waline] {
  font-size: 1rem !important;
  color: hsl(var(--foreground)) !important;
  text-align: start !important;
}

[data-waline] * {
  box-sizing: border-box !important;
  line-height: 1.6 !important;
}

/* === 修复评论输入面板 === */
[data-waline] .wl-panel {
  background: linear-gradient(
    135deg,
    hsl(var(--card) / 0.8) 0%,
    hsl(var(--card) / 0.95) 100%
  ) !important;
  border: 1px solid hsl(var(--border) / 0.3) !important;
  border-radius: 0.75rem !important;
  box-shadow: 
    0 4px 15px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
  margin: 0.5rem 0 !important;
  width: 100% !important;
}

/* 修复用户信息输入区域 - 水平布局 */
[data-waline] .wl-header {
  display: flex !important;
  flex-direction: row !important;
  flex-wrap: wrap !important;
  padding: 0 !important;
  border-bottom: 1px solid hsl(var(--border) / 0.2) !important;
  border-radius: 0.75rem 0.75rem 0 0 !important;
  background: transparent !important;
}

[data-waline] .wl-header-item {
  display: flex !important;
  flex: 1 !important;
  min-width: 150px !important;
  border-right: 1px solid hsl(var(--border) / 0.2) !important;
}

[data-waline] .wl-header-item:last-child {
  border-right: none !important;
}

[data-waline] .wl-header label {
  min-width: 60px !important;
  padding: 0.75rem 0.5rem !important;
  color: hsl(var(--muted-foreground)) !important;
  font-size: 0.875rem !important;
  text-align: center !important;
  background: hsl(var(--muted) / 0.3) !important;
  border-right: 1px solid hsl(var(--border) / 0.2) !important;
}

[data-waline] .wl-header input {
  flex: 1 !important;
  padding: 0.75rem !important;
  border: none !important;
  background: hsl(var(--background) / 0.8) !important;
  color: hsl(var(--foreground)) !important;
  font-size: 0.875rem !important;
  outline: none !important;
  transition: all 0.2s ease !important;
}

[data-waline] .wl-header input:focus {
  background: hsl(var(--background)) !important;
  box-shadow: inset 0 0 0 1px hsl(var(--primary) / 0.3) !important;
}

/* === 修复评论输入框 === */
[data-waline] .wl-editor {
  width: calc(100% - 1rem) !important;
  min-height: 120px !important;
  margin: 0.75rem 0.5rem !important;
  padding: 1rem !important;
  border: 1px solid hsl(var(--border) / 0.3) !important;
  border-radius: 0.5rem !important;
  background: hsl(var(--background) / 0.8) !important;
  color: hsl(var(--foreground)) !important;
  font-size: 0.875rem !important;
  line-height: 1.6 !important;
  resize: vertical !important;
  transition: all 0.2s ease !important;
}

[data-waline] .wl-editor:focus {
  border-color: hsl(var(--primary) / 0.5) !important;
  background: hsl(var(--background)) !important;
  box-shadow: 0 0 0 2px hsl(var(--primary) / 0.15) !important;
  outline: none !important;
}

/* === 修复预览区域 === */
[data-waline] .wl-preview {
  padding: 0.5rem !important;
  border-top: 1px solid hsl(var(--border) / 0.2) !important;
  background: hsl(var(--muted) / 0.3) !important;
  border-radius: 0 0 0.75rem 0.75rem !important;
}

[data-waline] .wl-preview h4 {
  margin: 0.5rem 0 !important;
  color: hsl(var(--foreground)) !important;
  font-size: 0.9rem !important;
  font-weight: 600 !important;
}

[data-waline] .wl-preview .wl-content {
  min-height: 2rem !important;
  padding: 0.5rem !important;
  background: hsl(var(--background)) !important;
  border: 1px solid hsl(var(--border) / 0.2) !important;
  border-radius: 0.375rem !important;
  color: hsl(var(--foreground)) !important;
}

/* === 修复底部操作区域 === */
[data-waline] .wl-footer {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  flex-wrap: wrap !important;
  margin: 0.5rem 0.75rem !important;
  padding: 0.5rem 0 !important;
  border-top: 1px solid hsl(var(--border) / 0.2) !important;
}

/* 修复操作按钮区域 - 水平布局 */
[data-waline] .wl-actions {
  display: flex !important;
  flex-direction: row !important;
  align-items: center !important;
  gap: 0.5rem !important;
  flex: 1 !important;
}

[data-waline] .wl-action {
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 2rem !important;
  height: 2rem !important;
  padding: 0 !important;
  border: 1px solid hsl(var(--border) / 0.3) !important;
  border-radius: 0.375rem !important;
  background: hsl(var(--background)) !important;
  color: hsl(var(--muted-foreground)) !important;
  font-size: 1rem !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
}

[data-waline] .wl-action:hover {
  background: hsl(var(--primary) / 0.1) !important;
  border-color: hsl(var(--primary) / 0.3) !important;
  color: hsl(var(--primary)) !important;
  transform: translateY(-1px) !important;
}

[data-waline] .wl-action.active {
  background: hsl(var(--primary)) !important;
  border-color: hsl(var(--primary)) !important;
  color: hsl(var(--primary-foreground)) !important;
}

/* === 修复表情和GIF弹窗 === */
[data-waline] .wl-emoji-popup {
  position: fixed !important;
  left: 50% !important;
  top: 50% !important;
  transform: translate(-50%, -50%) !important;
  z-index: 9999 !important;
  width: 90vw !important;
  max-width: 400px !important;
  max-height: 80vh !important;
  border: 1px solid hsl(var(--border) / 0.3) !important;
  border-radius: 1rem !important;
  background: hsl(var(--card)) !important;
  box-shadow: 
    0 20px 60px rgba(0, 0, 0, 0.2),
    0 0 0 1px rgba(255, 255, 255, 0.1) !important;
  -webkit-backdrop-filter: blur(20px) !important;
          backdrop-filter: blur(20px) !important;
  display: none !important;
  opacity: 0 !important;
  animation: none !important;
}

[data-waline] .wl-emoji-popup.display {
  display: block !important;
  opacity: 1 !important;
  animation: modalFadeIn 0.2s ease-out !important;
}

@keyframes modalFadeIn {
  from {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

/* 模态框背景遮罩 */
[data-waline] .wl-emoji-popup::before {
  content: '' !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background: rgba(0, 0, 0, 0.5) !important;
  z-index: -1 !important;
  -webkit-backdrop-filter: blur(4px) !important;
          backdrop-filter: blur(4px) !important;
}

/* 表情弹窗头部 */
[data-waline] .wl-emoji-popup .wl-popup-header {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  padding: 1rem 1rem 0 1rem !important;
  border-bottom: 1px solid hsl(var(--border) / 0.2) !important;
  margin-bottom: 0.5rem !important;
}

[data-waline] .wl-emoji-popup .wl-popup-title {
  font-size: 1rem !important;
  font-weight: 600 !important;
  color: hsl(var(--foreground)) !important;
}

[data-waline] .wl-emoji-popup .wl-popup-close {
  width: 2rem !important;
  height: 2rem !important;
  border: 1px solid hsl(var(--border) / 0.3) !important;
  border-radius: 0.5rem !important;
  background: hsl(var(--background)) !important;
  color: hsl(var(--muted-foreground)) !important;
  cursor: pointer !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  transition: all 0.2s ease !important;
  font-size: 1.2rem !important;
}

[data-waline] .wl-emoji-popup .wl-popup-close:hover {
  background: hsl(var(--destructive) / 0.1) !important;
  border-color: hsl(var(--destructive) / 0.3) !important;
  color: hsl(var(--destructive)) !important;
}

[data-waline] .wl-emoji-popup .wl-tabs {
  display: flex !important;
  flex-direction: row !important;
  overflow-x: auto !important;
  padding: 0.5rem 1rem !important;
  border-bottom: 1px solid hsl(var(--border) / 0.2) !important;
  white-space: nowrap !important;
}

[data-waline] .wl-emoji-popup .wl-tab {
  padding: 0.5rem 1rem !important;
  border-radius: 0.375rem !important;
  background: transparent !important;
  color: hsl(var(--muted-foreground)) !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  white-space: nowrap !important;
}

[data-waline] .wl-emoji-popup .wl-tab:hover {
  background: hsl(var(--muted) / 0.5) !important;
  color: hsl(var(--foreground)) !important;
}

[data-waline] .wl-emoji-popup .wl-tab.active {
  background: hsl(var(--primary)) !important;
  color: hsl(var(--primary-foreground)) !important;
}

[data-waline] .wl-emoji-popup .wl-tab-wrapper {
  max-height: 300px !important;
  overflow-y: auto !important;
  padding: 0.75rem !important;
  scrollbar-width: thin !important;
  scrollbar-color: hsl(var(--muted-foreground)) transparent !important;
}

[data-waline] .wl-emoji-popup .wl-tab-wrapper::-webkit-scrollbar {
  width: 6px !important;
}

[data-waline] .wl-emoji-popup .wl-tab-wrapper::-webkit-scrollbar-track {
  background: transparent !important;
}

[data-waline] .wl-emoji-popup .wl-tab-wrapper::-webkit-scrollbar-thumb {
  background: hsl(var(--muted-foreground) / 0.3) !important;
  border-radius: 3px !important;
}

[data-waline] .wl-emoji-popup .wl-tab-wrapper::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground) / 0.5) !important;
}

[data-waline] .wl-emoji-popup button {
  display: inline-block !important;
  width: 2rem !important;
  height: 2rem !important;
  margin: 0.125rem !important;
  padding: 0 !important;
  border: none !important;
  border-radius: 0.25rem !important;
  background: transparent !important;
  font-size: 1.2rem !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
}

[data-waline] .wl-emoji-popup button:hover {
  background: hsl(var(--muted)) !important;
  transform: scale(1.1) !important;
}

/* 修复GIF搜索弹窗 */
[data-waline] .wl-gif-popup {
  position: fixed !important;
  left: 50% !important;
  top: 50% !important;
  transform: translate(-50%, -50%) !important;
  z-index: 9999 !important;
  width: 90vw !important;
  max-width: 500px !important;
  max-height: 80vh !important;
  padding: 1.5rem !important;
  border: 1px solid hsl(var(--border) / 0.3) !important;
  border-radius: 1rem !important;
  background: hsl(var(--card)) !important;
  box-shadow: 
    0 20px 60px rgba(0, 0, 0, 0.2),
    0 0 0 1px rgba(255, 255, 255, 0.1) !important;
  -webkit-backdrop-filter: blur(20px) !important;
          backdrop-filter: blur(20px) !important;
  opacity: 0 !important;
  visibility: hidden !important;
  transition: all 0.2s ease !important;
}

[data-waline] .wl-gif-popup.display {
  opacity: 1 !important;
  visibility: visible !important;
}

[data-waline] .wl-gif-popup::before {
  content: '' !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background: rgba(0, 0, 0, 0.5) !important;
  z-index: -1 !important;
  -webkit-backdrop-filter: blur(4px) !important;
          backdrop-filter: blur(4px) !important;
}

[data-waline] .wl-gif-popup input {
  width: 100% !important;
  padding: 0.75rem !important;
  margin-bottom: 1rem !important;
  border: 1px solid hsl(var(--border) / 0.3) !important;
  border-radius: 0.5rem !important;
  background: hsl(var(--background)) !important;
  color: hsl(var(--foreground)) !important;
  font-size: 0.875rem !important;
  outline: none !important;
}

[data-waline] .wl-gif-popup input:focus {
  border-color: hsl(var(--primary) / 0.5) !important;
  box-shadow: 0 0 0 2px hsl(var(--primary) / 0.15) !important;
}

[data-waline] .wl-gif-popup .wl-gallery {
  display: grid !important;
  grid-template-columns: repeat(auto-fill, minmax(100px, 1fr)) !important;
  gap: 0.5rem !important;
  max-height: 200px !important;
  overflow-y: auto !important;
}

[data-waline] .wl-gif-popup .wl-gallery img {
  width: 100% !important;
  height: auto !important;
  border-radius: 0.375rem !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
}

[data-waline] .wl-gif-popup .wl-gallery img:hover {
  transform: scale(1.05) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
}

/* === 修复提交按钮 === */
[data-waline] .wl-info {
  display: flex !important;
  align-items: center !important;
  justify-content: flex-end !important;
  gap: 0.75rem !important;
}

[data-waline] .wl-info .wl-text-number {
  color: hsl(var(--muted-foreground)) !important;
  font-size: 0.75rem !important;
}

[data-waline] .wl-info .wl-text-number .illegal {
  color: hsl(var(--destructive)) !important;
}

[data-waline] .wl-info button {
  padding: 0.5rem 1.5rem !important;
  border: none !important;
  border-radius: 0.5rem !important;
  background: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary) / 0.9)) !important;
  color: hsl(var(--primary-foreground)) !important;
  font-size: 0.875rem !important;
  font-weight: 600 !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
}

[data-waline] .wl-info button:hover {
  background: linear-gradient(135deg, hsl(var(--primary) / 0.9), hsl(var(--primary) / 0.8)) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px hsl(var(--primary) / 0.3) !important;
}

[data-waline] .wl-info button:disabled {
  background: hsl(var(--muted)) !important;
  color: hsl(var(--muted-foreground)) !important;
  cursor: not-allowed !important;
  transform: none !important;
  box-shadow: none !important;
}

/* === 修复评论列表 === */
[data-waline] .wl-cards {
  margin-top: 2rem !important;
}

[data-waline] .wl-card-item {
  display: flex !important;
  padding: 1rem 0 !important;
  border-bottom: 1px solid hsl(var(--border) / 0.2) !important;
}

[data-waline] .wl-card-item:last-child {
  border-bottom: none !important;
}

[data-waline] .wl-card-item .wl-user {
  flex-shrink: 0 !important;
  margin-right: 1rem !important;
  display: flex !important;
  align-items: flex-start !important;
  padding-top: 0.25rem !important;
}

[data-waline] .wl-card-item .wl-user .wl-user-avatar {
  width: 3rem !important;
  height: 3rem !important;
  border-radius: 50% !important;
  border: 2px solid hsl(var(--primary) / 0.2) !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1) !important;
  transition: all 0.2s ease !important;
  -o-object-fit: cover !important;
     object-fit: cover !important;
  background: hsl(var(--muted)) !important;
}

[data-waline] .wl-card-item .wl-user .wl-user-avatar:hover {
  border-color: hsl(var(--primary) / 0.4) !important;
  transform: scale(1.05) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
}

[data-waline] .wl-card {
  flex: 1 !important;
  background: linear-gradient(135deg, hsl(var(--card) / 0.5), hsl(var(--card) / 0.8)) !important;
  border: 1px solid hsl(var(--border) / 0.2) !important;
  border-radius: 0.75rem !important;
  padding: 1rem !important;
  transition: all 0.2s ease !important;
}

[data-waline] .wl-card:hover {
  background: linear-gradient(135deg, hsl(var(--card) / 0.8), hsl(var(--card))) !important;
  border-color: hsl(var(--primary) / 0.3) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08) !important;
}

[data-waline] .wl-card .wl-head {
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
  margin-bottom: 0.75rem !important;
  padding-bottom: 0.5rem !important;
  border-bottom: 1px solid hsl(var(--border) / 0.15) !important;
}

[data-waline] .wl-card .wl-nick {
  color: hsl(var(--primary)) !important;
  font-weight: 600 !important;
  font-size: 0.875rem !important;
  text-decoration: none !important;
}

[data-waline] .wl-card .wl-time {
  color: hsl(var(--muted-foreground)) !important;
  font-size: 0.75rem !important;
}

[data-waline] .wl-card .wl-content {
  color: hsl(var(--foreground)) !important;
  line-height: 1.6 !important;
  font-size: 0.875rem !important;
  margin-bottom: 0.75rem !important;
}

[data-waline] .wl-card .wl-content p {
  margin: 0.5rem 0 !important;
}

[data-waline] .wl-card .wl-content p:first-child {
  margin-top: 0 !important;
}

[data-waline] .wl-card .wl-content p:last-child {
  margin-bottom: 0 !important;
}

/* 修复评论操作按钮 - 水平布局 */
[data-waline] .wl-card .wl-comment-actions {
  display: flex !important;
  align-items: center !important;
  gap: 0.75rem !important;
  margin-top: 0.75rem !important;
  padding-top: 0.5rem !important;
  border-top: 1px solid hsl(var(--border) / 0.1) !important;
  float: none !important;
}

[data-waline] .wl-card .wl-reply,
[data-waline] .wl-card .wl-like,
[data-waline] .wl-card .wl-edit,
[data-waline] .wl-card .wl-delete {
  display: inline-flex !important;
  align-items: center !important;
  gap: 0.25rem !important;
  padding: 0.25rem 0.5rem !important;
  border: none !important;
  border-radius: 0.25rem !important;
  background: transparent !important;
  color: hsl(var(--muted-foreground)) !important;
  font-size: 0.75rem !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
}

[data-waline] .wl-card .wl-reply:hover,
[data-waline] .wl-card .wl-like:hover,
[data-waline] .wl-card .wl-edit:hover,
[data-waline] .wl-card .wl-delete:hover {
  background: hsl(var(--primary) / 0.1) !important;
  color: hsl(var(--primary)) !important;
}

[data-waline] .wl-card .wl-like.active {
  color: hsl(var(--destructive)) !important;
}

/* === 修复反应区域（表态） - 水平布局 === */
[data-waline] .wl-reaction {
  margin: 1.5rem 0 !important;
  padding: 1rem !important;
  background: linear-gradient(135deg, hsl(var(--muted) / 0.3), hsl(var(--muted) / 0.1)) !important;
  border: 1px solid hsl(var(--border) / 0.2) !important;
  border-radius: 0.75rem !important;
  text-align: center !important;
}

[data-waline] .wl-reaction-title {
  margin: 0 0 1rem 0 !important;
  color: hsl(var(--foreground)) !important;
  font-size: 1rem !important;
  font-weight: 600 !important;
}

[data-waline] .wl-reaction-list {
  display: flex !important;
  flex-direction: row !important;
  justify-content: center !important;
  align-items: center !important;
  gap: 1rem !important;
  margin: 0 !important;
  padding: 0 !important;
  list-style: none !important;
  flex-wrap: wrap !important;
}

[data-waline] .wl-reaction-item {
  display: flex !important;
  flex-direction: column !important;
  align-items: center !important;
  gap: 0.5rem !important;
  padding: 0.75rem !important;
  border-radius: 0.5rem !important;
  background: hsl(var(--background)) !important;
  border: 1px solid hsl(var(--border) / 0.3) !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
  min-width: 60px !important;
}

[data-waline] .wl-reaction-item:hover {
  background: hsl(var(--primary) / 0.1) !important;
  border-color: hsl(var(--primary) / 0.3) !important;
  transform: translateY(-2px) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
}

[data-waline] .wl-reaction-item.active {
  background: hsl(var(--primary) / 0.15) !important;
  border-color: hsl(var(--primary)) !important;
  color: hsl(var(--primary)) !important;
}

[data-waline] .wl-reaction-img {
  width: 2rem !important;
  height: 2rem !important;
  position: relative !important;
}

[data-waline] .wl-reaction-img img {
  width: 100% !important;
  height: 100% !important;
  transition: all 0.2s ease !important;
}

[data-waline] .wl-reaction-item:hover .wl-reaction-img img,
[data-waline] .wl-reaction-item.active .wl-reaction-img img {
  transform: scale(1.1) !important;
}

[data-waline] .wl-reaction-votes {
  position: absolute !important;
  top: -4px !important;
  right: -4px !important;
  min-width: 1.2rem !important;
  height: 1.2rem !important;
  padding: 0 0.25rem !important;
  border-radius: 0.6rem !important;
  background: hsl(var(--primary)) !important;
  color: hsl(var(--primary-foreground)) !important;
  font-size: 0.6rem !important;
  font-weight: 700 !important;
  line-height: 1.2rem !important;
  text-align: center !important;
  border: 1px solid hsl(var(--background)) !important;
}

[data-waline] .wl-reaction-text {
  color: hsl(var(--muted-foreground)) !important;
  font-size: 0.75rem !important;
  font-weight: 500 !important;
}

[data-waline] .wl-reaction-item.active .wl-reaction-text {
  color: hsl(var(--primary)) !important;
}

/* === 修复分页和加载 === */
[data-waline] .wl-operation {
  text-align: center !important;
  margin: 2rem 0 !important;
}

[data-waline] .wl-operation button {
  margin: 0.5rem !important;
  padding: 0.5rem 1rem !important;
  border: 1px solid hsl(var(--border)) !important;
  border-radius: 0.5rem !important;
  background: hsl(var(--background)) !important;
  color: hsl(var(--foreground)) !important;
  cursor: pointer !important;
  transition: all 0.2s ease !important;
}

[data-waline] .wl-operation button:hover {
  background: hsl(var(--primary)) !important;
  color: hsl(var(--primary-foreground)) !important;
  border-color: hsl(var(--primary)) !important;
}

/* 修复空状态 */
[data-waline] .wl-empty {
  text-align: center !important;
  padding: 3rem 1rem !important;
  color: hsl(var(--muted-foreground)) !important;
  font-size: 0.875rem !important;
}

/* 修复加载状态 */
[data-waline] .wl-loading {
  text-align: center !important;
  padding: 2rem !important;
  color: hsl(var(--muted-foreground)) !important;
}

/* === 响应式修复 === */
@media (max-width: 768px) {
  [data-waline] .wl-header {
    flex-direction: column !important;
  }
  
  [data-waline] .wl-header-item {
    min-width: unset !important;
    border-right: none !important;
    border-bottom: 1px solid hsl(var(--border) / 0.2) !important;
  }
  
  [data-waline] .wl-header-item:last-child {
    border-bottom: none !important;
  }
  
  [data-waline] .wl-reaction-list {
    gap: 0.75rem !important;
  }
  
  [data-waline] .wl-reaction-item {
    min-width: 50px !important;
    padding: 0.5rem !important;
  }
  
  [data-waline] .wl-reaction-img {
    width: 1.5rem !important;
    height: 1.5rem !important;
  }
  
  [data-waline] .wl-footer {
    flex-direction: column !important;
    align-items: stretch !important;
    gap: 1rem !important;
  }
  
  [data-waline] .wl-actions {
    justify-content: center !important;
  }
  
  [data-waline] .wl-info {
    justify-content: center !important;
  }
}

/* === 自定义CSS变量覆盖 === */
[data-waline] {
  --waline-font-size: 1rem;
  --waline-white: hsl(var(--background));
  --waline-light-grey: hsl(var(--muted-foreground));
  --waline-dark-grey: hsl(var(--foreground));
  --waline-theme-color: hsl(var(--primary));
  --waline-active-color: hsl(var(--primary) / 0.8);
  --waline-color: hsl(var(--foreground));
  --waline-bg-color: hsl(var(--background));
  --waline-bg-color-light: hsl(var(--muted));
  --waline-bg-color-hover: hsl(var(--muted) / 0.8);
  --waline-border-color: hsl(var(--border));
  --waline-disable-bg-color: hsl(var(--muted));
  --waline-disable-color: hsl(var(--muted-foreground));
  --waline-avatar-size: 2.5rem;
  --waline-m-avatar-size: 2rem;
  --waline-badge-color: hsl(var(--primary));
  --waline-info-color: hsl(var(--muted-foreground));
  --waline-info-bg-color: hsl(var(--muted));
}

/* === 强制修复任何残留的竖向布局 === */
[data-waline] .wl-header,
[data-waline] .wl-actions,
[data-waline] .wl-reaction-list,
[data-waline] .wl-card .wl-comment-actions {
  display: flex !important;
  flex-direction: row !important;
}

/* 确保所有弹窗正确定位 */
[data-waline] .wl-emoji-popup,
[data-waline] .wl-gif-popup {
  position: fixed !important;
  z-index: 9999 !important;
}

/* 确保所有输入框和按钮可点击 */
[data-waline] input,
[data-waline] textarea,
[data-waline] button {
  pointer-events: auto !important;
  touch-action: manipulation !important;
}

/* 防止文字选择干扰交互 */
[data-waline] .wl-action,
[data-waline] .wl-reaction-item,
[data-waline] button {
  -moz-user-select: none !important;
       user-select: none !important;
  -webkit-user-select: none !important;
}

/* === 额外的美化样式 === */

/* 美化评论卡片的阴影效果 */
[data-waline] .wl-card {
  position: relative !important;
  overflow: visible !important;
}

[data-waline] .wl-card::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  height: 2px !important;
  background: linear-gradient(90deg, 
    transparent 0%, 
    hsl(var(--primary) / 0.3) 50%, 
    transparent 100%) !important;
  border-radius: 0.75rem 0.75rem 0 0 !important;
}

/* 美化输入框焦点效果 */
[data-waline] .wl-editor:focus {
  position: relative !important;
}

[data-waline] .wl-editor:focus::before {
  content: '' !important;
  position: absolute !important;
  top: -2px !important;
  left: -2px !important;
  right: -2px !important;
  bottom: -2px !important;
  background: linear-gradient(45deg, 
    hsl(var(--primary) / 0.3), 
    hsl(var(--primary) / 0.1), 
    hsl(var(--primary) / 0.3)) !important;
  border-radius: 0.75rem !important;
  z-index: -1 !important;
  animation: focusGlow 2s ease-in-out infinite alternate !important;
}

@keyframes focusGlow {
  from {
    opacity: 0.3;
  }
  to {
    opacity: 0.7;
  }
}

/* 美化用户等级标识 */
[data-waline] .wl-card .wl-badge {
  background: linear-gradient(135deg, 
    hsl(var(--primary) / 0.8), 
    hsl(var(--primary) / 0.6)) !important;
  color: hsl(var(--primary-foreground)) !important;
  padding: 0.25rem 0.5rem !important;
  border-radius: 0.25rem !important;
  font-size: 0.7rem !important;
  font-weight: 600 !important;
  margin-left: 0.5rem !important;
  box-shadow: 0 2px 4px hsl(var(--primary) / 0.2) !important;
}

/* 美化时间显示 */
[data-waline] .wl-card .wl-time {
  position: relative !important;
  padding: 0.25rem 0.5rem !important;
  background: hsl(var(--muted) / 0.3) !important;
  border-radius: 0.25rem !important;
  font-weight: 500 !important;
}

/* 美化回复嵌套显示 */
[data-waline] .wl-card .wl-quote {
  background: linear-gradient(135deg, 
    hsl(var(--muted) / 0.5), 
    hsl(var(--muted) / 0.3)) !important;
  border-left: 3px solid hsl(var(--primary) / 0.5) !important;
  padding: 0.75rem !important;
  margin: 0.5rem 0 !important;
  border-radius: 0 0.5rem 0.5rem 0 !important;
  font-size: 0.85rem !important;
  color: hsl(var(--muted-foreground)) !important;
}

/* 美化表情按钮悬停效果 */
[data-waline] .wl-emoji-popup button {
  position: relative !important;
  overflow: hidden !important;
}

[data-waline] .wl-emoji-popup button::before {
  content: '' !important;
  position: absolute !important;
  inset: 0 !important;
  background: radial-gradient(circle, 
    hsl(var(--primary) / 0.2) 0%, 
    transparent 70%) !important;
  opacity: 0 !important;
  transition: opacity 0.2s ease !important;
}

[data-waline] .wl-emoji-popup button:hover::before {
  opacity: 1 !important;
}

/* 美化滚动条 */
[data-waline] *::-webkit-scrollbar {
  width: 8px !important;
  height: 8px !important;
}

[data-waline] *::-webkit-scrollbar-track {
  background: hsl(var(--muted) / 0.3) !important;
  border-radius: 4px !important;
}

[data-waline] *::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, 
    hsl(var(--primary) / 0.5), 
    hsl(var(--primary) / 0.3)) !important;
  border-radius: 4px !important;
  border: 1px solid hsl(var(--background)) !important;
}

[data-waline] *::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, 
    hsl(var(--primary) / 0.7), 
    hsl(var(--primary) / 0.5)) !important;
}

/* 美化加载动画 */
[data-waline] .wl-loading::after {
  content: '' !important;
  display: inline-block !important;
  width: 1rem !important;
  height: 1rem !important;
  margin-left: 0.5rem !important;
  border: 2px solid hsl(var(--muted-foreground) / 0.3) !important;
  border-top: 2px solid hsl(var(--primary)) !important;
  border-radius: 50% !important;
  animation: spin 1s linear infinite !important;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
/*!***************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./src/styles/waline-premium.css ***!
  \***************************************************************************************************************************************************************************************************************************************************************************/
/* Waline Premium Design - Red Dot Award Level */

/* 主容器 */
.waline-container-premium {
  margin: 2rem 0;
  width: 100%;
  position: relative;
}

/* Waline包装器 */
.waline-wrapper-premium {
  width: 100%;
  max-width: none;
  background: linear-gradient(
    135deg,
    hsl(var(--background) / 0.95) 0%,
    hsl(var(--background) / 0.98) 50%,
    hsl(var(--background) / 0.95) 100%
  );
  border: 1px solid hsl(var(--border) / 0.3);
  border-radius: 1.5rem;
  padding: 2rem;
  box-shadow:
    0 20px 40px -12px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(255, 255, 255, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  -webkit-backdrop-filter: blur(10px) saturate(1.1);
          backdrop-filter: blur(10px) saturate(1.1);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

/* === 修复用户登录状态显示问题 === */
/* 只隐藏输入框上方突兀的用户头像显示，保留评论列表中的头像 */
.waline-wrapper-premium .wl-panel .wl-user-info,
.waline-wrapper-premium .wl-panel .wl-user-avatar {
  display: none !important;
}

/* 隐藏登录后在输入框上方显示的用户信息，但保留评论中的用户信息 */
.waline-wrapper-premium .wl-panel > .wl-user,
.waline-wrapper-premium .wl-panel > .wl-login-info {
  display: none !important;
}

/* 确保评论列表中的头像正常显示 */
.waline-wrapper-premium .wl-card .wl-avatar,
.waline-wrapper-premium .wl-cards .wl-avatar {
  display: block !important;
}

/* 优化用户信息在输入框内的显示 */
.waline-wrapper-premium .wl-header .wl-user-meta,
.waline-wrapper-premium .wl-login-status {
  background: linear-gradient(135deg,
    hsl(var(--primary) / 0.08) 0%,
    hsl(var(--primary) / 0.04) 100%) !important;
  border: 1px solid hsl(var(--primary) / 0.2) !important;
  border-radius: 0.5rem !important;
  padding: 0.5rem 0.75rem !important;
  font-size: 0.8rem !important;
  color: hsl(var(--primary)) !important;
  font-weight: 500 !important;
  display: inline-flex !important;
  align-items: center !important;
  gap: 0.5rem !important;
  margin-bottom: 0.75rem !important;
}

/* 确保登录按钮样式优化 */
.waline-wrapper-premium .wl-login-btn,
.waline-wrapper-premium .wl-logout-btn {
  background: linear-gradient(135deg,
    hsl(var(--primary) / 0.1) 0%,
    hsl(var(--primary) / 0.05) 100%) !important;
  border: 1px solid hsl(var(--primary) / 0.3) !important;
  color: hsl(var(--primary)) !important;
  border-radius: 0.5rem !important;
  padding: 0.5rem 1rem !important;
  font-size: 0.8rem !important;
  font-weight: 500 !important;
  transition: all 0.25s ease !important;
}

.waline-wrapper-premium .wl-login-btn:hover,
.waline-wrapper-premium .wl-logout-btn:hover {
  background: linear-gradient(135deg,
    hsl(var(--primary) / 0.15) 0%,
    hsl(var(--primary) / 0.1) 100%) !important;
  border-color: hsl(var(--primary) / 0.5) !important;
  transform: translateY(-1px) !important;
}

.waline-wrapper-premium::before {
  content: '';
  position: absolute;
  top: 0;
  left: 20%;
  right: 20%;
  height: 1px;
  background: linear-gradient(90deg, transparent, hsl(var(--primary) / 0.4), transparent);
  opacity: 0.8;
}

.waline-wrapper-premium:hover {
  transform: translateY(-2px);
  box-shadow: 
    0 32px 64px -12px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.15),
    0 0 80px hsl(var(--primary) / 0.08);
  border-color: hsl(var(--primary) / 0.2);
}

/* 重置任何默认样式 */
.waline-wrapper-premium * {
  box-sizing: border-box;
}

/* === 核心Waline组件样式 === */

/* 主要容器 */
.waline-wrapper-premium .wl-container {
  display: block !important;
  position: relative !important;
  width: 100% !important;
}

/* 输入面板 */
.waline-wrapper-premium .wl-panel {
  background: linear-gradient(
    135deg,
    hsl(var(--card) / 0.6) 0%,
    hsl(var(--card) / 0.8) 100%
  ) !important;
  border: 1px solid hsl(var(--border) / 0.2) !important;
  border-radius: 1rem !important;
  padding: 1.5rem !important;
  margin-bottom: 1.5rem !important;
  -webkit-backdrop-filter: blur(8px) !important;
          backdrop-filter: blur(8px) !important;
  box-shadow: 
    0 4px 20px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  position: relative !important;
}

.waline-wrapper-premium .wl-panel:hover {
  transform: translateY(-1px) !important;
  border-color: hsl(var(--primary) / 0.3) !important;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.15) !important;
}

/* 用户信息输入区 */
.waline-wrapper-premium .wl-header {
  display: flex !important;
  flex-wrap: wrap !important;
  gap: 0.75rem !important;
  margin-bottom: 1rem !important;
}

.waline-wrapper-premium .wl-header .wl-input {
  flex: 1 !important;
  min-width: 120px !important;
  background: hsl(var(--background) / 0.8) !important;
  border: 1px solid hsl(var(--border) / 0.3) !important;
  border-radius: 0.5rem !important;
  padding: 0.75rem 1rem !important;
  font-size: 0.875rem !important;
  color: hsl(var(--foreground)) !important;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
  -webkit-backdrop-filter: blur(4px) !important;
          backdrop-filter: blur(4px) !important;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05) !important;
  pointer-events: auto !important;
  -webkit-user-select: auto !important;
     -moz-user-select: auto !important;
          user-select: auto !important;
  position: relative !important;
  z-index: 1 !important;
}

.waline-wrapper-premium .wl-header .wl-input:focus {
  border-color: hsl(var(--primary) / 0.5) !important;
  box-shadow: 
    inset 0 1px 2px rgba(0, 0, 0, 0.05),
    0 0 0 2px hsl(var(--primary) / 0.15) !important;
  outline: none !important;
  transform: translateY(-1px) !important;
  background: hsl(var(--background)) !important;
}

.waline-wrapper-premium .wl-header .wl-input::-moz-placeholder {
  color: hsl(var(--muted-foreground)) !important;
  opacity: 0.7 !important;
}

.waline-wrapper-premium .wl-header .wl-input::placeholder {
  color: hsl(var(--muted-foreground)) !important;
  opacity: 0.7 !important;
}

/* 评论输入框 */
.waline-wrapper-premium .wl-editor {
  background: hsl(var(--background) / 0.6) !important;
  border: 1px solid hsl(var(--border) / 0.3) !important;
  border-radius: 0.75rem !important;
  padding: 1rem !important;
  font-size: 0.9rem !important;
  line-height: 1.6 !important;
  color: hsl(var(--foreground)) !important;
  min-height: 120px !important;
  resize: vertical !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  -webkit-backdrop-filter: blur(6px) !important;
          backdrop-filter: blur(6px) !important;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.06) !important;
  pointer-events: auto !important;
  -webkit-user-select: auto !important;
     -moz-user-select: auto !important;
          user-select: auto !important;
  position: relative !important;
  z-index: 1 !important;
}

.waline-wrapper-premium .wl-editor:focus {
  border-color: hsl(var(--primary) / 0.4) !important;
  box-shadow: 
    inset 0 2px 4px rgba(0, 0, 0, 0.06),
    0 0 0 3px hsl(var(--primary) / 0.12),
    0 4px 20px rgba(0, 0, 0, 0.1) !important;
  outline: none !important;
  transform: translateY(-1px) !important;
  background: hsl(var(--background) / 0.8) !important;
}

.waline-wrapper-premium .wl-editor::-moz-placeholder {
  color: hsl(var(--muted-foreground)) !important;
  opacity: 0.8 !important;
}

.waline-wrapper-premium .wl-editor::placeholder {
  color: hsl(var(--muted-foreground)) !important;
  opacity: 0.8 !important;
}

/* 操作区域 */
.waline-wrapper-premium .wl-footer {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  flex-wrap: wrap !important;
  gap: 0.75rem !important;
  margin-top: 1rem !important;
}

/* === 工具栏按钮重新设计 === */
.waline-wrapper-premium .wl-actions {
  display: flex !important;
  align-items: center !important;
  gap: 0.5rem !important;
}

/* === 工具栏按钮优化 - Red Dot Award Design === */
.waline-wrapper-premium .wl-action {
  position: relative !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 42px !important;
  height: 42px !important;
  border-radius: 0.75rem !important;
  background: linear-gradient(135deg,
    hsl(var(--card) / 0.9) 0%,
    hsl(var(--card) / 0.95) 100%) !important;
  border: 1px solid hsl(var(--border) / 0.2) !important;
  color: hsl(var(--muted-foreground)) !important;
  cursor: pointer !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  -webkit-backdrop-filter: blur(12px) !important;
          backdrop-filter: blur(12px) !important;
  box-shadow:
    0 3px 12px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.15) !important;
  overflow: hidden !important;
  font-size: 18px !important;
  pointer-events: auto !important;
  z-index: 2 !important;
}

.waline-wrapper-premium .wl-action::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: 0.75rem;
  background: linear-gradient(135deg,
    hsl(var(--primary) / 0.15) 0%,
    hsl(var(--primary) / 0.08) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.waline-wrapper-premium .wl-action:hover::before {
  opacity: 1;
}

.waline-wrapper-premium .wl-action:hover {
  transform: translateY(-2px) scale(1.08) !important;
  border-color: hsl(var(--primary) / 0.4) !important;
  color: hsl(var(--primary)) !important;
  box-shadow:
    0 8px 24px rgba(0, 0, 0, 0.12),
    inset 0 1px 0 rgba(255, 255, 255, 0.25) !important;
  background: linear-gradient(135deg,
    hsl(var(--primary) / 0.12) 0%,
    hsl(var(--primary) / 0.08) 100%) !important;
}

.waline-wrapper-premium .wl-action:active {
  transform: translateY(0) scale(0.96) !important;
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.08) !important;
}

/* === 特殊按钮样式优化 === */
/* 文件上传按钮 */
.waline-wrapper-premium .wl-action[title*="上传图片"],
.waline-wrapper-premium .wl-action[title*="Upload"],
.waline-wrapper-premium .wl-action[title*="选择文件"] {
  background: linear-gradient(135deg,
    hsl(210, 60%, 95%) 0%,
    hsl(210, 60%, 98%) 100%) !important;
  border-color: hsl(210, 40%, 85%) !important;
  color: hsl(210, 70%, 45%) !important;
}

.waline-wrapper-premium .wl-action[title*="上传图片"]:hover,
.waline-wrapper-premium .wl-action[title*="Upload"]:hover,
.waline-wrapper-premium .wl-action[title*="选择文件"]:hover {
  background: linear-gradient(135deg,
    hsl(210, 80%, 88%) 0%,
    hsl(210, 80%, 94%) 100%) !important;
  color: hsl(210, 90%, 35%) !important;
  border-color: hsl(210, 70%, 65%) !important;
  box-shadow:
    0 8px 24px hsl(210, 60%, 85%),
    inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
}

/* 表情按钮 */
.waline-wrapper-premium .wl-action[title*="表情"],
.waline-wrapper-premium .wl-action[title*="Emoji"] {
  background: linear-gradient(135deg,
    hsl(45, 85%, 94%) 0%,
    hsl(45, 85%, 97%) 100%) !important;
  border-color: hsl(45, 50%, 80%) !important;
  color: hsl(45, 80%, 40%) !important;
}

.waline-wrapper-premium .wl-action[title*="表情"]:hover,
.waline-wrapper-premium .wl-action[title*="Emoji"]:hover {
  background: linear-gradient(135deg,
    hsl(45, 95%, 85%) 0%,
    hsl(45, 95%, 92%) 100%) !important;
  color: hsl(45, 90%, 30%) !important;
  border-color: hsl(45, 70%, 60%) !important;
  box-shadow:
    0 8px 24px hsl(45, 60%, 80%),
    inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
}

/* 隐藏的文件输入 */
.waline-wrapper-premium input[type="file"] {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
  opacity: 0 !important;
  pointer-events: none !important;
}

/* 文件拖拽区域 */
.waline-wrapper-premium .wl-panel.wl-drop-active {
  border-color: hsl(var(--primary) / 0.5) !important;
  background: linear-gradient(135deg, 
    hsl(var(--primary) / 0.05) 0%, 
    hsl(var(--primary) / 0.02) 100%) !important;
  box-shadow: 
    0 8px 32px hsl(var(--primary) / 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.15) !important;
}

.waline-wrapper-premium .wl-panel.wl-drop-active::after {
  content: '拖拽文件到此处上传';
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: hsl(var(--primary) / 0.05);
  border-radius: 1rem;
  color: hsl(var(--primary));
  font-weight: 600;
  font-size: 1.1rem;
  -webkit-backdrop-filter: blur(12px);
          backdrop-filter: blur(12px);
  z-index: 10;
}

/* 提交按钮 */
.waline-wrapper-premium .wl-btn {
  background: linear-gradient(
    135deg,
    hsl(var(--primary)) 0%,
    hsl(var(--primary) / 0.9) 100%
  ) !important;
  color: hsl(var(--primary-foreground)) !important;
  border: none !important;
  border-radius: 0.5rem !important;
  padding: 0.75rem 1.5rem !important;
  font-size: 0.875rem !important;
  font-weight: 600 !important;
  letter-spacing: 0.025em !important;
  cursor: pointer !important;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1) !important;
  box-shadow: 
    0 2px 8px hsl(var(--primary) / 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
  position: relative !important;
  overflow: hidden !important;
  pointer-events: auto !important;
  z-index: 2 !important;
}

.waline-wrapper-premium .wl-btn::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, transparent 50%);
  opacity: 0;
  transition: opacity 0.25s ease;
}

.waline-wrapper-premium .wl-btn:hover::before {
  opacity: 1;
}

.waline-wrapper-premium .wl-btn:hover {
  transform: translateY(-2px) scale(1.02) !important;
  box-shadow: 
    0 6px 20px hsl(var(--primary) / 0.35),
    inset 0 1px 0 rgba(255, 255, 255, 0.25) !important;
}

.waline-wrapper-premium .wl-btn:active {
  transform: translateY(0) scale(0.98) !important;
  box-shadow: 
    0 1px 4px hsl(var(--primary) / 0.3) !important;
}

.waline-wrapper-premium .wl-btn:disabled {
  opacity: 0.5 !important;
  cursor: not-allowed !important;
  transform: none !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

/* 次要按钮 */
.waline-wrapper-premium .wl-btn.wl-secondary-btn {
  background: linear-gradient(
    135deg,
    hsl(var(--secondary) / 0.1) 0%,
    hsl(var(--secondary) / 0.05) 100%
  ) !important;
  color: hsl(var(--secondary-foreground)) !important;
  border: 1px solid hsl(var(--border) / 0.3) !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05) !important;
}

.waline-wrapper-premium .wl-btn.wl-secondary-btn:hover {
  background: linear-gradient(
    135deg,
    hsl(var(--secondary) / 0.15) 0%,
    hsl(var(--secondary) / 0.1) 100%
  ) !important;
  border-color: hsl(var(--primary) / 0.3) !important;
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.1) !important;
}

/* === 评论列表样式 === */

.waline-wrapper-premium .wl-cards {
  margin-top: 2rem !important;
}

.waline-wrapper-premium .wl-card {
  background: linear-gradient(
    135deg,
    hsl(var(--card) / 0.5) 0%,
    hsl(var(--card) / 0.8) 100%
  ) !important;
  border: 1px solid hsl(var(--border) / 0.2) !important;
  border-radius: 1rem !important;
  padding: 1.5rem !important;
  margin-bottom: 1rem !important;
  -webkit-backdrop-filter: blur(8px) !important;
          backdrop-filter: blur(8px) !important;
  box-shadow: 
    0 2px 16px rgba(0, 0, 0, 0.04),
    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  position: relative !important;
  overflow: hidden !important;
}

.waline-wrapper-premium .wl-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, hsl(var(--primary) / 0.2), transparent);
}

.waline-wrapper-premium .wl-card:hover {
  transform: translateY(-1px) !important;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.15) !important;
  border-color: hsl(var(--primary) / 0.25) !important;
}

/* === 头像系统重新设计 === */
.waline-wrapper-premium .wl-card .wl-head {
  display: flex !important;
  align-items: flex-start !important;
  gap: 1rem !important;
  margin-bottom: 1.25rem !important;
  padding-bottom: 0.75rem !important;
  border-bottom: 1px solid hsl(var(--border) / 0.15) !important;
  position: relative !important;
}

/* 头像容器 - 红点奖级设计 */
.waline-wrapper-premium .wl-avatar {
  position: relative !important;
  width: 48px !important;
  height: 48px !important;
  border-radius: 50% !important;
  background: linear-gradient(135deg,
    hsl(var(--primary) / 0.1) 0%,
    hsl(var(--primary) / 0.05) 100%) !important;
  padding: 2px !important;
  flex-shrink: 0 !important;
  overflow: hidden !important;
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
  transition: all 0.35s cubic-bezier(0.4, 0, 0.2, 1) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* 默认头像样式 - 作为随机头像的后备方案 */
.waline-wrapper-premium .wl-avatar:empty::before,
.waline-wrapper-premium .wl-avatar[data-no-avatar="true"]::before {
  content: '👤' !important;
  font-size: 20px !important;
  color: hsl(var(--primary)) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 100% !important;
  height: 100% !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  z-index: 1 !important;
}

/* 随机头像加载状态 */
.waline-wrapper-premium .wl-avatar img[src*="dicebear"] {
  background: linear-gradient(135deg,
    hsl(var(--primary) / 0.1) 0%,
    hsl(var(--primary) / 0.05) 100%) !important;
  transition: all 0.35s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* 随机头像悬停效果增强 */
.waline-wrapper-premium .wl-card:hover .wl-avatar img[src*="dicebear"] {
  transform: scale(1.05) rotate(2deg) !important;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
}

.waline-wrapper-premium .wl-avatar::before {
  content: '';
  position: absolute;
  inset: -2px;
  border-radius: 50%;
  background: linear-gradient(135deg, 
    hsl(var(--primary) / 0.3) 0%,
    transparent 30%,
    transparent 70%,
    hsl(var(--primary) / 0.3) 100%);
  z-index: -1;
  opacity: 0;
  transition: opacity 0.35s ease;
}

.waline-wrapper-premium .wl-card:hover .wl-avatar::before {
  opacity: 1;
}

.waline-wrapper-premium .wl-avatar img {
  width: calc(100% - 4px) !important;
  height: calc(100% - 4px) !important;
  border-radius: 50% !important;
  -o-object-fit: cover !important;
     object-fit: cover !important;
  transition: all 0.35s cubic-bezier(0.4, 0, 0.2, 1) !important;
  position: absolute !important;
  top: 2px !important;
  left: 2px !important;
  z-index: 1 !important;
}

/* 确保有头像图片时隐藏默认头像 */
.waline-wrapper-premium .wl-avatar:has(img)::before {
  display: none !important;
}

/* 为没有头像的用户提供更好的默认显示 */
.waline-wrapper-premium .wl-avatar[data-no-avatar="true"]::before {
  content: '👤' !important;
  font-size: 20px !important;
  color: hsl(var(--primary)) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 100% !important;
  height: 100% !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  z-index: 1 !important;
}

/* 兼容性：如果浏览器不支持:has()选择器 */
.waline-wrapper-premium .wl-avatar:not([data-no-avatar]):empty::before {
  content: '👤' !important;
  font-size: 20px !important;
  color: hsl(var(--primary)) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 100% !important;
  height: 100% !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  z-index: 1 !important;
}

/* 确保头像容器在所有情况下都正确显示 */
.waline-wrapper-premium .wl-card .wl-avatar,
.waline-wrapper-premium .wl-cards .wl-avatar,
.waline-wrapper-premium [data-waline] .wl-avatar {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  position: relative !important;
  width: 48px !important;
  height: 48px !important;
  border-radius: 50% !important;
  background: linear-gradient(135deg,
    hsl(var(--primary) / 0.1) 0%,
    hsl(var(--primary) / 0.05) 100%) !important;
  flex-shrink: 0 !important;
  overflow: visible !important;
}

/* 覆盖可能的冲突样式 */
.waline-wrapper-premium [data-waline] .wl-card-item .wl-user .wl-user-avatar {
  width: 48px !important;
  height: 48px !important;
  border: 2px solid hsl(var(--primary) / 0.2) !important;
  background: linear-gradient(135deg,
    hsl(var(--primary) / 0.1) 0%,
    hsl(var(--primary) / 0.05) 100%) !important;
}

.waline-wrapper-premium .wl-card:hover .wl-avatar {
  transform: scale(1.05) !important;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
}

/* 用户信息区域 */
.waline-wrapper-premium .wl-card .wl-meta {
  flex: 1 !important;
  min-width: 0 !important;
}

.waline-wrapper-premium .wl-card .wl-author {
  display: flex !important;
  align-items: center !important;
  gap: 0.5rem !important;
  margin-bottom: 0.25rem !important;
}

.waline-wrapper-premium .wl-card .wl-nick {
  color: hsl(var(--primary)) !important;
  font-weight: 700 !important;
  font-size: 0.9rem !important;
  letter-spacing: 0.025em !important;
  transition: all 0.2s ease !important;
}

.waline-wrapper-premium .wl-card:hover .wl-nick {
  color: hsl(var(--primary) / 0.8) !important;
  transform: translateX(2px) !important;
}

.waline-wrapper-premium .wl-card .wl-time {
  color: hsl(var(--muted-foreground)) !important;
  font-size: 0.75rem !important;
  font-weight: 500 !important;
  opacity: 0.8 !important;
}

/* 评论内容 */
.waline-wrapper-premium .wl-card .wl-content {
  color: hsl(var(--foreground)) !important;
  line-height: 1.7 !important;
  font-size: 0.9rem !important;
  letter-spacing: 0.01em !important;
  transition: color 0.2s ease !important;
}

.waline-wrapper-premium .wl-card:hover .wl-content {
  color: hsl(var(--foreground) / 0.9) !important;
}

.waline-wrapper-premium .wl-card .wl-content p {
  margin: 0.75rem 0 !important;
}

.waline-wrapper-premium .wl-card .wl-content p:first-child {
  margin-top: 0 !important;
}

.waline-wrapper-premium .wl-card .wl-content p:last-child {
  margin-bottom: 0 !important;
}

.waline-wrapper-premium .wl-card .wl-content code {
  background: hsl(var(--muted)) !important;
  color: hsl(var(--foreground)) !important;
  padding: 0.2rem 0.4rem !important;
  border-radius: 0.25rem !important;
  font-size: 0.8rem !important;
  font-weight: 500 !important;
}

.waline-wrapper-premium .wl-card .wl-content pre {
  background: hsl(var(--muted)) !important;
  color: hsl(var(--foreground)) !important;
  padding: 1rem !important;
  border-radius: 0.5rem !important;
  overflow-x: auto !important;
  margin: 1rem 0 !important;
  border: 1px solid hsl(var(--border) / 0.3) !important;
}

/* 评论操作 */
.waline-wrapper-premium .wl-card .wl-action {
  display: flex !important;
  align-items: center !important;
  gap: 1rem !important;
  margin-top: 1rem !important;
  padding-top: 0.75rem !important;
  border-top: 1px solid hsl(var(--border) / 0.1) !important;
}

.waline-wrapper-premium .wl-card .wl-action button {
  color: hsl(var(--muted-foreground)) !important;
  font-size: 0.75rem !important;
  font-weight: 500 !important;
  padding: 0.4rem 0.8rem !important;
  border-radius: 0.375rem !important;
  border: none !important;
  background: none !important;
  cursor: pointer !important;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
  pointer-events: auto !important;
}

.waline-wrapper-premium .wl-card .wl-action button:hover {
  color: hsl(var(--primary)) !important;
  background: hsl(var(--primary) / 0.08) !important;
  transform: translateY(-1px) !important;
}

/* === 整体布局细节优化 === */

/* 评论区标题和统计 */
.waline-wrapper-premium .wl-comment-title {
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
  margin-bottom: 1.5rem !important;
  padding-bottom: 1rem !important;
  border-bottom: 1px solid hsl(var(--border) / 0.1) !important;
}

.waline-wrapper-premium .wl-comment-title h3 {
  font-size: 1.25rem !important;
  font-weight: 700 !important;
  color: hsl(var(--foreground)) !important;
  margin: 0 !important;
  display: flex !important;
  align-items: center !important;
  gap: 0.5rem !important;
}

.waline-wrapper-premium .wl-comment-title h3::before {
  content: '💬';
  font-size: 1.1rem;
}

.waline-wrapper-premium .wl-stats {
  display: flex !important;
  align-items: center !important;
  gap: 1rem !important;
  font-size: 0.8rem !important;
  color: hsl(var(--muted-foreground)) !important;
}

.waline-wrapper-premium .wl-stats .wl-stat {
  display: flex !important;
  align-items: center !important;
  gap: 0.25rem !important;
  padding: 0.25rem 0.5rem !important;
  background: hsl(var(--muted) / 0.3) !important;
  border-radius: 0.375rem !important;
  transition: all 0.2s ease !important;
}

.waline-wrapper-premium .wl-stats .wl-stat:hover {
  background: hsl(var(--muted) / 0.5) !important;
  transform: translateY(-1px) !important;
}

/* 间距优化系统 */
.waline-wrapper-premium .wl-container > * + * {
  margin-top: 1.5rem !important;
}

.waline-wrapper-premium .wl-card + .wl-card {
  margin-top: 1.25rem !important;
}

.waline-wrapper-premium .wl-card .wl-content + .wl-action {
  margin-top: 1rem !important;
}

/* 视觉层次系统 */
.waline-wrapper-premium {
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 0.75rem;
  --spacing-lg: 1rem;
  --spacing-xl: 1.5rem;
  --spacing-2xl: 2rem;
  --spacing-3xl: 3rem;
  
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
  
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.08);
  --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.12);
  --shadow-xl: 0 16px 48px rgba(0, 0, 0, 0.15);
}

/* 微交互优化 */
.waline-wrapper-premium .wl-card {
  transform-origin: center !important;
}

.waline-wrapper-premium .wl-card:hover {
  --shadow-color: rgba(0, 0, 0, 0.1);
  box-shadow: 
    0 8px 32px var(--shadow-color),
    inset 0 1px 0 rgba(255, 255, 255, 0.15) !important;
}

.waline-wrapper-premium .wl-btn,
.waline-wrapper-premium .wl-action,
.waline-wrapper-premium .wl-sort-btn {
  will-change: transform, box-shadow !important;
}

/* 文字排版优化 */
.waline-wrapper-premium .wl-content {
  text-rendering: optimizeLegibility !important;
  -webkit-font-smoothing: antialiased !important;
  -moz-osx-font-smoothing: grayscale !important;
}

.waline-wrapper-premium .wl-content h1,
.waline-wrapper-premium .wl-content h2,
.waline-wrapper-premium .wl-content h3,
.waline-wrapper-premium .wl-content h4,
.waline-wrapper-premium .wl-content h5,
.waline-wrapper-premium .wl-content h6 {
  font-weight: 700 !important;
  margin-top: 1.5rem !important;
  margin-bottom: 0.75rem !important;
  color: hsl(var(--foreground)) !important;
}

.waline-wrapper-premium .wl-content blockquote {
  position: relative !important;
  margin: 1.5rem 0 !important;
  padding: 1rem 1.5rem !important;
  background: linear-gradient(135deg, 
    hsl(var(--muted) / 0.3) 0%, 
    hsl(var(--muted) / 0.1) 100%) !important;
  border-left: 4px solid hsl(var(--primary)) !important;
  border-radius: 0 var(--radius-lg) var(--radius-lg) 0 !important;
  font-style: italic !important;
  color: hsl(var(--muted-foreground)) !important;
}

.waline-wrapper-premium .wl-content blockquote::before {
  content: '"';
  position: absolute;
  top: -0.5rem;
  left: 0.75rem;
  font-size: 3rem;
  color: hsl(var(--primary) / 0.3);
  font-family: serif;
  line-height: 1;
}

/* 表情和媒体内容优化 */
.waline-wrapper-premium .wl-content img {
  border-radius: var(--radius-lg) !important;
  box-shadow: var(--shadow-md) !important;
  transition: all 0.3s ease !important;
  max-width: 100% !important;
  height: auto !important;
}

.waline-wrapper-premium .wl-content img:hover {
  transform: scale(1.02) !important;
  box-shadow: var(--shadow-lg) !important;
}

/* === 响应式设计优化 === */
@media (max-width: 1024px) {
  .waline-wrapper-premium {
    padding: 1.75rem !important;
  }
  
  .waline-wrapper-premium .wl-sort-container {
    flex-wrap: wrap !important;
    justify-content: center !important;
  }
}

@media (max-width: 768px) {
  .waline-wrapper-premium {
    padding: 1.25rem !important;
    margin: 0 -0.5rem !important;
    border-radius: var(--radius-xl) !important;
    box-shadow: 
      0 8px 32px rgba(0, 0, 0, 0.08),
      inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
  }

  .waline-wrapper-premium .wl-panel {
    padding: 1.25rem !important;
  }

  .waline-wrapper-premium .wl-header {
    flex-direction: column !important;
    gap: 0.75rem !important;
  }

  .waline-wrapper-premium .wl-header .wl-input {
    width: 100% !important;
    font-size: 16px !important; /* Prevent iOS zoom */
  }

  .waline-wrapper-premium .wl-editor {
    min-height: 100px !important;
    font-size: 16px !important; /* Prevent iOS zoom */
  }

  .waline-wrapper-premium .wl-card {
    padding: 1.25rem !important;
    margin-bottom: 1rem !important;
  }

  .waline-wrapper-premium .wl-card .wl-head {
    gap: 0.75rem !important;
  }

  .waline-wrapper-premium .wl-avatar {
    width: 40px !important;
    height: 40px !important;
  }

  .waline-wrapper-premium .wl-sort-container {
    padding: 0.5rem !important;
    flex-wrap: wrap !important;
    gap: 0.25rem !important;
  }

  .waline-wrapper-premium .wl-sort-btn {
    padding: 0.375rem 0.75rem !important;
    font-size: 0.75rem !important;
  }

  .waline-wrapper-premium .wl-operation {
    flex-wrap: wrap !important;
    gap: 0.375rem !important;
  }
}

@media (max-width: 480px) {
  .waline-wrapper-premium {
    padding: 1rem !important;
    margin: 0 -1rem !important;
    border-radius: var(--radius-lg) !important;
  }

  .waline-wrapper-premium .wl-panel {
    padding: 1rem !important;
  }

  .waline-wrapper-premium .wl-card {
    padding: 1rem !important;
  }

  .waline-wrapper-premium .wl-avatar {
    width: 36px !important;
    height: 36px !important;
  }
}

/* === 加载状态增强 === */
.waline-wrapper-premium.waline-loaded {
  animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* === 可访问性改进 === */
.waline-wrapper-premium *:focus-visible {
  outline: 2px solid hsl(var(--primary)) !important;
  outline-offset: 2px !important;
}

/* === 暗色模式优化 === */
@media (prefers-color-scheme: dark) {
  .waline-wrapper-premium .wl-content img {
    opacity: 0.9 !important;
    border-radius: 0.5rem !important;
  }

  .waline-wrapper-premium .wl-content blockquote {
    border-left: 4px solid hsl(var(--primary)) !important;
    padding-left: 1rem !important;
    margin: 1rem 0 !important;
    background: hsl(var(--muted) / 0.3) !important;
    border-radius: 0 0.5rem 0.5rem 0 !important;
    padding: 0.75rem 1rem !important;
  }
}

/* === 表情包弹窗优化 - Red Dot Award Design === */
.waline-wrapper-premium .wl-emoji {
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  z-index: 9999 !important;
  width: 90vw !important;
  max-width: 420px !important;
  min-width: 320px !important;
  height: auto !important;
  max-height: 65vh !important;
  background: linear-gradient(135deg,
    hsl(var(--card) / 0.95) 0%,
    hsl(var(--card) / 0.98) 100%) !important;
  border: 1px solid hsl(var(--border) / 0.2) !important;
  border-radius: 1.25rem !important;
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
  -webkit-backdrop-filter: blur(20px) saturate(1.2) !important;
          backdrop-filter: blur(20px) saturate(1.2) !important;
  overflow: hidden !important;
  display: flex !important;
  flex-direction: column !important;
}

/* 表情包弹窗背景遮罩 */
.waline-wrapper-premium .wl-emoji::before {
  content: '' !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background: rgba(0, 0, 0, 0.4) !important;
  -webkit-backdrop-filter: blur(8px) !important;
          backdrop-filter: blur(8px) !important;
  z-index: -1 !important;
}

/* 表情包标签页优化 */
.waline-wrapper-premium .wl-emoji .wl-tab-wrapper {
  display: flex !important;
  background: hsl(var(--muted) / 0.5) !important;
  border-radius: 1rem 1rem 0 0 !important;
  padding: 0.25rem !important;
  margin: 0.75rem 0.75rem 0 0.75rem !important;
  gap: 0.25rem !important;
}

.waline-wrapper-premium .wl-emoji .wl-tab {
  flex: 1 !important;
  background: transparent !important;
  color: hsl(var(--muted-foreground)) !important;
  border: none !important;
  border-radius: 0.75rem !important;
  padding: 0.75rem 1rem !important;
  font-size: 0.8rem !important;
  font-weight: 500 !important;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1) !important;
  cursor: pointer !important;
  text-align: center !important;
  position: relative !important;
  overflow: hidden !important;
}

.waline-wrapper-premium .wl-emoji .wl-tab::before {
  content: '' !important;
  position: absolute !important;
  inset: 0 !important;
  background: linear-gradient(135deg,
    hsl(var(--primary) / 0.1) 0%,
    hsl(var(--primary) / 0.05) 100%) !important;
  opacity: 0 !important;
  transition: opacity 0.25s ease !important;
}

.waline-wrapper-premium .wl-emoji .wl-tab:hover::before {
  opacity: 1 !important;
}

.waline-wrapper-premium .wl-emoji .wl-tab:hover {
  color: hsl(var(--primary)) !important;
  transform: translateY(-1px) !important;
}

.waline-wrapper-premium .wl-emoji .wl-tab.wl-active {
  background: linear-gradient(135deg,
    hsl(var(--primary)) 0%,
    hsl(var(--primary) / 0.9) 100%) !important;
  color: hsl(var(--primary-foreground)) !important;
  box-shadow:
    0 2px 8px hsl(var(--primary) / 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
  transform: translateY(-1px) !important;
}

.waline-wrapper-premium .wl-emoji .wl-tab.wl-active::before {
  opacity: 0 !important;
}

/* 表情包内容区域 */
.waline-wrapper-premium .wl-emoji .wl-content {
  flex: 1 !important;
  padding: 1rem 1.25rem 1.25rem !important;
  max-height: 45vh !important;
  min-height: 200px !important;
  overflow-y: auto !important;
  display: grid !important;
  grid-template-columns: repeat(auto-fill, minmax(40px, 1fr)) !important;
  gap: 0.25rem !important;
  align-content: start !important;
}

.waline-wrapper-premium .wl-emoji .wl-content::-webkit-scrollbar {
  width: 6px !important;
}

.waline-wrapper-premium .wl-emoji .wl-content::-webkit-scrollbar-track {
  background: hsl(var(--muted) / 0.3) !important;
  border-radius: 3px !important;
}

.waline-wrapper-premium .wl-emoji .wl-content::-webkit-scrollbar-thumb {
  background: hsl(var(--primary) / 0.4) !important;
  border-radius: 3px !important;
}

.waline-wrapper-premium .wl-emoji .wl-content::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--primary) / 0.6) !important;
}

/* 表情包按钮 */
.waline-wrapper-premium .wl-emoji .wl-emoji-item {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 40px !important;
  height: 40px !important;
  padding: 0.25rem !important;
  margin: 0 !important;
  border-radius: 0.5rem !important;
  transition: all 0.2s ease !important;
  cursor: pointer !important;
  font-size: 1.25rem !important;
  line-height: 1 !important;
  background: transparent !important;
  border: none !important;
}

/* 表情包图片样式 */
.waline-wrapper-premium .wl-emoji .wl-emoji-item img {
  width: 100% !important;
  height: 100% !important;
  -o-object-fit: contain !important;
     object-fit: contain !important;
  border-radius: 0.25rem !important;
}

.waline-wrapper-premium .wl-emoji .wl-emoji-item:hover {
  background: hsl(var(--primary) / 0.1) !important;
  transform: scale(1.05) !important;
  box-shadow: 0 2px 8px hsl(var(--primary) / 0.2) !important;
}

/* 移动设备表情包弹窗优化 */
@media (max-width: 640px) {
  .waline-wrapper-premium .wl-emoji {
    width: 95vw !important;
    max-width: none !important;
    min-width: 280px !important;
    max-height: 70vh !important;
    margin: 0 !important;
  }

  .waline-wrapper-premium .wl-emoji .wl-content {
    grid-template-columns: repeat(auto-fill, minmax(35px, 1fr)) !important;
    padding: 0.75rem 1rem 1rem !important;
    max-height: 50vh !important;
  }

  .waline-wrapper-premium .wl-emoji .wl-emoji-item {
    width: 35px !important;
    height: 35px !important;
    font-size: 1.1rem !important;
  }

  .waline-wrapper-premium .wl-emoji .wl-tab-wrapper {
    margin: 0.5rem 0.75rem 0 0.75rem !important;
    padding: 0.125rem !important;
  }

  .waline-wrapper-premium .wl-emoji .wl-tab {
    padding: 0.5rem 0.75rem !important;
    font-size: 0.875rem !important;
  }
}

/* === 评论排序系统优化 - 水平布局 === */
.waline-wrapper-premium .wl-sort-container {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  flex-direction: row !important;
  flex-wrap: wrap !important;
  gap: 0.75rem !important;
  margin: 1.5rem 0 !important;
  padding: 1rem 1.5rem !important;
  background: linear-gradient(135deg,
    hsl(var(--card) / 0.8) 0%,
    hsl(var(--card) / 0.95) 100%) !important;
  border: 1px solid hsl(var(--border) / 0.2) !important;
  border-radius: 1.25rem !important;
  -webkit-backdrop-filter: blur(12px) !important;
          backdrop-filter: blur(12px) !important;
  box-shadow:
    0 4px 20px rgba(0, 0, 0, 0.06),
    inset 0 1px 0 rgba(255, 255, 255, 0.15) !important;
  position: relative !important;
  overflow: hidden !important;
}

.waline-wrapper-premium .wl-sort-container::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  height: 2px !important;
  background: linear-gradient(90deg,
    transparent 0%,
    hsl(var(--primary) / 0.3) 50%,
    transparent 100%) !important;
}

.waline-wrapper-premium .wl-sort-btn {
  position: relative !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 0.625rem 1.25rem !important;
  border-radius: 0.75rem !important;
  font-size: 0.85rem !important;
  font-weight: 600 !important;
  color: hsl(var(--muted-foreground)) !important;
  background: linear-gradient(135deg,
    hsl(var(--background) / 0.6) 0%,
    hsl(var(--background) / 0.8) 100%) !important;
  border: 1px solid hsl(var(--border) / 0.3) !important;
  cursor: pointer !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  letter-spacing: 0.025em !important;
  pointer-events: auto !important;
  z-index: 2 !important;
  min-width: 100px !important;
  text-align: center !important;
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.04),
    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
}

.waline-wrapper-premium .wl-sort-btn::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: 0.75rem;
  background: linear-gradient(135deg,
    hsl(var(--primary) / 0.12) 0%,
    hsl(var(--primary) / 0.08) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.waline-wrapper-premium .wl-sort-btn:hover::before {
  opacity: 1;
}

.waline-wrapper-premium .wl-sort-btn:hover {
  color: hsl(var(--primary)) !important;
  transform: translateY(-2px) scale(1.02) !important;
  border-color: hsl(var(--primary) / 0.4) !important;
  box-shadow:
    0 6px 20px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
}

.waline-wrapper-premium .wl-sort-btn.wl-active {
  color: hsl(var(--primary-foreground)) !important;
  background: linear-gradient(135deg,
    hsl(var(--primary)) 0%,
    hsl(var(--primary) / 0.85) 100%) !important;
  border-color: hsl(var(--primary) / 0.6) !important;
  box-shadow:
    0 4px 16px hsl(var(--primary) / 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.25) !important;
  transform: translateY(-2px) scale(1.02) !important;
}

.waline-wrapper-premium .wl-sort-btn.wl-active::before {
  opacity: 0;
}

/* 排序按钮图标 */
.waline-wrapper-premium .wl-sort-btn::after {
  content: '';
  margin-left: 0.25rem;
  width: 0;
  height: 0;
  border-left: 3px solid transparent;
  border-right: 3px solid transparent;
  border-top: 4px solid currentColor;
  opacity: 0.6;
  transition: all 0.25s ease;
}

.waline-wrapper-premium .wl-sort-btn[data-sort="latest"]::after {
  border-top: 4px solid currentColor;
  border-bottom: none;
}

.waline-wrapper-premium .wl-sort-btn[data-sort="oldest"]::after {
  border-bottom: 4px solid currentColor;
  border-top: none;
}

.waline-wrapper-premium .wl-sort-btn[data-sort="hottest"]::after {
  content: '🔥';
  border: none;
  font-size: 0.7rem;
  margin-left: 0.125rem;
}

.waline-wrapper-premium .wl-sort-btn[data-sort="comment"]::after {
  content: '💬';
  border: none;
  font-size: 0.7rem;
  margin-left: 0.125rem;
}

/* === 分页器重新设计 === */
.waline-wrapper-premium .wl-operation {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 0.5rem !important;
  margin-top: 2rem !important;
  padding: 1rem !important;
}

.waline-wrapper-premium .wl-operation .wl-btn {
  position: relative !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  min-width: 40px !important;
  height: 40px !important;
  padding: 0.5rem 1rem !important;
  border-radius: 0.5rem !important;
  font-size: 0.85rem !important;
  font-weight: 500 !important;
  color: hsl(var(--muted-foreground)) !important;
  background: linear-gradient(135deg, 
    hsl(var(--card) / 0.5) 0%, 
    hsl(var(--card) / 0.8) 100%) !important;
  border: 1px solid hsl(var(--border) / 0.2) !important;
  cursor: pointer !important;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1) !important;
  -webkit-backdrop-filter: blur(8px) !important;
          backdrop-filter: blur(8px) !important;
  box-shadow: 
    0 2px 8px rgba(0, 0, 0, 0.04),
    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
  margin: 0 !important;
  pointer-events: auto !important;
}

.waline-wrapper-premium .wl-operation .wl-btn:hover {
  color: hsl(var(--primary)) !important;
  border-color: hsl(var(--primary) / 0.3) !important;
  transform: translateY(-1px) !important;
  box-shadow: 
    0 4px 16px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.15) !important;
}

.waline-wrapper-premium .wl-operation .wl-current {
  color: hsl(var(--primary-foreground)) !important;
  background: linear-gradient(135deg, 
    hsl(var(--primary)) 0%, 
    hsl(var(--primary) / 0.9) 100%) !important;
  border-color: hsl(var(--primary)) !important;
  box-shadow: 
    0 4px 16px hsl(var(--primary) / 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
  transform: translateY(-1px) !important;
}

.waline-wrapper-premium .wl-operation .wl-current:hover {
  transform: translateY(-1px) !important;
  box-shadow: 
    0 6px 20px hsl(var(--primary) / 0.35),
    inset 0 1px 0 rgba(255, 255, 255, 0.25) !important;
}

/* 加载更多按钮 */
.waline-wrapper-premium .wl-load-more {
  display: block !important;
  width: 100% !important;
  max-width: 200px !important;
  margin: 2rem auto !important;
  padding: 0.875rem 2rem !important;
  border-radius: 0.75rem !important;
  background: linear-gradient(135deg, 
    hsl(var(--secondary) / 0.8) 0%, 
    hsl(var(--secondary) / 0.6) 100%) !important;
  color: hsl(var(--secondary-foreground)) !important;
  border: 1px solid hsl(var(--border) / 0.3) !important;
  font-weight: 600 !important;
  font-size: 0.9rem !important;
  letter-spacing: 0.025em !important;
  cursor: pointer !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  -webkit-backdrop-filter: blur(8px) !important;
          backdrop-filter: blur(8px) !important;
  box-shadow: 
    0 4px 16px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
}

.waline-wrapper-premium .wl-load-more:hover {
  transform: translateY(-2px) !important;
  border-color: hsl(var(--primary) / 0.3) !important;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.12),
    inset 0 1px 0 rgba(255, 255, 255, 0.15) !important;
}

/* === 错误和空状态 === */
.waline-wrapper-premium .wl-error {
  background: hsl(var(--destructive) / 0.1) !important;
  color: hsl(var(--destructive)) !important;
  padding: 1rem !important;
  border-radius: 0.75rem !important;
  border: 1px solid hsl(var(--destructive) / 0.2) !important;
  margin: 1rem 0 !important;
  text-align: center !important;
}

.waline-wrapper-premium .wl-empty {
  text-align: center !important;
  padding: 3rem 1rem !important;
  color: hsl(var(--muted-foreground)) !important;
}

.waline-wrapper-premium .wl-empty::before {
  content: '💬';
  display: block;
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.3;
}
/*!*********************************************************************************************************************************************************************************************************************************************************************!*\
  !*** css ./node_modules/next/dist/build/webpack/loaders/css-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[2]!./node_modules/next/dist/build/webpack/loaders/postcss-loader/src/index.js??ruleSet[1].rules[14].oneOf[12].use[3]!./src/styles/tailwind.css ***!
  \*********************************************************************************************************************************************************************************************************************************************************************/
*, ::before, ::after {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}

::backdrop {
  --tw-border-spacing-x: 0;
  --tw-border-spacing-y: 0;
  --tw-translate-x: 0;
  --tw-translate-y: 0;
  --tw-rotate: 0;
  --tw-skew-x: 0;
  --tw-skew-y: 0;
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  --tw-pan-x:  ;
  --tw-pan-y:  ;
  --tw-pinch-zoom:  ;
  --tw-scroll-snap-strictness: proximity;
  --tw-gradient-from-position:  ;
  --tw-gradient-via-position:  ;
  --tw-gradient-to-position:  ;
  --tw-ordinal:  ;
  --tw-slashed-zero:  ;
  --tw-numeric-figure:  ;
  --tw-numeric-spacing:  ;
  --tw-numeric-fraction:  ;
  --tw-ring-inset:  ;
  --tw-ring-offset-width: 0px;
  --tw-ring-offset-color: #fff;
  --tw-ring-color: rgb(59 130 246 / 0.5);
  --tw-ring-offset-shadow: 0 0 #0000;
  --tw-ring-shadow: 0 0 #0000;
  --tw-shadow: 0 0 #0000;
  --tw-shadow-colored: 0 0 #0000;
  --tw-blur:  ;
  --tw-brightness:  ;
  --tw-contrast:  ;
  --tw-grayscale:  ;
  --tw-hue-rotate:  ;
  --tw-invert:  ;
  --tw-saturate:  ;
  --tw-sepia:  ;
  --tw-drop-shadow:  ;
  --tw-backdrop-blur:  ;
  --tw-backdrop-brightness:  ;
  --tw-backdrop-contrast:  ;
  --tw-backdrop-grayscale:  ;
  --tw-backdrop-hue-rotate:  ;
  --tw-backdrop-invert:  ;
  --tw-backdrop-opacity:  ;
  --tw-backdrop-saturate:  ;
  --tw-backdrop-sepia:  ;
  --tw-contain-size:  ;
  --tw-contain-layout:  ;
  --tw-contain-paint:  ;
  --tw-contain-style:  ;
}/*
! tailwindcss v3.4.17 | MIT License | https://tailwindcss.com
*//*
1. Prevent padding and border from affecting element width. (https://github.com/mozdevs/cssremedy/issues/4)
2. Allow adding a border to an element by just adding a border-width. (https://github.com/tailwindcss/tailwindcss/pull/116)
*/

*,
::before,
::after {
  box-sizing: border-box; /* 1 */
  border-width: 0; /* 2 */
  border-style: solid; /* 2 */
  border-color: #e5e7eb; /* 2 */
}

::before,
::after {
  --tw-content: '';
}

/*
1. Use a consistent sensible line-height in all browsers.
2. Prevent adjustments of font size after orientation changes in iOS.
3. Use a more readable tab size.
4. Use the user's configured `sans` font-family by default.
5. Use the user's configured `sans` font-feature-settings by default.
6. Use the user's configured `sans` font-variation-settings by default.
7. Disable tap highlights on iOS
*/

html,
:host {
  line-height: 1.5; /* 1 */
  -webkit-text-size-adjust: 100%; /* 2 */
  -moz-tab-size: 4; /* 3 */
  -o-tab-size: 4;
     tab-size: 4; /* 3 */
  font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"; /* 4 */
  font-feature-settings: normal; /* 5 */
  font-variation-settings: normal; /* 6 */
  -webkit-tap-highlight-color: transparent; /* 7 */
}

/*
1. Remove the margin in all browsers.
2. Inherit line-height from `html` so users can set them as a class directly on the `html` element.
*/

body {
  margin: 0; /* 1 */
  line-height: inherit; /* 2 */
}

/*
1. Add the correct height in Firefox.
2. Correct the inheritance of border color in Firefox. (https://bugzilla.mozilla.org/show_bug.cgi?id=190655)
3. Ensure horizontal rules are visible by default.
*/

hr {
  height: 0; /* 1 */
  color: inherit; /* 2 */
  border-top-width: 1px; /* 3 */
}

/*
Add the correct text decoration in Chrome, Edge, and Safari.
*/

abbr:where([title]) {
  -webkit-text-decoration: underline dotted;
          text-decoration: underline dotted;
}

/*
Remove the default font size and weight for headings.
*/

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

/*
Reset links to optimize for opt-in styling instead of opt-out.
*/

a {
  color: inherit;
  text-decoration: inherit;
}

/*
Add the correct font weight in Edge and Safari.
*/

b,
strong {
  font-weight: bolder;
}

/*
1. Use the user's configured `mono` font-family by default.
2. Use the user's configured `mono` font-feature-settings by default.
3. Use the user's configured `mono` font-variation-settings by default.
4. Correct the odd `em` font sizing in all browsers.
*/

code,
kbd,
samp,
pre {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace; /* 1 */
  font-feature-settings: normal; /* 2 */
  font-variation-settings: normal; /* 3 */
  font-size: 1em; /* 4 */
}

/*
Add the correct font size in all browsers.
*/

small {
  font-size: 80%;
}

/*
Prevent `sub` and `sup` elements from affecting the line height in all browsers.
*/

sub,
sup {
  font-size: 75%;
  line-height: 0;
  position: relative;
  vertical-align: baseline;
}

sub {
  bottom: -0.25em;
}

sup {
  top: -0.5em;
}

/*
1. Remove text indentation from table contents in Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=999088, https://bugs.webkit.org/show_bug.cgi?id=201297)
2. Correct table border color inheritance in all Chrome and Safari. (https://bugs.chromium.org/p/chromium/issues/detail?id=935729, https://bugs.webkit.org/show_bug.cgi?id=195016)
3. Remove gaps between table borders by default.
*/

table {
  text-indent: 0; /* 1 */
  border-color: inherit; /* 2 */
  border-collapse: collapse; /* 3 */
}

/*
1. Change the font styles in all browsers.
2. Remove the margin in Firefox and Safari.
3. Remove default padding in all browsers.
*/

button,
input,
optgroup,
select,
textarea {
  font-family: inherit; /* 1 */
  font-feature-settings: inherit; /* 1 */
  font-variation-settings: inherit; /* 1 */
  font-size: 100%; /* 1 */
  font-weight: inherit; /* 1 */
  line-height: inherit; /* 1 */
  letter-spacing: inherit; /* 1 */
  color: inherit; /* 1 */
  margin: 0; /* 2 */
  padding: 0; /* 3 */
}

/*
Remove the inheritance of text transform in Edge and Firefox.
*/

button,
select {
  text-transform: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Remove default button styles.
*/

button,
input:where([type='button']),
input:where([type='reset']),
input:where([type='submit']) {
  -webkit-appearance: button; /* 1 */
  background-color: transparent; /* 2 */
  background-image: none; /* 2 */
}

/*
Use the modern Firefox focus style for all focusable elements.
*/

:-moz-focusring {
  outline: auto;
}

/*
Remove the additional `:invalid` styles in Firefox. (https://github.com/mozilla/gecko-dev/blob/2f9eacd9d3d995c937b4251a5557d95d494c9be1/layout/style/res/forms.css#L728-L737)
*/

:-moz-ui-invalid {
  box-shadow: none;
}

/*
Add the correct vertical alignment in Chrome and Firefox.
*/

progress {
  vertical-align: baseline;
}

/*
Correct the cursor style of increment and decrement buttons in Safari.
*/

::-webkit-inner-spin-button,
::-webkit-outer-spin-button {
  height: auto;
}

/*
1. Correct the odd appearance in Chrome and Safari.
2. Correct the outline style in Safari.
*/

[type='search'] {
  -webkit-appearance: textfield; /* 1 */
  outline-offset: -2px; /* 2 */
}

/*
Remove the inner padding in Chrome and Safari on macOS.
*/

::-webkit-search-decoration {
  -webkit-appearance: none;
}

/*
1. Correct the inability to style clickable types in iOS and Safari.
2. Change font properties to `inherit` in Safari.
*/

::-webkit-file-upload-button {
  -webkit-appearance: button; /* 1 */
  font: inherit; /* 2 */
}

/*
Add the correct display in Chrome and Safari.
*/

summary {
  display: list-item;
}

/*
Removes the default spacing and border for appropriate elements.
*/

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

fieldset {
  margin: 0;
  padding: 0;
}

legend {
  padding: 0;
}

ol,
ul,
menu {
  list-style: none;
  margin: 0;
  padding: 0;
}

/*
Reset default styling for dialogs.
*/
dialog {
  padding: 0;
}

/*
Prevent resizing textareas horizontally by default.
*/

textarea {
  resize: vertical;
}

/*
1. Reset the default placeholder opacity in Firefox. (https://github.com/tailwindlabs/tailwindcss/issues/3300)
2. Set the default placeholder color to the user's configured gray 400 color.
*/

input::-moz-placeholder, textarea::-moz-placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}

input::placeholder,
textarea::placeholder {
  opacity: 1; /* 1 */
  color: #9ca3af; /* 2 */
}

/*
Set the default cursor for buttons.
*/

button,
[role="button"] {
  cursor: pointer;
}

/*
Make sure disabled buttons don't get the pointer cursor.
*/
:disabled {
  cursor: default;
}

/*
1. Make replaced elements `display: block` by default. (https://github.com/mozdevs/cssremedy/issues/14)
2. Add `vertical-align: middle` to align replaced elements more sensibly by default. (https://github.com/jensimmons/cssremedy/issues/14#issuecomment-634934210)
   This can trigger a poorly considered lint error in some tools but is included by design.
*/

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block; /* 1 */
  vertical-align: middle; /* 2 */
}

/*
Constrain images and videos to the parent width and preserve their intrinsic aspect ratio. (https://github.com/mozdevs/cssremedy/issues/14)
*/

img,
video {
  max-width: 100%;
  height: auto;
}

/* Make elements with the HTML hidden attribute stay hidden by default */
[hidden]:where(:not([hidden="until-found"])) {
  display: none;
}
  :root {
      --background: 0 0% 100%;
      --foreground: 240 10% 3.9%;
      --card: 0 0% 100%;        /* 浅灰色背景 */
      --card-foreground: 240 10% 3.9%;  /* 保持原来的深色文字 */
      --popover: 0 0% 100%;
      --popover-foreground: 240 10% 3.9%;
      --primary: 171 70% 35%;      /* 稍微加深主色提高对比度 */
      --primary-foreground: 0 0% 100%;
      --secondary: 240 4.8% 95.9%;
      --secondary-foreground: 240 5.9% 10%;
      --muted: 240 4.8% 95.9%;
      --muted-foreground: 240 3.8% 46.1%;
      --accent: 240 4.8% 95.9%;
      --accent-foreground: 240 5.9% 10%;
      --destructive: 0 84.2% 60.2%;
      --destructive-foreground: 0 0% 98%;
      --border: 240 5.9% 90%;
      --input: 240 5.9% 90%;
      --ring: 171 70% 35%;  /* 使用主色作为焦点环 */

      /* 新增状态色彩 */
      --success: 142 76% 36%;
      --success-foreground: 0 0% 98%;
      --warning: 38 92% 50%;
      --warning-foreground: 0 0% 98%;
      --info: 221 83% 53%;
      --info-foreground: 0 0% 98%;
      --radius: 0.75rem;
      --chart-1: 12 76% 61%;
      --chart-2: 173 58% 39%;
      --chart-3: 197 37% 24%;
      --chart-4: 43 74% 66%;
      --chart-5: 27 87% 67%;
    }

    .dark {
      /* 暗色模式基础色彩 - 增强对比度 */
      --background: 240 10% 3.9%;
      --foreground: 0 0% 98%;
      --card: 240 10% 8%;        /* 稍微提亮卡片背景 */
      --card-foreground: 0 0% 98%;
      --popover: 240 10% 3.9%;
      --popover-foreground: 0 0% 98%;

      /* 暗色模式主色调 - 提高亮度 */
      --primary: 171 70% 55%;      /* 提高主色亮度 */
      --primary-foreground: 240 10% 3.9%;
      --secondary: 240 3.7% 15.9%;
      --secondary-foreground: 0 0% 98%;

      /* 暗色模式辅助色彩 */
      --muted: 240 3.7% 15.9%;
      --muted-foreground: 240 5% 64.9%;
      --accent: 240 3.7% 15.9%;
      --accent-foreground: 0 0% 98%;
      --destructive: 0 62.8% 30.6%;
      --destructive-foreground: 0 0% 98%;

      /* 暗色模式边框和输入 */
      --border: 240 3.7% 15.9%;
      --input: 240 3.7% 15.9%;
      --ring: 171 70% 55%;  /* 使用主色作为焦点环 */

      /* 暗色模式状态色彩 */
      --success: 142 76% 45%;
      --success-foreground: 240 10% 3.9%;
      --warning: 38 92% 60%;
      --warning-foreground: 240 10% 3.9%;
      --info: 221 83% 63%;
      --info-foreground: 240 10% 3.9%;
      --ring: 240 4.9% 83.9%;
      --chart-1: 220 70% 50%;
      --chart-2: 160 60% 45%;
      --chart-3: 30 80% 55%;
      --chart-4: 280 65% 60%;
      --chart-5: 340 75% 55%;
    }
  * {
  border-color: hsl(var(--border));
}
  body {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
}
.\!container {
  width: 100% !important;
}
.container {
  width: 100%;
}
@media (min-width: 320px) {

  .\!container {
    max-width: 320px !important;
  }

  .container {
    max-width: 320px;
  }
}
@media (min-width: 640px) {

  .\!container {
    max-width: 640px !important;
  }

  .container {
    max-width: 640px;
  }
}
@media (min-width: 768px) {

  .\!container {
    max-width: 768px !important;
  }

  .container {
    max-width: 768px;
  }
}
@media (min-width: 1024px) {

  .\!container {
    max-width: 1024px !important;
  }

  .container {
    max-width: 1024px;
  }
}
@media (min-width: 1280px) {

  .\!container {
    max-width: 1280px !important;
  }

  .container {
    max-width: 1280px;
  }
}
@media (min-width: 1536px) {

  .\!container {
    max-width: 1536px !important;
  }

  .container {
    max-width: 1536px;
  }
}
@media (min-width: 1920px) {

  .\!container {
    max-width: 1920px !important;
  }

  .container {
    max-width: 1920px;
  }
}
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border-width: 0;
}
.pointer-events-none {
  pointer-events: none;
}
.pointer-events-auto {
  pointer-events: auto;
}
.visible {
  visibility: visible;
}
.collapse {
  visibility: collapse;
}
.fixed {
  position: fixed;
}
.absolute {
  position: absolute;
}
.relative {
  position: relative;
}
.sticky {
  position: sticky;
}
.-inset-1 {
  inset: -0.25rem;
}
.-inset-2 {
  inset: -0.5rem;
}
.inset-0 {
  inset: 0px;
}
.inset-1 {
  inset: 0.25rem;
}
.inset-2 {
  inset: 0.5rem;
}
.inset-4 {
  inset: 1rem;
}
.inset-8 {
  inset: 2rem;
}
.-inset-x-4 {
  left: -1rem;
  right: -1rem;
}
.-inset-y-6 {
  top: -1.5rem;
  bottom: -1.5rem;
}
.inset-x-1 {
  left: 0.25rem;
  right: 0.25rem;
}
.inset-x-4 {
  left: 1rem;
  right: 1rem;
}
.inset-y-0 {
  top: 0px;
  bottom: 0px;
}
.-bottom-0\.5 {
  bottom: -0.125rem;
}
.-bottom-1 {
  bottom: -0.25rem;
}
.-bottom-10 {
  bottom: -2.5rem;
}
.-bottom-2 {
  bottom: -0.5rem;
}
.-bottom-px {
  bottom: -1px;
}
.-left-10 {
  left: -2.5rem;
}
.-left-12 {
  left: -3rem;
}
.-left-14 {
  left: -3.5rem;
}
.-left-16 {
  left: -4rem;
}
.-left-2 {
  left: -0.5rem;
}
.-left-24 {
  left: -6rem;
}
.-left-4 {
  left: -1rem;
}
.-right-1 {
  right: -0.25rem;
}
.-right-10 {
  right: -2.5rem;
}
.-right-2 {
  right: -0.5rem;
}
.-top-1 {
  top: -0.25rem;
}
.-top-10 {
  top: -2.5rem;
}
.-top-12 {
  top: -3rem;
}
.-top-16 {
  top: -4rem;
}
.-top-2 {
  top: -0.5rem;
}
.bottom-0 {
  bottom: 0px;
}
.bottom-1\/4 {
  bottom: 25%;
}
.bottom-12 {
  bottom: 3rem;
}
.bottom-16 {
  bottom: 4rem;
}
.bottom-20 {
  bottom: 5rem;
}
.bottom-4 {
  bottom: 1rem;
}
.bottom-6 {
  bottom: 1.5rem;
}
.bottom-8 {
  bottom: 2rem;
}
.bottom-full {
  bottom: 100%;
}
.left-0 {
  left: 0px;
}
.left-1\/2 {
  left: 50%;
}
.left-1\/4 {
  left: 25%;
}
.left-12 {
  left: 3rem;
}
.left-16 {
  left: 4rem;
}
.left-2 {
  left: 0.5rem;
}
.left-20 {
  left: 5rem;
}
.left-3 {
  left: 0.75rem;
}
.left-4 {
  left: 1rem;
}
.left-5 {
  left: 1.25rem;
}
.left-6 {
  left: 1.5rem;
}
.left-8 {
  left: 2rem;
}
.left-\[31px\] {
  left: 31px;
}
.left-full {
  left: 100%;
}
.right-0 {
  right: 0px;
}
.right-1 {
  right: 0.25rem;
}
.right-1\/3 {
  right: 33.333333%;
}
.right-1\/4 {
  right: 25%;
}
.right-10 {
  right: 2.5rem;
}
.right-12 {
  right: 3rem;
}
.right-16 {
  right: 4rem;
}
.right-2 {
  right: 0.5rem;
}
.right-3 {
  right: 0.75rem;
}
.right-4 {
  right: 1rem;
}
.right-6 {
  right: 1.5rem;
}
.right-8 {
  right: 2rem;
}
.top-0 {
  top: 0px;
}
.top-1 {
  top: 0.25rem;
}
.top-1\/2 {
  top: 50%;
}
.top-1\/3 {
  top: 33.333333%;
}
.top-1\/4 {
  top: 25%;
}
.top-10 {
  top: 2.5rem;
}
.top-2 {
  top: 0.5rem;
}
.top-3 {
  top: 0.75rem;
}
.top-4 {
  top: 1rem;
}
.top-6 {
  top: 1.5rem;
}
.top-7 {
  top: 1.75rem;
}
.top-8 {
  top: 2rem;
}
.top-\[var\(--avatar-top\2c theme\(spacing\.3\)\)\] {
  top: var(--avatar-top,0.75rem);
}
.top-\[var\(--header-top\2c theme\(spacing\.6\)\)\] {
  top: var(--header-top,1.5rem);
}
.top-full {
  top: 100%;
}
.-z-10 {
  z-index: -10;
}
.z-0 {
  z-index: 0;
}
.z-10 {
  z-index: 10;
}
.z-20 {
  z-index: 20;
}
.z-30 {
  z-index: 30;
}
.z-40 {
  z-index: 40;
}
.z-50 {
  z-index: 50;
}
.z-\[1000\] {
  z-index: 1000;
}
.z-\[10\] {
  z-index: 10;
}
.z-\[2000\] {
  z-index: 2000;
}
.z-\[5\] {
  z-index: 5;
}
.z-\[60\] {
  z-index: 60;
}
.z-\[99999\] {
  z-index: 99999;
}
.z-\[9999\] {
  z-index: 9999;
}
.order-first {
  order: -9999;
}
.order-last {
  order: 9999;
}
.col-span-full {
  grid-column: 1 / -1;
}
.-m-1 {
  margin: -0.25rem;
}
.-m-6 {
  margin: -1.5rem;
}
.m-0 {
  margin: 0px;
}
.m-4 {
  margin: 1rem;
}
.-mx-1 {
  margin-left: -0.25rem;
  margin-right: -0.25rem;
}
.-my-2 {
  margin-top: -0.5rem;
  margin-bottom: -0.5rem;
}
.mx-1 {
  margin-left: 0.25rem;
  margin-right: 0.25rem;
}
.mx-4 {
  margin-left: 1rem;
  margin-right: 1rem;
}
.mx-auto {
  margin-left: auto;
  margin-right: auto;
}
.my-1 {
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
}
.my-2 {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}
.my-3 {
  margin-top: 0.75rem;
  margin-bottom: 0.75rem;
}
.my-4 {
  margin-top: 1rem;
  margin-bottom: 1rem;
}
.my-6 {
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
}
.my-8 {
  margin-top: 2rem;
  margin-bottom: 2rem;
}
.-mb-3 {
  margin-bottom: -0.75rem;
}
.mb-0 {
  margin-bottom: 0px;
}
.mb-0\.5 {
  margin-bottom: 0.125rem;
}
.mb-1 {
  margin-bottom: 0.25rem;
}
.mb-12 {
  margin-bottom: 3rem;
}
.mb-16 {
  margin-bottom: 4rem;
}
.mb-2 {
  margin-bottom: 0.5rem;
}
.mb-3 {
  margin-bottom: 0.75rem;
}
.mb-4 {
  margin-bottom: 1rem;
}
.mb-5 {
  margin-bottom: 1.25rem;
}
.mb-6 {
  margin-bottom: 1.5rem;
}
.mb-8 {
  margin-bottom: 2rem;
}
.ml-1 {
  margin-left: 0.25rem;
}
.ml-2 {
  margin-left: 0.5rem;
}
.ml-24 {
  margin-left: 6rem;
}
.ml-3 {
  margin-left: 0.75rem;
}
.ml-4 {
  margin-left: 1rem;
}
.ml-8 {
  margin-left: 2rem;
}
.ml-auto {
  margin-left: auto;
}
.mr-1 {
  margin-right: 0.25rem;
}
.mr-2 {
  margin-right: 0.5rem;
}
.mt-0 {
  margin-top: 0px;
}
.mt-1 {
  margin-top: 0.25rem;
}
.mt-10 {
  margin-top: 2.5rem;
}
.mt-12 {
  margin-top: 3rem;
}
.mt-16 {
  margin-top: 4rem;
}
.mt-2 {
  margin-top: 0.5rem;
}
.mt-20 {
  margin-top: 5rem;
}
.mt-24 {
  margin-top: 6rem;
}
.mt-3 {
  margin-top: 0.75rem;
}
.mt-4 {
  margin-top: 1rem;
}
.mt-5 {
  margin-top: 1.25rem;
}
.mt-6 {
  margin-top: 1.5rem;
}
.mt-8 {
  margin-top: 2rem;
}
.mt-\[calc\(theme\(spacing\.16\)-theme\(spacing\.3\)\)\] {
  margin-top: calc(4rem - 0.75rem);
}
.mt-auto {
  margin-top: auto;
}
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}
.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}
.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}
.line-clamp-4 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 4;
}
.line-clamp-6 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 6;
}
.block {
  display: block;
}
.inline-block {
  display: inline-block;
}
.inline {
  display: inline;
}
.flex {
  display: flex;
}
.inline-flex {
  display: inline-flex;
}
.table {
  display: table;
}
.grid {
  display: grid;
}
.list-item {
  display: list-item;
}
.hidden {
  display: none;
}
.aspect-\[4\/3\] {
  aspect-ratio: 4/3;
}
.aspect-auto {
  aspect-ratio: auto;
}
.aspect-square {
  aspect-ratio: 1 / 1;
}
.aspect-video {
  aspect-ratio: 16 / 9;
}
.h-0 {
  height: 0px;
}
.h-0\.5 {
  height: 0.125rem;
}
.h-1 {
  height: 0.25rem;
}
.h-1\.5 {
  height: 0.375rem;
}
.h-10 {
  height: 2.5rem;
}
.h-12 {
  height: 3rem;
}
.h-14 {
  height: 3.5rem;
}
.h-16 {
  height: 4rem;
}
.h-2 {
  height: 0.5rem;
}
.h-2\.5 {
  height: 0.625rem;
}
.h-20 {
  height: 5rem;
}
.h-24 {
  height: 6rem;
}
.h-3 {
  height: 0.75rem;
}
.h-3\.5 {
  height: 0.875rem;
}
.h-32 {
  height: 8rem;
}
.h-4 {
  height: 1rem;
}
.h-40 {
  height: 10rem;
}
.h-48 {
  height: 12rem;
}
.h-5 {
  height: 1.25rem;
}
.h-6 {
  height: 1.5rem;
}
.h-64 {
  height: 16rem;
}
.h-7 {
  height: 1.75rem;
}
.h-8 {
  height: 2rem;
}
.h-80 {
  height: 20rem;
}
.h-9 {
  height: 2.25rem;
}
.h-96 {
  height: 24rem;
}
.h-\[1\.2rem\] {
  height: 1.2rem;
}
.h-\[2px\] {
  height: 2px;
}
.h-\[4\.5rem\] {
  height: 4.5rem;
}
.h-\[420px\] {
  height: 420px;
}
.h-\[450px\] {
  height: 450px;
}
.h-\[calc\(90vh-80px\)\] {
  height: calc(90vh - 80px);
}
.h-auto {
  height: auto;
}
.h-full {
  height: 100%;
}
.h-px {
  height: 1px;
}
.max-h-0 {
  max-height: 0px;
}
.max-h-32 {
  max-height: 8rem;
}
.max-h-36 {
  max-height: 9rem;
}
.max-h-48 {
  max-height: 12rem;
}
.max-h-60 {
  max-height: 15rem;
}
.max-h-64 {
  max-height: 16rem;
}
.max-h-80 {
  max-height: 20rem;
}
.max-h-96 {
  max-height: 24rem;
}
.max-h-\[10000px\] {
  max-height: 10000px;
}
.max-h-\[70vh\] {
  max-height: 70vh;
}
.max-h-\[80vh\] {
  max-height: 80vh;
}
.max-h-\[85vh\] {
  max-height: 85vh;
}
.max-h-\[90vh\] {
  max-height: 90vh;
}
.max-h-\[calc\(100vh-16rem\)\] {
  max-height: calc(100vh - 16rem);
}
.max-h-\[calc\(100vh-6rem\)\] {
  max-height: calc(100vh - 6rem);
}
.max-h-full {
  max-height: 100%;
}
.min-h-0 {
  min-height: 0px;
}
.min-h-\[200px\] {
  min-height: 200px;
}
.min-h-\[300px\] {
  min-height: 300px;
}
.min-h-\[400px\] {
  min-height: 400px;
}
.min-h-\[450px\] {
  min-height: 450px;
}
.min-h-\[500px\] {
  min-height: 500px;
}
.min-h-\[80px\] {
  min-height: 80px;
}
.min-h-screen {
  min-height: 100vh;
}
.w-0 {
  width: 0px;
}
.w-0\.5 {
  width: 0.125rem;
}
.w-1 {
  width: 0.25rem;
}
.w-1\.5 {
  width: 0.375rem;
}
.w-1\/2 {
  width: 50%;
}
.w-1\/3 {
  width: 33.333333%;
}
.w-10 {
  width: 2.5rem;
}
.w-12 {
  width: 3rem;
}
.w-14 {
  width: 3.5rem;
}
.w-16 {
  width: 4rem;
}
.w-2 {
  width: 0.5rem;
}
.w-2\.5 {
  width: 0.625rem;
}
.w-2\/3 {
  width: 66.666667%;
}
.w-20 {
  width: 5rem;
}
.w-24 {
  width: 6rem;
}
.w-3 {
  width: 0.75rem;
}
.w-3\.5 {
  width: 0.875rem;
}
.w-3\/4 {
  width: 75%;
}
.w-32 {
  width: 8rem;
}
.w-4 {
  width: 1rem;
}
.w-4\/6 {
  width: 66.666667%;
}
.w-40 {
  width: 10rem;
}
.w-48 {
  width: 12rem;
}
.w-5 {
  width: 1.25rem;
}
.w-5\/6 {
  width: 83.333333%;
}
.w-6 {
  width: 1.5rem;
}
.w-64 {
  width: 16rem;
}
.w-7 {
  width: 1.75rem;
}
.w-72 {
  width: 18rem;
}
.w-8 {
  width: 2rem;
}
.w-80 {
  width: 20rem;
}
.w-9 {
  width: 2.25rem;
}
.w-96 {
  width: 24rem;
}
.w-\[1\.2rem\] {
  width: 1.2rem;
}
.w-full {
  width: 100%;
}
.w-px {
  width: 1px;
}
.min-w-0 {
  min-width: 0px;
}
.min-w-48 {
  min-width: 12rem;
}
.min-w-64 {
  min-width: 16rem;
}
.min-w-\[8rem\] {
  min-width: 8rem;
}
.max-w-20 {
  max-width: 5rem;
}
.max-w-2xl {
  max-width: 42rem;
}
.max-w-32 {
  max-width: 8rem;
}
.max-w-3xl {
  max-width: 48rem;
}
.max-w-4xl {
  max-width: 56rem;
}
.max-w-5xl {
  max-width: 64rem;
}
.max-w-6xl {
  max-width: 72rem;
}
.max-w-7xl {
  max-width: 80rem;
}
.max-w-\[200px\] {
  max-width: 200px;
}
.max-w-\[85vw\] {
  max-width: 85vw;
}
.max-w-\[90vw\] {
  max-width: 90vw;
}
.max-w-full {
  max-width: 100%;
}
.max-w-lg {
  max-width: 32rem;
}
.max-w-md {
  max-width: 28rem;
}
.max-w-none {
  max-width: none;
}
.max-w-xl {
  max-width: 36rem;
}
.max-w-xs {
  max-width: 20rem;
}
.flex-1 {
  flex: 1 1 0%;
}
.flex-auto {
  flex: 1 1 auto;
}
.flex-none {
  flex: none;
}
.flex-shrink-0 {
  flex-shrink: 0;
}
.flex-grow {
  flex-grow: 1;
}
.table-auto {
  table-layout: auto;
}
.border-collapse {
  border-collapse: collapse;
}
.origin-left {
  transform-origin: left;
}
.origin-top {
  transform-origin: top;
}
.-translate-x-1 {
  --tw-translate-x: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-x-1\/2 {
  --tw-translate-x: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-x-8 {
  --tw-translate-x: -2rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-y-1\/2 {
  --tw-translate-y: -50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-y-4 {
  --tw-translate-y: -1rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-translate-y-8 {
  --tw-translate-y: -2rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-0 {
  --tw-translate-x: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-1 {
  --tw-translate-x: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-8 {
  --tw-translate-x: 2rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-x-\[-100\%\] {
  --tw-translate-x: -100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-0 {
  --tw-translate-y: 0px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-4 {
  --tw-translate-y: 1rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-8 {
  --tw-translate-y: 2rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.translate-y-full {
  --tw-translate-y: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.-rotate-90 {
  --tw-rotate: -90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-0 {
  --tw-rotate: 0deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-180 {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-3 {
  --tw-rotate: 3deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-45 {
  --tw-rotate: 45deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-90 {
  --tw-rotate: 90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.rotate-\[-90deg\] {
  --tw-rotate: -90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-0 {
  --tw-scale-x: 0;
  --tw-scale-y: 0;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-100 {
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-105 {
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-110 {
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-125 {
  --tw-scale-x: 1.25;
  --tw-scale-y: 1.25;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-150 {
  --tw-scale-x: 1.5;
  --tw-scale-y: 1.5;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-95 {
  --tw-scale-x: .95;
  --tw-scale-y: .95;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-\[1\.02\] {
  --tw-scale-x: 1.02;
  --tw-scale-y: 1.02;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-x-0 {
  --tw-scale-x: 0;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.scale-x-100 {
  --tw-scale-x: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.transform {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
.transform-gpu {
  transform: translate3d(var(--tw-translate-x), var(--tw-translate-y), 0) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}
@keyframes bounce {

  0%, 100% {
    transform: translateY(-25%);
    animation-timing-function: cubic-bezier(0.8,0,1,1);
  }

  50% {
    transform: none;
    animation-timing-function: cubic-bezier(0,0,0.2,1);
  }
}
.animate-bounce {
  animation: bounce 1s infinite;
}
@keyframes bounce-soft {

  0%, 100% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-4px);
  }
}
.animate-bounce-soft {
  animation: bounce-soft 1s infinite;
}
@keyframes fade-in {

  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}
.animate-fade-in {
  animation: fade-in 0.3s ease-out;
}
@keyframes fade-in-down {

  0% {
    opacity: 0;
    transform: translateY(-20px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
.animate-fade-in-down {
  animation: fade-in-down 0.4s ease-out;
}
@keyframes fade-in-up {

  0% {
    opacity: 0;
    transform: translateY(20px);
  }

  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
.animate-fade-in-up {
  animation: fade-in-up 0.4s ease-out;
}
@keyframes glow-pulse {

  0%, 100% {
    opacity: 0.5;
    transform: scale(1);
  }

  50% {
    opacity: 1;
    transform: scale(1.05);
  }
}
.animate-glow-pulse {
  animation: glow-pulse 2s ease-in-out infinite;
}
@keyframes ping {

  75%, 100% {
    transform: scale(2);
    opacity: 0;
  }
}
.animate-ping {
  animation: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;
}
@keyframes pulse {

  50% {
    opacity: .5;
  }
}
.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
@keyframes pulse-soft {

  0%, 100% {
    opacity: 1;
  }

  50% {
    opacity: 0.6;
  }
}
.animate-pulse-soft {
  animation: pulse-soft 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}
@keyframes slide-in-left {

  0% {
    opacity: 0;
    transform: translateX(-20px);
  }

  100% {
    opacity: 1;
    transform: translateX(0);
  }
}
.animate-slide-in-left {
  animation: slide-in-left 0.3s ease-out;
}
@keyframes slide-in-right {

  0% {
    opacity: 0;
    transform: translateX(20px);
  }

  100% {
    opacity: 1;
    transform: translateX(0);
  }
}
.animate-slide-in-right {
  animation: slide-in-right 0.3s ease-out;
}
@keyframes spin {

  to {
    transform: rotate(360deg);
  }
}
.animate-spin {
  animation: spin 1s linear infinite;
}
@keyframes spin-slow {

  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
.animate-spin-slow {
  animation: spin-slow 2s linear infinite;
}
.cursor-default {
  cursor: default;
}
.cursor-not-allowed {
  cursor: not-allowed;
}
.cursor-pointer {
  cursor: pointer;
}
.select-none {
  -webkit-user-select: none;
     -moz-user-select: none;
          user-select: none;
}
.resize {
  resize: both;
}
.scroll-mt-20 {
  scroll-margin-top: 5rem;
}
.list-inside {
  list-style-position: inside;
}
.list-decimal {
  list-style-type: decimal;
}
.list-disc {
  list-style-type: disc;
}
.list-none {
  list-style-type: none;
}
.appearance-none {
  -webkit-appearance: none;
     -moz-appearance: none;
          appearance: none;
}
.columns-1 {
  -moz-columns: 1;
       columns: 1;
}
.columns-2 {
  -moz-columns: 2;
       columns: 2;
}
.break-inside-avoid {
  -moz-column-break-inside: avoid;
       break-inside: avoid;
}
.auto-rows-fr {
  grid-auto-rows: minmax(0, 1fr);
}
.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}
.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}
.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}
.flex-row {
  flex-direction: row;
}
.flex-row-reverse {
  flex-direction: row-reverse;
}
.flex-col {
  flex-direction: column;
}
.flex-wrap {
  flex-wrap: wrap;
}
.items-start {
  align-items: flex-start;
}
.items-center {
  align-items: center;
}
.items-baseline {
  align-items: baseline;
}
.justify-start {
  justify-content: flex-start;
}
.justify-end {
  justify-content: flex-end;
}
.justify-center {
  justify-content: center;
}
.justify-between {
  justify-content: space-between;
}
.gap-1 {
  gap: 0.25rem;
}
.gap-1\.5 {
  gap: 0.375rem;
}
.gap-12 {
  gap: 3rem;
}
.gap-2 {
  gap: 0.5rem;
}
.gap-2\.5 {
  gap: 0.625rem;
}
.gap-3 {
  gap: 0.75rem;
}
.gap-4 {
  gap: 1rem;
}
.gap-6 {
  gap: 1.5rem;
}
.gap-8 {
  gap: 2rem;
}
.gap-x-2 {
  -moz-column-gap: 0.5rem;
       column-gap: 0.5rem;
}
.gap-x-6 {
  -moz-column-gap: 1.5rem;
       column-gap: 1.5rem;
}
.gap-x-8 {
  -moz-column-gap: 2rem;
       column-gap: 2rem;
}
.gap-y-1 {
  row-gap: 0.25rem;
}
.gap-y-10 {
  row-gap: 2.5rem;
}
.gap-y-14 {
  row-gap: 3.5rem;
}
.gap-y-16 {
  row-gap: 4rem;
}
.gap-y-8 {
  row-gap: 2rem;
}
.-space-x-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(-0.5rem * var(--tw-space-x-reverse));
  margin-left: calc(-0.5rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.25rem * var(--tw-space-x-reverse));
  margin-left: calc(0.25rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-x-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-x-reverse: 0;
  margin-right: calc(0.75rem * var(--tw-space-x-reverse));
  margin-left: calc(0.75rem * calc(1 - var(--tw-space-x-reverse)));
}
.space-y-0\.5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.125rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.125rem * var(--tw-space-y-reverse));
}
.space-y-1 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.25rem * var(--tw-space-y-reverse));
}
.space-y-1\.5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.375rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.375rem * var(--tw-space-y-reverse));
}
.space-y-12 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(3rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(3rem * var(--tw-space-y-reverse));
}
.space-y-16 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(4rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(4rem * var(--tw-space-y-reverse));
}
.space-y-2 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.5rem * var(--tw-space-y-reverse));
}
.space-y-20 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(5rem * var(--tw-space-y-reverse));
}
.space-y-3 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(0.75rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(0.75rem * var(--tw-space-y-reverse));
}
.space-y-4 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1rem * var(--tw-space-y-reverse));
}
.space-y-5 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.25rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.25rem * var(--tw-space-y-reverse));
}
.space-y-6 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(1.5rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(1.5rem * var(--tw-space-y-reverse));
}
.space-y-8 > :not([hidden]) ~ :not([hidden]) {
  --tw-space-y-reverse: 0;
  margin-top: calc(2rem * calc(1 - var(--tw-space-y-reverse)));
  margin-bottom: calc(2rem * var(--tw-space-y-reverse));
}
.divide-y > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-y-reverse: 0;
  border-top-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));
  border-bottom-width: calc(1px * var(--tw-divide-y-reverse));
}
.divide-zinc-100 > :not([hidden]) ~ :not([hidden]) {
  --tw-divide-opacity: 1;
  border-color: rgb(244 244 245 / var(--tw-divide-opacity, 1));
}
.overflow-auto {
  overflow: auto;
}
.overflow-hidden {
  overflow: hidden;
}
.overflow-visible {
  overflow: visible;
}
.overflow-x-auto {
  overflow-x: auto;
}
.overflow-y-auto {
  overflow-y: auto;
}
.truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.text-ellipsis {
  text-overflow: ellipsis;
}
.hyphens-auto {
  -webkit-hyphens: auto;
          hyphens: auto;
}
.whitespace-nowrap {
  white-space: nowrap;
}
.break-words {
  overflow-wrap: break-word;
}
.rounded {
  border-radius: 0.25rem;
}
.rounded-2xl {
  border-radius: 1rem;
}
.rounded-3xl {
  border-radius: 1.5rem;
}
.rounded-\[50\%\] {
  border-radius: 50%;
}
.rounded-full {
  border-radius: 9999px;
}
.rounded-lg {
  border-radius: var(--radius);
}
.rounded-md {
  border-radius: calc(var(--radius) - 2px);
}
.rounded-none {
  border-radius: 0px;
}
.rounded-sm {
  border-radius: calc(var(--radius) - 4px);
}
.rounded-xl {
  border-radius: 0.75rem;
}
.rounded-b-2xl {
  border-bottom-right-radius: 1rem;
  border-bottom-left-radius: 1rem;
}
.rounded-b-xl {
  border-bottom-right-radius: 0.75rem;
  border-bottom-left-radius: 0.75rem;
}
.rounded-l-full {
  border-top-left-radius: 9999px;
  border-bottom-left-radius: 9999px;
}
.rounded-l-lg {
  border-top-left-radius: var(--radius);
  border-bottom-left-radius: var(--radius);
}
.rounded-r-2xl {
  border-top-right-radius: 1rem;
  border-bottom-right-radius: 1rem;
}
.rounded-r-lg {
  border-top-right-radius: var(--radius);
  border-bottom-right-radius: var(--radius);
}
.rounded-r-sm {
  border-top-right-radius: calc(var(--radius) - 4px);
  border-bottom-right-radius: calc(var(--radius) - 4px);
}
.rounded-t-2xl {
  border-top-left-radius: 1rem;
  border-top-right-radius: 1rem;
}
.rounded-t-xl {
  border-top-left-radius: 0.75rem;
  border-top-right-radius: 0.75rem;
}
.rounded-tl-lg {
  border-top-left-radius: var(--radius);
}
.rounded-tr-lg {
  border-top-right-radius: var(--radius);
}
.border {
  border-width: 1px;
}
.border-0 {
  border-width: 0px;
}
.border-2 {
  border-width: 2px;
}
.border-4 {
  border-width: 4px;
}
.border-b {
  border-bottom-width: 1px;
}
.border-b-0 {
  border-bottom-width: 0px;
}
.border-b-2 {
  border-bottom-width: 2px;
}
.border-b-4 {
  border-bottom-width: 4px;
}
.border-l {
  border-left-width: 1px;
}
.border-l-2 {
  border-left-width: 2px;
}
.border-l-4 {
  border-left-width: 4px;
}
.border-r {
  border-right-width: 1px;
}
.border-r-2 {
  border-right-width: 2px;
}
.border-r-4 {
  border-right-width: 4px;
}
.border-t {
  border-top-width: 1px;
}
.border-t-0 {
  border-top-width: 0px;
}
.border-t-2 {
  border-top-width: 2px;
}
.border-t-4 {
  border-top-width: 4px;
}
.border-solid {
  border-style: solid;
}
.border-amber-500\/20 {
  border-color: rgb(245 158 11 / 0.2);
}
.border-amber-500\/30 {
  border-color: rgb(245 158 11 / 0.3);
}
.border-background {
  border-color: hsl(var(--background));
}
.border-blue-200 {
  --tw-border-opacity: 1;
  border-color: rgb(191 219 254 / var(--tw-border-opacity, 1));
}
.border-blue-500\/30 {
  border-color: rgb(59 130 246 / 0.3);
}
.border-border {
  border-color: hsl(var(--border));
}
.border-border\/20 {
  border-color: hsl(var(--border) / 0.2);
}
.border-border\/30 {
  border-color: hsl(var(--border) / 0.3);
}
.border-border\/40 {
  border-color: hsl(var(--border) / 0.4);
}
.border-border\/50 {
  border-color: hsl(var(--border) / 0.5);
}
.border-current {
  border-color: currentColor;
}
.border-destructive\/20 {
  border-color: hsl(var(--destructive) / 0.2);
}
.border-emerald-200 {
  --tw-border-opacity: 1;
  border-color: rgb(167 243 208 / var(--tw-border-opacity, 1));
}
.border-emerald-300 {
  --tw-border-opacity: 1;
  border-color: rgb(110 231 183 / var(--tw-border-opacity, 1));
}
.border-gray-200 {
  --tw-border-opacity: 1;
  border-color: rgb(229 231 235 / var(--tw-border-opacity, 1));
}
.border-gray-200\/20 {
  border-color: rgb(229 231 235 / 0.2);
}
.border-gray-200\/50 {
  border-color: rgb(229 231 235 / 0.5);
}
.border-gray-300 {
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}
.border-green-500\/20 {
  border-color: rgb(34 197 94 / 0.2);
}
.border-green-500\/30 {
  border-color: rgb(34 197 94 / 0.3);
}
.border-indigo-500\/30 {
  border-color: rgb(99 102 241 / 0.3);
}
.border-input {
  border-color: hsl(var(--input));
}
.border-muted {
  border-color: hsl(var(--muted));
}
.border-muted-foreground\/10 {
  border-color: hsl(var(--muted-foreground) / 0.1);
}
.border-muted-foreground\/20 {
  border-color: hsl(var(--muted-foreground) / 0.2);
}
.border-orange-500\/20 {
  border-color: rgb(249 115 22 / 0.2);
}
.border-orange-500\/30 {
  border-color: rgb(249 115 22 / 0.3);
}
.border-pink-500\/30 {
  border-color: rgb(236 72 153 / 0.3);
}
.border-primary {
  border-color: hsl(var(--primary));
}
.border-primary\/10 {
  border-color: hsl(var(--primary) / 0.1);
}
.border-primary\/20 {
  border-color: hsl(var(--primary) / 0.2);
}
.border-primary\/30 {
  border-color: hsl(var(--primary) / 0.3);
}
.border-primary\/50 {
  border-color: hsl(var(--primary) / 0.5);
}
.border-purple-500\/30 {
  border-color: rgb(168 85 247 / 0.3);
}
.border-red-500\/30 {
  border-color: rgb(239 68 68 / 0.3);
}
.border-secondary\/20 {
  border-color: hsl(var(--secondary) / 0.2);
}
.border-teal-100 {
  --tw-border-opacity: 1;
  border-color: rgb(204 251 241 / var(--tw-border-opacity, 1));
}
.border-teal-500 {
  --tw-border-opacity: 1;
  border-color: rgb(20 184 166 / var(--tw-border-opacity, 1));
}
.border-transparent {
  border-color: transparent;
}
.border-white {
  --tw-border-opacity: 1;
  border-color: rgb(255 255 255 / var(--tw-border-opacity, 1));
}
.border-white\/10 {
  border-color: rgb(255 255 255 / 0.1);
}
.border-white\/20 {
  border-color: rgb(255 255 255 / 0.2);
}
.border-white\/30 {
  border-color: rgb(255 255 255 / 0.3);
}
.border-white\/80 {
  border-color: rgb(255 255 255 / 0.8);
}
.border-zinc-100 {
  --tw-border-opacity: 1;
  border-color: rgb(244 244 245 / var(--tw-border-opacity, 1));
}
.border-zinc-200 {
  --tw-border-opacity: 1;
  border-color: rgb(228 228 231 / var(--tw-border-opacity, 1));
}
.border-zinc-300 {
  --tw-border-opacity: 1;
  border-color: rgb(212 212 216 / var(--tw-border-opacity, 1));
}
.border-b-foreground\/90 {
  border-bottom-color: hsl(var(--foreground) / 0.9);
}
.border-b-primary\/30 {
  border-bottom-color: hsl(var(--primary) / 0.3);
}
.border-l-primary\/30 {
  border-left-color: hsl(var(--primary) / 0.3);
}
.border-r-primary\/50 {
  border-right-color: hsl(var(--primary) / 0.5);
}
.border-r-transparent {
  border-right-color: transparent;
}
.border-t-foreground\/90 {
  border-top-color: hsl(var(--foreground) / 0.9);
}
.border-t-primary {
  border-top-color: hsl(var(--primary));
}
.border-t-transparent {
  border-top-color: transparent;
}
.bg-accent {
  background-color: hsl(var(--accent));
}
.bg-background {
  background-color: hsl(var(--background));
}
.bg-background\/40 {
  background-color: hsl(var(--background) / 0.4);
}
.bg-background\/50 {
  background-color: hsl(var(--background) / 0.5);
}
.bg-background\/60 {
  background-color: hsl(var(--background) / 0.6);
}
.bg-background\/80 {
  background-color: hsl(var(--background) / 0.8);
}
.bg-background\/95 {
  background-color: hsl(var(--background) / 0.95);
}
.bg-black {
  --tw-bg-opacity: 1;
  background-color: rgb(0 0 0 / var(--tw-bg-opacity, 1));
}
.bg-black\/0 {
  background-color: rgb(0 0 0 / 0);
}
.bg-black\/20 {
  background-color: rgb(0 0 0 / 0.2);
}
.bg-black\/50 {
  background-color: rgb(0 0 0 / 0.5);
}
.bg-black\/60 {
  background-color: rgb(0 0 0 / 0.6);
}
.bg-black\/80 {
  background-color: rgb(0 0 0 / 0.8);
}
.bg-black\/90 {
  background-color: rgb(0 0 0 / 0.9);
}
.bg-black\/95 {
  background-color: rgb(0 0 0 / 0.95);
}
.bg-blue-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(219 234 254 / var(--tw-bg-opacity, 1));
}
.bg-blue-400\/50 {
  background-color: rgb(96 165 250 / 0.5);
}
.bg-blue-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));
}
.bg-blue-500\/10 {
  background-color: rgb(59 130 246 / 0.1);
}
.bg-blue-500\/20 {
  background-color: rgb(59 130 246 / 0.2);
}
.bg-border {
  background-color: hsl(var(--border));
}
.bg-card {
  background-color: hsl(var(--card));
}
.bg-card\/95 {
  background-color: hsl(var(--card) / 0.95);
}
.bg-current {
  background-color: currentColor;
}
.bg-cyan-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(6 182 212 / var(--tw-bg-opacity, 1));
}
.bg-destructive {
  background-color: hsl(var(--destructive));
}
.bg-destructive\/10 {
  background-color: hsl(var(--destructive) / 0.1);
}
.bg-destructive\/20 {
  background-color: hsl(var(--destructive) / 0.2);
}
.bg-emerald-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(209 250 229 / var(--tw-bg-opacity, 1));
}
.bg-emerald-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(16 185 129 / var(--tw-bg-opacity, 1));
}
.bg-foreground\/90 {
  background-color: hsl(var(--foreground) / 0.9);
}
.bg-gray-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}
.bg-gray-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(229 231 235 / var(--tw-bg-opacity, 1));
}
.bg-gray-200\/50 {
  background-color: rgb(229 231 235 / 0.5);
}
.bg-gray-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}
.bg-gray-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(107 114 128 / var(--tw-bg-opacity, 1));
}
.bg-gray-500\/20 {
  background-color: rgb(107 114 128 / 0.2);
}
.bg-green-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(220 252 231 / var(--tw-bg-opacity, 1));
}
.bg-green-400\/40 {
  background-color: rgb(74 222 128 / 0.4);
}
.bg-green-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(34 197 94 / var(--tw-bg-opacity, 1));
}
.bg-green-500\/10 {
  background-color: rgb(34 197 94 / 0.1);
}
.bg-green-500\/20 {
  background-color: rgb(34 197 94 / 0.2);
}
.bg-green-500\/40 {
  background-color: rgb(34 197 94 / 0.4);
}
.bg-muted {
  background-color: hsl(var(--muted));
}
.bg-muted-foreground\/30 {
  background-color: hsl(var(--muted-foreground) / 0.3);
}
.bg-muted\/20 {
  background-color: hsl(var(--muted) / 0.2);
}
.bg-muted\/30 {
  background-color: hsl(var(--muted) / 0.3);
}
.bg-muted\/50 {
  background-color: hsl(var(--muted) / 0.5);
}
.bg-muted\/60 {
  background-color: hsl(var(--muted) / 0.6);
}
.bg-orange-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 237 213 / var(--tw-bg-opacity, 1));
}
.bg-popover {
  background-color: hsl(var(--popover));
}
.bg-primary {
  background-color: hsl(var(--primary));
}
.bg-primary\/10 {
  background-color: hsl(var(--primary) / 0.1);
}
.bg-primary\/20 {
  background-color: hsl(var(--primary) / 0.2);
}
.bg-primary\/30 {
  background-color: hsl(var(--primary) / 0.3);
}
.bg-primary\/40 {
  background-color: hsl(var(--primary) / 0.4);
}
.bg-primary\/5 {
  background-color: hsl(var(--primary) / 0.05);
}
.bg-primary\/50 {
  background-color: hsl(var(--primary) / 0.5);
}
.bg-primary\/60 {
  background-color: hsl(var(--primary) / 0.6);
}
.bg-primary\/80 {
  background-color: hsl(var(--primary) / 0.8);
}
.bg-purple-400\/40 {
  background-color: rgb(192 132 252 / 0.4);
}
.bg-purple-500\/10 {
  background-color: rgb(168 85 247 / 0.1);
}
.bg-purple-500\/20 {
  background-color: rgb(168 85 247 / 0.2);
}
.bg-red-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(254 226 226 / var(--tw-bg-opacity, 1));
}
.bg-red-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));
}
.bg-red-500\/20 {
  background-color: rgb(239 68 68 / 0.2);
}
.bg-secondary {
  background-color: hsl(var(--secondary));
}
.bg-secondary\/10 {
  background-color: hsl(var(--secondary) / 0.1);
}
.bg-secondary\/20 {
  background-color: hsl(var(--secondary) / 0.2);
}
.bg-secondary\/30 {
  background-color: hsl(var(--secondary) / 0.3);
}
.bg-secondary\/40 {
  background-color: hsl(var(--secondary) / 0.4);
}
.bg-secondary\/5 {
  background-color: hsl(var(--secondary) / 0.05);
}
.bg-secondary\/50 {
  background-color: hsl(var(--secondary) / 0.5);
}
.bg-teal-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(240 253 250 / var(--tw-bg-opacity, 1));
}
.bg-teal-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(20 184 166 / var(--tw-bg-opacity, 1));
}
.bg-transparent {
  background-color: transparent;
}
.bg-white {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}
.bg-white\/10 {
  background-color: rgb(255 255 255 / 0.1);
}
.bg-white\/20 {
  background-color: rgb(255 255 255 / 0.2);
}
.bg-white\/30 {
  background-color: rgb(255 255 255 / 0.3);
}
.bg-white\/5 {
  background-color: rgb(255 255 255 / 0.05);
}
.bg-white\/60 {
  background-color: rgb(255 255 255 / 0.6);
}
.bg-white\/80 {
  background-color: rgb(255 255 255 / 0.8);
}
.bg-white\/90 {
  background-color: rgb(255 255 255 / 0.9);
}
.bg-white\/95 {
  background-color: rgb(255 255 255 / 0.95);
}
.bg-yellow-500 {
  --tw-bg-opacity: 1;
  background-color: rgb(234 179 8 / var(--tw-bg-opacity, 1));
}
.bg-zinc-100 {
  --tw-bg-opacity: 1;
  background-color: rgb(244 244 245 / var(--tw-bg-opacity, 1));
}
.bg-zinc-200 {
  --tw-bg-opacity: 1;
  background-color: rgb(228 228 231 / var(--tw-bg-opacity, 1));
}
.bg-zinc-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(250 250 250 / var(--tw-bg-opacity, 1));
}
.bg-zinc-800 {
  --tw-bg-opacity: 1;
  background-color: rgb(39 39 42 / var(--tw-bg-opacity, 1));
}
.\!bg-gradient-to-r {
  background-image: linear-gradient(to right, var(--tw-gradient-stops)) !important;
}
.bg-\[radial-gradient\(ellipse_at_top_right\2c _var\(--tw-gradient-stops\)\)\] {
  background-image: radial-gradient(ellipse at top right, var(--tw-gradient-stops));
}
.bg-gradient-card {
  background-image: linear-gradient(135deg, hsl(var(--card)) 0%, hsl(var(--card) / 0.95) 100%);
}
.bg-gradient-primary {
  background-image: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--primary) / 0.8) 100%);
}
.bg-gradient-soft {
  background-image: linear-gradient(135deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
}
.bg-gradient-to-b {
  background-image: linear-gradient(to bottom, var(--tw-gradient-stops));
}
.bg-gradient-to-bl {
  background-image: linear-gradient(to bottom left, var(--tw-gradient-stops));
}
.bg-gradient-to-br {
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}
.bg-gradient-to-r {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}
.bg-gradient-to-t {
  background-image: linear-gradient(to top, var(--tw-gradient-stops));
}
.bg-gradient-to-tr {
  background-image: linear-gradient(to top right, var(--tw-gradient-stops));
}
.from-amber-500\/10 {
  --tw-gradient-from: rgb(245 158 11 / 0.1) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(245 158 11 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-amber-500\/15 {
  --tw-gradient-from: rgb(245 158 11 / 0.15) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(245 158 11 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-amber-500\/20 {
  --tw-gradient-from: rgb(245 158 11 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(245 158 11 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-amber-500\/90 {
  --tw-gradient-from: rgb(245 158 11 / 0.9) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(245 158 11 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-background {
  --tw-gradient-from: hsl(var(--background)) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--background) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-background\/80 {
  --tw-gradient-from: hsl(var(--background) / 0.8) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--background) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-background\/85 {
  --tw-gradient-from: hsl(var(--background) / 0.85) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--background) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-background\/90 {
  --tw-gradient-from: hsl(var(--background) / 0.9) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--background) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-background\/95 {
  --tw-gradient-from: hsl(var(--background) / 0.95) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--background) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-black\/20 {
  --tw-gradient-from: rgb(0 0 0 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-black\/5 {
  --tw-gradient-from: rgb(0 0 0 / 0.05) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-black\/60 {
  --tw-gradient-from: rgb(0 0 0 / 0.6) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-black\/80 {
  --tw-gradient-from: rgb(0 0 0 / 0.8) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-blue-500 {
  --tw-gradient-from: #3b82f6 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(59 130 246 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-blue-500\/15 {
  --tw-gradient-from: rgb(59 130 246 / 0.15) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(59 130 246 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-blue-500\/20 {
  --tw-gradient-from: rgb(59 130 246 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(59 130 246 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-border {
  --tw-gradient-from: hsl(var(--border)) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--border) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-emerald-600 {
  --tw-gradient-from: #059669 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(5 150 105 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-foreground {
  --tw-gradient-from: hsl(var(--foreground)) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--foreground) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-gray-300 {
  --tw-gradient-from: #d1d5db var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(209 213 219 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-gray-50 {
  --tw-gradient-from: #f9fafb var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(249 250 251 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-green-500\/10 {
  --tw-gradient-from: rgb(34 197 94 / 0.1) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(34 197 94 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-green-500\/15 {
  --tw-gradient-from: rgb(34 197 94 / 0.15) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(34 197 94 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-green-500\/20 {
  --tw-gradient-from: rgb(34 197 94 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(34 197 94 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-green-500\/5 {
  --tw-gradient-from: rgb(34 197 94 / 0.05) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(34 197 94 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-green-600 {
  --tw-gradient-from: #16a34a var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(22 163 74 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-indigo-500\/20 {
  --tw-gradient-from: rgb(99 102 241 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(99 102 241 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-muted\/20 {
  --tw-gradient-from: hsl(var(--muted) / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--muted) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-muted\/50 {
  --tw-gradient-from: hsl(var(--muted) / 0.5) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--muted) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-muted\/90 {
  --tw-gradient-from: hsl(var(--muted) / 0.9) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--muted) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-orange-500 {
  --tw-gradient-from: #f97316 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(249 115 22 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-orange-500\/10 {
  --tw-gradient-from: rgb(249 115 22 / 0.1) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(249 115 22 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-orange-500\/15 {
  --tw-gradient-from: rgb(249 115 22 / 0.15) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(249 115 22 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-orange-500\/20 {
  --tw-gradient-from: rgb(249 115 22 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(249 115 22 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-orange-500\/90 {
  --tw-gradient-from: rgb(249 115 22 / 0.9) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(249 115 22 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-pink-500\/20 {
  --tw-gradient-from: rgb(236 72 153 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(236 72 153 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-primary {
  --tw-gradient-from: hsl(var(--primary)) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--primary) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-primary\/0 {
  --tw-gradient-from: hsl(var(--primary) / 0) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--primary) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-primary\/10 {
  --tw-gradient-from: hsl(var(--primary) / 0.1) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--primary) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-primary\/15 {
  --tw-gradient-from: hsl(var(--primary) / 0.15) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--primary) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-primary\/20 {
  --tw-gradient-from: hsl(var(--primary) / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--primary) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-primary\/25 {
  --tw-gradient-from: hsl(var(--primary) / 0.25) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--primary) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-primary\/30 {
  --tw-gradient-from: hsl(var(--primary) / 0.3) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--primary) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-primary\/5 {
  --tw-gradient-from: hsl(var(--primary) / 0.05) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--primary) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-primary\/50 {
  --tw-gradient-from: hsl(var(--primary) / 0.5) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--primary) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-primary\/60 {
  --tw-gradient-from: hsl(var(--primary) / 0.6) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--primary) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-primary\/70 {
  --tw-gradient-from: hsl(var(--primary) / 0.7) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--primary) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-primary\/\[0\.02\] {
  --tw-gradient-from: hsl(var(--primary) / 0.02) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--primary) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-purple-500\/20 {
  --tw-gradient-from: rgb(168 85 247 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(168 85 247 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-red-500 {
  --tw-gradient-from: #ef4444 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(239 68 68 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-red-500\/15 {
  --tw-gradient-from: rgb(239 68 68 / 0.15) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(239 68 68 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-red-500\/20 {
  --tw-gradient-from: rgb(239 68 68 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(239 68 68 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-secondary {
  --tw-gradient-from: hsl(var(--secondary)) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--secondary) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-secondary\/10 {
  --tw-gradient-from: hsl(var(--secondary) / 0.1) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--secondary) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-secondary\/15 {
  --tw-gradient-from: hsl(var(--secondary) / 0.15) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--secondary) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-secondary\/20 {
  --tw-gradient-from: hsl(var(--secondary) / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--secondary) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-slate-50\/50 {
  --tw-gradient-from: rgb(248 250 252 / 0.5) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(248 250 252 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-slate-50\/80 {
  --tw-gradient-from: rgb(248 250 252 / 0.8) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(248 250 252 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-teal-50 {
  --tw-gradient-from: #f0fdfa var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(240 253 250 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-transparent {
  --tw-gradient-from: transparent var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(0 0 0 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-white\/20 {
  --tw-gradient-from: rgb(255 255 255 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-white\/30 {
  --tw-gradient-from: rgb(255 255 255 / 0.3) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-white\/5 {
  --tw-gradient-from: rgb(255 255 255 / 0.05) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-white\/50 {
  --tw-gradient-from: rgb(255 255 255 / 0.5) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(255 255 255 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-yellow-400 {
  --tw-gradient-from: #facc15 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(250 204 21 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-yellow-400\/20 {
  --tw-gradient-from: rgb(250 204 21 / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(250 204 21 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.from-zinc-100 {
  --tw-gradient-from: #f4f4f5 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(244 244 245 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}
.via-background\/85 {
  --tw-gradient-to: hsl(var(--background) / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), hsl(var(--background) / 0.85) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-background\/90 {
  --tw-gradient-to: hsl(var(--background) / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), hsl(var(--background) / 0.9) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-background\/95 {
  --tw-gradient-to: hsl(var(--background) / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), hsl(var(--background) / 0.95) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-black\/20 {
  --tw-gradient-to: rgb(0 0 0 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(0 0 0 / 0.2) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-black\/60 {
  --tw-gradient-to: rgb(0 0 0 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(0 0 0 / 0.6) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-blue-500 {
  --tw-gradient-to: rgb(59 130 246 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #3b82f6 var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-blue-500\/10 {
  --tw-gradient-to: rgb(59 130 246 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(59 130 246 / 0.1) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-blue-500\/20 {
  --tw-gradient-to: rgb(59 130 246 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(59 130 246 / 0.2) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-blue-500\/5 {
  --tw-gradient-to: rgb(59 130 246 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(59 130 246 / 0.05) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-border {
  --tw-gradient-to: hsl(var(--border) / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), hsl(var(--border)) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-border\/50 {
  --tw-gradient-to: hsl(var(--border) / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), hsl(var(--border) / 0.5) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-cyan-500\/10 {
  --tw-gradient-to: rgb(6 182 212 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(6 182 212 / 0.1) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-emerald-500 {
  --tw-gradient-to: rgb(16 185 129 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #10b981 var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-foreground\/90 {
  --tw-gradient-to: hsl(var(--foreground) / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), hsl(var(--foreground) / 0.9) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-green-500 {
  --tw-gradient-to: rgb(34 197 94 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #22c55e var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-green-500\/10 {
  --tw-gradient-to: rgb(34 197 94 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(34 197 94 / 0.1) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-green-500\/5 {
  --tw-gradient-to: rgb(34 197 94 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(34 197 94 / 0.05) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-muted\/20 {
  --tw-gradient-to: hsl(var(--muted) / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), hsl(var(--muted) / 0.2) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-orange-400 {
  --tw-gradient-to: rgb(251 146 60 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #fb923c var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-pink-500 {
  --tw-gradient-to: rgb(236 72 153 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #ec4899 var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-pink-500\/10 {
  --tw-gradient-to: rgb(236 72 153 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(236 72 153 / 0.1) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-primary {
  --tw-gradient-to: hsl(var(--primary) / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), hsl(var(--primary)) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-primary\/10 {
  --tw-gradient-to: hsl(var(--primary) / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), hsl(var(--primary) / 0.1) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-primary\/15 {
  --tw-gradient-to: hsl(var(--primary) / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), hsl(var(--primary) / 0.15) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-primary\/20 {
  --tw-gradient-to: hsl(var(--primary) / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), hsl(var(--primary) / 0.2) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-primary\/30 {
  --tw-gradient-to: hsl(var(--primary) / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), hsl(var(--primary) / 0.3) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-primary\/40 {
  --tw-gradient-to: hsl(var(--primary) / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), hsl(var(--primary) / 0.4) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-primary\/5 {
  --tw-gradient-to: hsl(var(--primary) / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), hsl(var(--primary) / 0.05) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-primary\/60 {
  --tw-gradient-to: hsl(var(--primary) / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), hsl(var(--primary) / 0.6) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-primary\/80 {
  --tw-gradient-to: hsl(var(--primary) / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), hsl(var(--primary) / 0.8) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-primary\/\[0\.01\] {
  --tw-gradient-to: hsl(var(--primary) / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), hsl(var(--primary) / 0.01) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-purple-500\/10 {
  --tw-gradient-to: rgb(168 85 247 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(168 85 247 / 0.1) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-secondary {
  --tw-gradient-to: hsl(var(--secondary) / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), hsl(var(--secondary)) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-secondary\/10 {
  --tw-gradient-to: hsl(var(--secondary) / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), hsl(var(--secondary) / 0.1) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-secondary\/5 {
  --tw-gradient-to: hsl(var(--secondary) / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), hsl(var(--secondary) / 0.05) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-secondary\/\[0\.01\] {
  --tw-gradient-to: hsl(var(--secondary) / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), hsl(var(--secondary) / 0.01) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-slate-50\/40 {
  --tw-gradient-to: rgb(248 250 252 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(248 250 252 / 0.4) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-teal-500 {
  --tw-gradient-to: rgb(20 184 166 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #14b8a6 var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-teal-500\/10 {
  --tw-gradient-to: rgb(20 184 166 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(20 184 166 / 0.1) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-teal-500\/5 {
  --tw-gradient-to: rgb(20 184 166 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(20 184 166 / 0.05) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-transparent {
  --tw-gradient-to: rgb(0 0 0 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), transparent var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-white\/10 {
  --tw-gradient-to: rgb(255 255 255 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(255 255 255 / 0.1) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-white\/20 {
  --tw-gradient-to: rgb(255 255 255 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(255 255 255 / 0.2) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-white\/30 {
  --tw-gradient-to: rgb(255 255 255 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(255 255 255 / 0.3) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-yellow-500 {
  --tw-gradient-to: rgb(234 179 8 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), #eab308 var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.via-yellow-500\/10 {
  --tw-gradient-to: rgb(234 179 8 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(234 179 8 / 0.1) var(--tw-gradient-via-position), var(--tw-gradient-to);
}
.to-accent {
  --tw-gradient-to: hsl(var(--accent)) var(--tw-gradient-to-position);
}
.to-accent\/10 {
  --tw-gradient-to: hsl(var(--accent) / 0.1) var(--tw-gradient-to-position);
}
.to-accent\/20 {
  --tw-gradient-to: hsl(var(--accent) / 0.2) var(--tw-gradient-to-position);
}
.to-background {
  --tw-gradient-to: hsl(var(--background)) var(--tw-gradient-to-position);
}
.to-background\/80 {
  --tw-gradient-to: hsl(var(--background) / 0.8) var(--tw-gradient-to-position);
}
.to-background\/85 {
  --tw-gradient-to: hsl(var(--background) / 0.85) var(--tw-gradient-to-position);
}
.to-background\/95 {
  --tw-gradient-to: hsl(var(--background) / 0.95) var(--tw-gradient-to-position);
}
.to-black\/40 {
  --tw-gradient-to: rgb(0 0 0 / 0.4) var(--tw-gradient-to-position);
}
.to-black\/5 {
  --tw-gradient-to: rgb(0 0 0 / 0.05) var(--tw-gradient-to-position);
}
.to-blue-500\/5 {
  --tw-gradient-to: rgb(59 130 246 / 0.05) var(--tw-gradient-to-position);
}
.to-border {
  --tw-gradient-to: hsl(var(--border)) var(--tw-gradient-to-position);
}
.to-cyan-500\/15 {
  --tw-gradient-to: rgb(6 182 212 / 0.15) var(--tw-gradient-to-position);
}
.to-cyan-500\/20 {
  --tw-gradient-to: rgb(6 182 212 / 0.2) var(--tw-gradient-to-position);
}
.to-emerald-500\/15 {
  --tw-gradient-to: rgb(16 185 129 / 0.15) var(--tw-gradient-to-position);
}
.to-emerald-500\/20 {
  --tw-gradient-to: rgb(16 185 129 / 0.2) var(--tw-gradient-to-position);
}
.to-foreground {
  --tw-gradient-to: hsl(var(--foreground)) var(--tw-gradient-to-position);
}
.to-foreground\/70 {
  --tw-gradient-to: hsl(var(--foreground) / 0.7) var(--tw-gradient-to-position);
}
.to-foreground\/80 {
  --tw-gradient-to: hsl(var(--foreground) / 0.8) var(--tw-gradient-to-position);
}
.to-gray-100 {
  --tw-gradient-to: #f3f4f6 var(--tw-gradient-to-position);
}
.to-green-500 {
  --tw-gradient-to: #22c55e var(--tw-gradient-to-position);
}
.to-indigo-500\/20 {
  --tw-gradient-to: rgb(99 102 241 / 0.2) var(--tw-gradient-to-position);
}
.to-indigo-600 {
  --tw-gradient-to: #4f46e5 var(--tw-gradient-to-position);
}
.to-muted {
  --tw-gradient-to: hsl(var(--muted)) var(--tw-gradient-to-position);
}
.to-muted\/70 {
  --tw-gradient-to: hsl(var(--muted) / 0.7) var(--tw-gradient-to-position);
}
.to-orange-400\/20 {
  --tw-gradient-to: rgb(251 146 60 / 0.2) var(--tw-gradient-to-position);
}
.to-orange-500\/10 {
  --tw-gradient-to: rgb(249 115 22 / 0.1) var(--tw-gradient-to-position);
}
.to-orange-500\/15 {
  --tw-gradient-to: rgb(249 115 22 / 0.15) var(--tw-gradient-to-position);
}
.to-orange-500\/20 {
  --tw-gradient-to: rgb(249 115 22 / 0.2) var(--tw-gradient-to-position);
}
.to-orange-500\/90 {
  --tw-gradient-to: rgb(249 115 22 / 0.9) var(--tw-gradient-to-position);
}
.to-pink-500\/20 {
  --tw-gradient-to: rgb(236 72 153 / 0.2) var(--tw-gradient-to-position);
}
.to-primary\/0 {
  --tw-gradient-to: hsl(var(--primary) / 0) var(--tw-gradient-to-position);
}
.to-primary\/10 {
  --tw-gradient-to: hsl(var(--primary) / 0.1) var(--tw-gradient-to-position);
}
.to-primary\/20 {
  --tw-gradient-to: hsl(var(--primary) / 0.2) var(--tw-gradient-to-position);
}
.to-primary\/5 {
  --tw-gradient-to: hsl(var(--primary) / 0.05) var(--tw-gradient-to-position);
}
.to-primary\/50 {
  --tw-gradient-to: hsl(var(--primary) / 0.5) var(--tw-gradient-to-position);
}
.to-primary\/60 {
  --tw-gradient-to: hsl(var(--primary) / 0.6) var(--tw-gradient-to-position);
}
.to-primary\/70 {
  --tw-gradient-to: hsl(var(--primary) / 0.7) var(--tw-gradient-to-position);
}
.to-primary\/90 {
  --tw-gradient-to: hsl(var(--primary) / 0.9) var(--tw-gradient-to-position);
}
.to-primary\/\[0\.02\] {
  --tw-gradient-to: hsl(var(--primary) / 0.02) var(--tw-gradient-to-position);
}
.to-purple-500 {
  --tw-gradient-to: #a855f7 var(--tw-gradient-to-position);
}
.to-purple-500\/20 {
  --tw-gradient-to: rgb(168 85 247 / 0.2) var(--tw-gradient-to-position);
}
.to-purple-600 {
  --tw-gradient-to: #9333ea var(--tw-gradient-to-position);
}
.to-red-400 {
  --tw-gradient-to: #f87171 var(--tw-gradient-to-position);
}
.to-red-500\/10 {
  --tw-gradient-to: rgb(239 68 68 / 0.1) var(--tw-gradient-to-position);
}
.to-red-500\/15 {
  --tw-gradient-to: rgb(239 68 68 / 0.15) var(--tw-gradient-to-position);
}
.to-red-500\/20 {
  --tw-gradient-to: rgb(239 68 68 / 0.2) var(--tw-gradient-to-position);
}
.to-red-500\/90 {
  --tw-gradient-to: rgb(239 68 68 / 0.9) var(--tw-gradient-to-position);
}
.to-secondary {
  --tw-gradient-to: hsl(var(--secondary)) var(--tw-gradient-to-position);
}
.to-secondary\/10 {
  --tw-gradient-to: hsl(var(--secondary) / 0.1) var(--tw-gradient-to-position);
}
.to-secondary\/20 {
  --tw-gradient-to: hsl(var(--secondary) / 0.2) var(--tw-gradient-to-position);
}
.to-secondary\/30 {
  --tw-gradient-to: hsl(var(--secondary) / 0.3) var(--tw-gradient-to-position);
}
.to-secondary\/5 {
  --tw-gradient-to: hsl(var(--secondary) / 0.05) var(--tw-gradient-to-position);
}
.to-secondary\/\[0\.02\] {
  --tw-gradient-to: hsl(var(--secondary) / 0.02) var(--tw-gradient-to-position);
}
.to-slate-100\/50 {
  --tw-gradient-to: rgb(241 245 249 / 0.5) var(--tw-gradient-to-position);
}
.to-teal-500 {
  --tw-gradient-to: #14b8a6 var(--tw-gradient-to-position);
}
.to-teal-500\/20 {
  --tw-gradient-to: rgb(20 184 166 / 0.2) var(--tw-gradient-to-position);
}
.to-teal-600 {
  --tw-gradient-to: #0d9488 var(--tw-gradient-to-position);
}
.to-transparent {
  --tw-gradient-to: transparent var(--tw-gradient-to-position);
}
.to-white\/10 {
  --tw-gradient-to: rgb(255 255 255 / 0.1) var(--tw-gradient-to-position);
}
.to-zinc-50 {
  --tw-gradient-to: #fafafa var(--tw-gradient-to-position);
}
.bg-clip-text {
  -webkit-background-clip: text;
          background-clip: text;
}
.fill-current {
  fill: currentColor;
}
.stroke-current {
  stroke: currentColor;
}
.stroke-zinc-500 {
  stroke: #71717a;
}
.object-contain {
  -o-object-fit: contain;
     object-fit: contain;
}
.object-cover {
  -o-object-fit: cover;
     object-fit: cover;
}
.p-0 {
  padding: 0px;
}
.p-0\.5 {
  padding: 0.125rem;
}
.p-1 {
  padding: 0.25rem;
}
.p-1\.5 {
  padding: 0.375rem;
}
.p-10 {
  padding: 2.5rem;
}
.p-2 {
  padding: 0.5rem;
}
.p-3 {
  padding: 0.75rem;
}
.p-4 {
  padding: 1rem;
}
.p-5 {
  padding: 1.25rem;
}
.p-6 {
  padding: 1.5rem;
}
.p-8 {
  padding: 2rem;
}
.px-1 {
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}
.px-1\.5 {
  padding-left: 0.375rem;
  padding-right: 0.375rem;
}
.px-10 {
  padding-left: 2.5rem;
  padding-right: 2.5rem;
}
.px-2 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.px-2\.5 {
  padding-left: 0.625rem;
  padding-right: 0.625rem;
}
.px-3 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}
.px-4 {
  padding-left: 1rem;
  padding-right: 1rem;
}
.px-5 {
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}
.px-6 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}
.px-8 {
  padding-left: 2rem;
  padding-right: 2rem;
}
.py-0\.5 {
  padding-top: 0.125rem;
  padding-bottom: 0.125rem;
}
.py-1 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}
.py-1\.5 {
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}
.py-12 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}
.py-16 {
  padding-top: 4rem;
  padding-bottom: 4rem;
}
.py-2 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.py-2\.5 {
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
}
.py-20 {
  padding-top: 5rem;
  padding-bottom: 5rem;
}
.py-3 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}
.py-4 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}
.py-5 {
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
}
.py-6 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}
.py-8 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}
.pb-16 {
  padding-bottom: 4rem;
}
.pb-2 {
  padding-bottom: 0.5rem;
}
.pb-3 {
  padding-bottom: 0.75rem;
}
.pb-4 {
  padding-bottom: 1rem;
}
.pb-5 {
  padding-bottom: 1.25rem;
}
.pl-10 {
  padding-left: 2.5rem;
}
.pl-12 {
  padding-left: 3rem;
}
.pl-2 {
  padding-left: 0.5rem;
}
.pl-3\.5 {
  padding-left: 0.875rem;
}
.pl-4 {
  padding-left: 1rem;
}
.pl-6 {
  padding-left: 1.5rem;
}
.pl-7 {
  padding-left: 1.75rem;
}
.pl-8 {
  padding-left: 2rem;
}
.pr-12 {
  padding-right: 3rem;
}
.pr-2 {
  padding-right: 0.5rem;
}
.pr-20 {
  padding-right: 5rem;
}
.pr-4 {
  padding-right: 1rem;
}
.pr-6 {
  padding-right: 1.5rem;
}
.pt-0 {
  padding-top: 0px;
}
.pt-10 {
  padding-top: 2.5rem;
}
.pt-12 {
  padding-top: 3rem;
}
.pt-16 {
  padding-top: 4rem;
}
.pt-2 {
  padding-top: 0.5rem;
}
.pt-3 {
  padding-top: 0.75rem;
}
.pt-4 {
  padding-top: 1rem;
}
.pt-6 {
  padding-top: 1.5rem;
}
.pt-8 {
  padding-top: 2rem;
}
.text-left {
  text-align: left;
}
.text-center {
  text-align: center;
}
.font-mono {
  font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}
.font-serif {
  font-family: ui-serif, Georgia, Cambria, "Times New Roman", Times, serif;
}
.text-2xl {
  font-size: 1.5rem;
  line-height: 2rem;
}
.text-3xl {
  font-size: 1.875rem;
  line-height: 2.25rem;
}
.text-4xl {
  font-size: 2rem;
  line-height: 2.5rem;
}
.text-6xl {
  font-size: 3.75rem;
  line-height: 1;
}
.text-base {
  font-size: 1rem;
  line-height: 1.75rem;
}
.text-lg {
  font-size: 1.125rem;
  line-height: 1.75rem;
}
.text-sm {
  font-size: 0.875rem;
  line-height: 1.5rem;
}
.text-xl {
  font-size: 1.25rem;
  line-height: 2rem;
}
.text-xs {
  font-size: 0.8125rem;
  line-height: 1.5rem;
}
.font-black {
  font-weight: 900;
}
.font-bold {
  font-weight: 700;
}
.font-light {
  font-weight: 300;
}
.font-medium {
  font-weight: 500;
}
.font-semibold {
  font-weight: 600;
}
.uppercase {
  text-transform: uppercase;
}
.capitalize {
  text-transform: capitalize;
}
.italic {
  font-style: italic;
}
.tabular-nums {
  --tw-numeric-spacing: tabular-nums;
  font-variant-numeric: var(--tw-ordinal) var(--tw-slashed-zero) var(--tw-numeric-figure) var(--tw-numeric-spacing) var(--tw-numeric-fraction);
}
.leading-7 {
  line-height: 1.75rem;
}
.leading-8 {
  line-height: 2rem;
}
.leading-9 {
  line-height: 2.25rem;
}
.leading-\[5rem\] {
  line-height: 5rem;
}
.leading-none {
  line-height: 1;
}
.leading-relaxed {
  line-height: 1.625;
}
.leading-tight {
  line-height: 1.25;
}
.tracking-\[-0\.02em\] {
  letter-spacing: -0.02em;
}
.tracking-normal {
  letter-spacing: 0em;
}
.tracking-tight {
  letter-spacing: -0.025em;
}
.tracking-wide {
  letter-spacing: 0.025em;
}
.tracking-widest {
  letter-spacing: 0.1em;
}
.text-accent-foreground {
  color: hsl(var(--accent-foreground));
}
.text-amber-700 {
  --tw-text-opacity: 1;
  color: rgb(180 83 9 / var(--tw-text-opacity, 1));
}
.text-background {
  color: hsl(var(--background));
}
.text-blue-500 {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity, 1));
}
.text-blue-600 {
  --tw-text-opacity: 1;
  color: rgb(37 99 235 / var(--tw-text-opacity, 1));
}
.text-blue-700 {
  --tw-text-opacity: 1;
  color: rgb(29 78 216 / var(--tw-text-opacity, 1));
}
.text-blue-800 {
  --tw-text-opacity: 1;
  color: rgb(30 64 175 / var(--tw-text-opacity, 1));
}
.text-border {
  color: hsl(var(--border));
}
.text-destructive {
  color: hsl(var(--destructive));
}
.text-destructive-foreground {
  color: hsl(var(--destructive-foreground));
}
.text-emerald-700 {
  --tw-text-opacity: 1;
  color: rgb(4 120 87 / var(--tw-text-opacity, 1));
}
.text-foreground {
  color: hsl(var(--foreground));
}
.text-foreground\/60 {
  color: hsl(var(--foreground) / 0.6);
}
.text-foreground\/70 {
  color: hsl(var(--foreground) / 0.7);
}
.text-foreground\/75 {
  color: hsl(var(--foreground) / 0.75);
}
.text-foreground\/80 {
  color: hsl(var(--foreground) / 0.8);
}
.text-foreground\/85 {
  color: hsl(var(--foreground) / 0.85);
}
.text-foreground\/90 {
  color: hsl(var(--foreground) / 0.9);
}
.text-gray-400 {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}
.text-gray-500 {
  --tw-text-opacity: 1;
  color: rgb(107 114 128 / var(--tw-text-opacity, 1));
}
.text-gray-600 {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}
.text-gray-700 {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}
.text-gray-800 {
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity, 1));
}
.text-gray-900 {
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}
.text-green-500 {
  --tw-text-opacity: 1;
  color: rgb(34 197 94 / var(--tw-text-opacity, 1));
}
.text-green-600 {
  --tw-text-opacity: 1;
  color: rgb(22 163 74 / var(--tw-text-opacity, 1));
}
.text-green-700 {
  --tw-text-opacity: 1;
  color: rgb(21 128 61 / var(--tw-text-opacity, 1));
}
.text-green-800 {
  --tw-text-opacity: 1;
  color: rgb(22 101 52 / var(--tw-text-opacity, 1));
}
.text-muted-foreground {
  color: hsl(var(--muted-foreground));
}
.text-muted-foreground\/50 {
  color: hsl(var(--muted-foreground) / 0.5);
}
.text-muted-foreground\/70 {
  color: hsl(var(--muted-foreground) / 0.7);
}
.text-muted-foreground\/80 {
  color: hsl(var(--muted-foreground) / 0.8);
}
.text-orange-500 {
  --tw-text-opacity: 1;
  color: rgb(249 115 22 / var(--tw-text-opacity, 1));
}
.text-orange-700 {
  --tw-text-opacity: 1;
  color: rgb(194 65 12 / var(--tw-text-opacity, 1));
}
.text-orange-800 {
  --tw-text-opacity: 1;
  color: rgb(154 52 18 / var(--tw-text-opacity, 1));
}
.text-popover-foreground {
  color: hsl(var(--popover-foreground));
}
.text-primary {
  color: hsl(var(--primary));
}
.text-primary-foreground {
  color: hsl(var(--primary-foreground));
}
.text-primary\/20 {
  color: hsl(var(--primary) / 0.2);
}
.text-primary\/30 {
  color: hsl(var(--primary) / 0.3);
}
.text-primary\/60 {
  color: hsl(var(--primary) / 0.6);
}
.text-primary\/70 {
  color: hsl(var(--primary) / 0.7);
}
.text-primary\/80 {
  color: hsl(var(--primary) / 0.8);
}
.text-purple-500 {
  --tw-text-opacity: 1;
  color: rgb(168 85 247 / var(--tw-text-opacity, 1));
}
.text-red-500 {
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}
.text-red-600 {
  --tw-text-opacity: 1;
  color: rgb(220 38 38 / var(--tw-text-opacity, 1));
}
.text-red-700 {
  --tw-text-opacity: 1;
  color: rgb(185 28 28 / var(--tw-text-opacity, 1));
}
.text-red-800 {
  --tw-text-opacity: 1;
  color: rgb(153 27 27 / var(--tw-text-opacity, 1));
}
.text-secondary-foreground {
  color: hsl(var(--secondary-foreground));
}
.text-slate-700 {
  --tw-text-opacity: 1;
  color: rgb(51 65 85 / var(--tw-text-opacity, 1));
}
.text-teal-600 {
  --tw-text-opacity: 1;
  color: rgb(13 148 136 / var(--tw-text-opacity, 1));
}
.text-transparent {
  color: transparent;
}
.text-white {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}
.text-white\/70 {
  color: rgb(255 255 255 / 0.7);
}
.text-white\/80 {
  color: rgb(255 255 255 / 0.8);
}
.text-white\/90 {
  color: rgb(255 255 255 / 0.9);
}
.text-yellow-500 {
  --tw-text-opacity: 1;
  color: rgb(234 179 8 / var(--tw-text-opacity, 1));
}
.text-yellow-600 {
  --tw-text-opacity: 1;
  color: rgb(202 138 4 / var(--tw-text-opacity, 1));
}
.text-zinc-100 {
  --tw-text-opacity: 1;
  color: rgb(244 244 245 / var(--tw-text-opacity, 1));
}
.text-zinc-400 {
  --tw-text-opacity: 1;
  color: rgb(161 161 170 / var(--tw-text-opacity, 1));
}
.text-zinc-500 {
  --tw-text-opacity: 1;
  color: rgb(113 113 122 / var(--tw-text-opacity, 1));
}
.text-zinc-600 {
  --tw-text-opacity: 1;
  color: rgb(82 82 91 / var(--tw-text-opacity, 1));
}
.text-zinc-700 {
  --tw-text-opacity: 1;
  color: rgb(63 63 70 / var(--tw-text-opacity, 1));
}
.text-zinc-800 {
  --tw-text-opacity: 1;
  color: rgb(39 39 42 / var(--tw-text-opacity, 1));
}
.text-zinc-900 {
  --tw-text-opacity: 1;
  color: rgb(24 24 27 / var(--tw-text-opacity, 1));
}
.underline {
  text-decoration-line: underline;
}
.line-through {
  text-decoration-line: line-through;
}
.decoration-primary\/30 {
  text-decoration-color: hsl(var(--primary) / 0.3);
}
.decoration-1 {
  text-decoration-thickness: 1px;
}
.underline-offset-2 {
  text-underline-offset: 2px;
}
.underline-offset-4 {
  text-underline-offset: 4px;
}
.antialiased {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
.placeholder-gray-500::-moz-placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(107 114 128 / var(--tw-placeholder-opacity, 1));
}
.placeholder-gray-500::placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(107 114 128 / var(--tw-placeholder-opacity, 1));
}
.opacity-0 {
  opacity: 0;
}
.opacity-100 {
  opacity: 1;
}
.opacity-20 {
  opacity: 0.2;
}
.opacity-25 {
  opacity: 0.25;
}
.opacity-30 {
  opacity: 0.3;
}
.opacity-50 {
  opacity: 0.5;
}
.opacity-60 {
  opacity: 0.6;
}
.opacity-70 {
  opacity: 0.7;
}
.opacity-75 {
  opacity: 0.75;
}
.opacity-80 {
  opacity: 0.8;
}
.opacity-\[0\.02\] {
  opacity: 0.02;
}
.shadow {
  --tw-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-2xl {
  --tw-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-\[0_0_0_1px_rgba\(0\2c 0\2c 0\2c 0\.05\)\] {
  --tw-shadow: 0 0 0 1px rgba(0,0,0,0.05);
  --tw-shadow-colored: 0 0 0 1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-\[0_25px_50px_-12px_rgba\(0\2c 0\2c 0\2c 0\.25\)\] {
  --tw-shadow: 0 25px 50px -12px rgba(0,0,0,0.25);
  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-\[8px_8px_16px_rgba\(0\2c 0\2c 0\2c 0\.1\)\2c -8px_-8px_16px_rgba\(255\2c 255\2c 255\2c 0\.1\)\] {
  --tw-shadow: 8px 8px 16px rgba(0,0,0,0.1),-8px -8px 16px rgba(255,255,255,0.1);
  --tw-shadow-colored: 8px 8px 16px var(--tw-shadow-color), -8px -8px 16px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-\[inset_8px_8px_16px_rgba\(0\2c 0\2c 0\2c 0\.1\)\2c inset_-8px_-8px_16px_rgba\(255\2c 255\2c 255\2c 0\.1\)\] {
  --tw-shadow: inset 8px 8px 16px rgba(0,0,0,0.1),inset -8px -8px 16px rgba(255,255,255,0.1);
  --tw-shadow-colored: inset 8px 8px 16px var(--tw-shadow-color), inset -8px -8px 16px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-lg {
  --tw-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-md {
  --tw-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-primary {
  --tw-shadow: 0 4px 14px 0 rgba(var(--primary) / 0.25);
  --tw-shadow-colored: 0 4px 14px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-sm {
  --tw-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-xl {
  --tw-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 10px 10px -5px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}
.shadow-blue-400\/20 {
  --tw-shadow-color: rgb(96 165 250 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-blue-500\/25 {
  --tw-shadow-color: rgb(59 130 246 / 0.25);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-green-400\/20 {
  --tw-shadow-color: rgb(74 222 128 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-green-500\/25 {
  --tw-shadow-color: rgb(34 197 94 / 0.25);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-green-500\/50 {
  --tw-shadow-color: rgb(34 197 94 / 0.5);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-indigo-500\/25 {
  --tw-shadow-color: rgb(99 102 241 / 0.25);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-pink-500\/25 {
  --tw-shadow-color: rgb(236 72 153 / 0.25);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-primary {
  --tw-shadow-color: hsl(var(--primary));
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-primary\/20 {
  --tw-shadow-color: hsl(var(--primary) / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-primary\/25 {
  --tw-shadow-color: hsl(var(--primary) / 0.25);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-primary\/5 {
  --tw-shadow-color: hsl(var(--primary) / 0.05);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-primary\/50 {
  --tw-shadow-color: hsl(var(--primary) / 0.5);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-purple-400\/20 {
  --tw-shadow-color: rgb(192 132 252 / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-purple-500\/25 {
  --tw-shadow-color: rgb(168 85 247 / 0.25);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-red-500\/25 {
  --tw-shadow-color: rgb(239 68 68 / 0.25);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-red-500\/50 {
  --tw-shadow-color: rgb(239 68 68 / 0.5);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-secondary\/20 {
  --tw-shadow-color: hsl(var(--secondary) / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-secondary\/50 {
  --tw-shadow-color: hsl(var(--secondary) / 0.5);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-yellow-500\/50 {
  --tw-shadow-color: rgb(234 179 8 / 0.5);
  --tw-shadow: var(--tw-shadow-colored);
}
.shadow-zinc-800\/5 {
  --tw-shadow-color: rgb(39 39 42 / 0.05);
  --tw-shadow: var(--tw-shadow-colored);
}
.outline-none {
  outline: 2px solid transparent;
  outline-offset: 2px;
}
.outline {
  outline-style: solid;
}
.outline-offset-2 {
  outline-offset: 2px;
}
.ring-1 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-2 {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}
.ring-blue-500\/20 {
  --tw-ring-color: rgb(59 130 246 / 0.2);
}
.ring-border {
  --tw-ring-color: hsl(var(--border));
}
.ring-muted {
  --tw-ring-color: hsl(var(--muted));
}
.ring-primary {
  --tw-ring-color: hsl(var(--primary));
}
.ring-primary\/20 {
  --tw-ring-color: hsl(var(--primary) / 0.2);
}
.ring-primary\/50 {
  --tw-ring-color: hsl(var(--primary) / 0.5);
}
.ring-red-500\/20 {
  --tw-ring-color: rgb(239 68 68 / 0.2);
}
.ring-white\/40 {
  --tw-ring-color: rgb(255 255 255 / 0.4);
}
.ring-zinc-900\/5 {
  --tw-ring-color: rgb(24 24 27 / 0.05);
}
.ring-offset-background {
  --tw-ring-offset-color: hsl(var(--background));
}
.blur {
  --tw-blur: blur(8px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.blur-2xl {
  --tw-blur: blur(40px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.blur-3xl {
  --tw-blur: blur(64px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.blur-lg {
  --tw-blur: blur(16px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.blur-md {
  --tw-blur: blur(12px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.blur-sm {
  --tw-blur: blur(4px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.blur-xl {
  --tw-blur: blur(24px);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.brightness-110 {
  --tw-brightness: brightness(1.1);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.drop-shadow {
  --tw-drop-shadow: drop-shadow(0 1px 2px rgb(0 0 0 / 0.1)) drop-shadow(0 1px 1px rgb(0 0 0 / 0.06));
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.drop-shadow-glow {
  --tw-drop-shadow: drop-shadow(0 0 2px rgba(255,255,255,0.2));
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.drop-shadow-sm {
  --tw-drop-shadow: drop-shadow(0 1px 1px rgb(0 0 0 / 0.05));
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.grayscale {
  --tw-grayscale: grayscale(100%);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.filter {
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}
.backdrop-blur {
  --tw-backdrop-blur: blur(8px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.backdrop-blur-lg {
  --tw-backdrop-blur: blur(16px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.backdrop-blur-md {
  --tw-backdrop-blur: blur(12px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.backdrop-blur-sm {
  --tw-backdrop-blur: blur(4px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.backdrop-blur-xl {
  --tw-backdrop-blur: blur(24px);
  -webkit-backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
  backdrop-filter: var(--tw-backdrop-blur) var(--tw-backdrop-brightness) var(--tw-backdrop-contrast) var(--tw-backdrop-grayscale) var(--tw-backdrop-hue-rotate) var(--tw-backdrop-invert) var(--tw-backdrop-opacity) var(--tw-backdrop-saturate) var(--tw-backdrop-sepia);
}
.transition {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, -webkit-backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter, -webkit-backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-opacity {
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-shadow {
  transition-property: box-shadow;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.transition-transform {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}
.duration-1000 {
  transition-duration: 1000ms;
}
.duration-150 {
  transition-duration: 150ms;
}
.duration-200 {
  transition-duration: 200ms;
}
.duration-300 {
  transition-duration: 300ms;
}
.duration-500 {
  transition-duration: 500ms;
}
.duration-600 {
  transition-duration: 600ms;
}
.duration-700 {
  transition-duration: 700ms;
}
.duration-75 {
  transition-duration: 75ms;
}
.ease-in {
  transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
}
.ease-in-out {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.ease-out {
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}
.ease-smooth {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.will-change-transform {
  will-change: transform;
}
@keyframes enter {

  from {
    opacity: var(--tw-enter-opacity, 1);
    transform: translate3d(var(--tw-enter-translate-x, 0), var(--tw-enter-translate-y, 0), 0) scale3d(var(--tw-enter-scale, 1), var(--tw-enter-scale, 1), var(--tw-enter-scale, 1)) rotate(var(--tw-enter-rotate, 0));
  }
}
@keyframes exit {

  to {
    opacity: var(--tw-exit-opacity, 1);
    transform: translate3d(var(--tw-exit-translate-x, 0), var(--tw-exit-translate-y, 0), 0) scale3d(var(--tw-exit-scale, 1), var(--tw-exit-scale, 1), var(--tw-exit-scale, 1)) rotate(var(--tw-exit-rotate, 0));
  }
}
.animate-in {
  animation-name: enter;
  animation-duration: 150ms;
  --tw-enter-opacity: initial;
  --tw-enter-scale: initial;
  --tw-enter-rotate: initial;
  --tw-enter-translate-x: initial;
  --tw-enter-translate-y: initial;
}
.fade-in {
  --tw-enter-opacity: 0;
}
.fade-in-0 {
  --tw-enter-opacity: 0;
}
.fade-out {
  --tw-exit-opacity: 0;
}
.zoom-in-95 {
  --tw-enter-scale: .95;
}
.duration-1000 {
  animation-duration: 1000ms;
}
.duration-150 {
  animation-duration: 150ms;
}
.duration-200 {
  animation-duration: 200ms;
}
.duration-300 {
  animation-duration: 300ms;
}
.duration-500 {
  animation-duration: 500ms;
}
.duration-600 {
  animation-duration: 600ms;
}
.duration-700 {
  animation-duration: 700ms;
}
.duration-75 {
  animation-duration: 75ms;
}
.ease-in {
  animation-timing-function: cubic-bezier(0.4, 0, 1, 1);
}
.ease-in-out {
  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.ease-out {
  animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
}
.ease-smooth {
  animation-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.paused {
  animation-play-state: paused;
}
.perspective-1000 {
  perspective: 1000px;
}
.preserve-3d {
  transform-style: preserve-3d;
}
.backface-hidden {
  backface-visibility: hidden;
}
.rotate-y-180 {
  transform: rotateY(180deg);
}
/* 主页时间线动画 */
.animate-fade-in-up {
    animation: fadeInUp 0.8s cubic-bezier(0.4, 0, 0.2, 1) forwards;
    opacity: 0;
    transform: translateY(30px) scale(0.95);
  }
@keyframes fadeInUp {
    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }
/* 高级悬停动画 */
/* 渐变边框动画 */
/* 文本截断 */
.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
/* 脉冲动画 */
.animate-pulse-soft {
    animation: pulseSoft 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }
@keyframes pulseSoft {
    0%, 100% {
      opacity: 1;
      transform: scale(1);
    }
    50% {
      opacity: 0.8;
      transform: scale(1.05);
    }
  }
/* 光晕效果 */
/* 目录滚动定位优化 */
.prose h1,
  .prose h2,
  .prose h3,
  .prose h4,
  .prose h5,
  .prose h6,
  h1, h2, h3, h4, h5, h6 {
    scroll-margin-top: 180px;
  }
/* 确保页面末尾有足够的空间 - 修复：使用绝对定位避免占据文档流空间 */
.prose::after,
  [data-mdx-content]::after {
    content: '';
    position: absolute;
    bottom: -50vh;
    left: 0;
    right: 0;
    height: 50vh;
    pointer-events: none;
    z-index: -1;
  }
/* 为使用::after的容器添加相对定位 */
.prose,
  [data-mdx-content] {
    position: relative;
  }
/* 文章末尾兜底：禁用最后几个元素的::after占位，避免尾部空白 */
.article-content .prose > *:is(:last-child, :nth-last-child(2), :nth-last-child(3))::after,
  [data-mdx-content] > *:is(:last-child, :nth-last-child(2), :nth-last-child(3))::after {
    content: none !important;
    display: none !important;
  }
/* 防止prose内容被分列显示 */
.prose,
  .prose-zinc,
  .prose-invert,
  [class*="prose"] {
    column-count: 1 !important;
    -moz-column-fill: auto !important;
         column-fill: auto !important;
    -moz-column-gap: 0 !important;
         column-gap: 0 !important;
    -moz-column-rule: none !important;
         column-rule: none !important;
    -moz-column-span: none !important;
         column-span: none !important;
    -moz-column-width: auto !important;
         column-width: auto !important;
    columns: 1 !important;
    -webkit-column-count: 1 !important;
    -moz-column-count: 1 !important;
    -webkit-columns: 1 !important;
    -moz-columns: 1 !important;
  }
/* 确保prose内容中的段落、列表项等不被分割 */
.prose p,
  .prose li,
  .prose blockquote,
  .prose div {
    -moz-column-break-inside: avoid !important;
         break-inside: avoid !important;
    page-break-inside: avoid !important;
    column-break-inside: avoid !important;
  }
/* 特别处理强调文本和内联元素，确保不被分割 */
.prose strong,
  .prose em,
  .prose b,
  .prose i,
  .prose a,
  .prose code,
  .prose span {
    display: inline !important;
    -moz-column-break-inside: avoid !important;
         break-inside: avoid !important;
    page-break-inside: avoid !important;
    column-break-inside: avoid !important;
  }
/* 确保列表项内容完整显示 */
.prose li {
    display: block !important;
    -moz-column-break-inside: avoid !important;
         break-inside: avoid !important;
    page-break-inside: avoid !important;
    column-break-inside: avoid !important;
    white-space: normal !important;
  }
/* 强制所有prose内容使用单列布局 */
.prose *,
  .prose *::before,
  .prose *::after {
    -moz-column-span: none !important;
         column-span: none !important;
    column-count: 1 !important;
    -moz-columns: 1 !important;
         columns: 1 !important;
    -webkit-column-count: 1 !important;
    -moz-column-count: 1 !important;
  }
/* 全局禁用多列布局 */
.rich-text-content,
  .rich-text-content *,
  [data-mdx-content],
  [data-mdx-content] * {
    column-count: 1 !important;
    columns: 1 !important;
    -moz-column-fill: auto !important;
         column-fill: auto !important;
    -moz-column-gap: 0 !important;
         column-gap: 0 !important;
    -moz-column-rule: none !important;
         column-rule: none !important;
    -moz-column-span: none !important;
         column-span: none !important;
    -moz-column-width: auto !important;
         column-width: auto !important;
    -webkit-column-count: 1 !important;
    -moz-column-count: 1 !important;
    -webkit-columns: 1 !important;
    -moz-columns: 1 !important;
    -moz-column-break-inside: avoid !important;
         break-inside: avoid !important;
    page-break-inside: avoid !important;
    column-break-inside: avoid !important;
  }
/* 确保prose内容不受masonry和grid影响 */
.prose.masonry-grid,
  .prose .masonry-grid,
  .prose.masonry-item,
  .prose .masonry-item,
  .prose.album-masonry,
  .prose .album-masonry,
  .prose.album-masonry-item,
  .prose .album-masonry-item {
    -moz-column-count: 1 !important;
         column-count: 1 !important;
    -moz-columns: 1 !important;
         columns: 1 !important;
    display: block !important;
  }
/* 重置所有可能的多列布局 */
body .prose,
  body .prose *,
  body .rich-text-content,
  body .rich-text-content *,
  body [data-mdx-content],
  body [data-mdx-content] * {
    column-count: 1 !important;
    -moz-columns: 1 !important;
         columns: 1 !important;
    -webkit-column-count: 1 !important;
    -moz-column-count: 1 !important;
  }
/* 标签悬停效果 */
/* 卡片悬停效果 */
/* 项目网格容器 - 确保hover效果不被裁剪 */
.project-grid-container {
    overflow: visible;
    padding: 1rem;
  }
/* 项目卡片hover优化 */
.project-card-hover {
    transition: all 0.7s cubic-bezier(0.4, 0, 0.2, 1);
    transform-origin: center;
  }
.project-card-hover:hover {
    transform: translateY(-4px) scale(1.015);
    z-index: 10;
  }
/* 渐变文字效果 */
/* 玻璃态效果 */
/* 暗色模式玻璃态 */
.dark .glass-effect {
    background: rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
/* 博客时间线动画 */
.timeline-node {
    animation: timeline-pulse 2s ease-in-out infinite;
  }
@keyframes timeline-pulse {
    0%, 100% {
      transform: scale(1);
      box-shadow: 0 0 0 0 rgba(var(--primary-rgb), 0.4);
    }
    50% {
      transform: scale(1.05);
      box-shadow: 0 0 0 8px rgba(var(--primary-rgb), 0);
    }
  }
/* 增强的悬停效果 */
.enhanced-hover {
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  }
.enhanced-hover:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow:
      0 20px 40px -12px rgba(0, 0, 0, 0.15),
      0 0 0 1px rgba(var(--primary-rgb), 0.1);
  }
/* 闪烁效果 */
.shimmer {
    position: relative;
    overflow: hidden;
  }
.shimmer::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      90deg,
      transparent,
      rgba(255, 255, 255, 0.2),
      transparent
    );
    transition: left 0.7s ease;
  }
.shimmer:hover::before {
    left: 100%;
  }
/* 脉冲动画 */
.pulse-soft {
    animation: pulse-soft 3s ease-in-out infinite;
  }
@keyframes pulse-soft {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.6;
    }
  }
/* 发光效果 */
.glow-pulse {
    animation: glow-pulse 4s ease-in-out infinite;
  }
@keyframes glow-pulse {
    0%, 100% {
      opacity: 0.5;
      transform: scale(1);
    }
    50% {
      opacity: 0.8;
      transform: scale(1.1);
    }
  }
/* 渐变径向背景 */
.bg-gradient-radial {
    background: radial-gradient(circle, var(--tw-gradient-stops));
  }
/* 增强的边框效果 */
.border-3 {
    border-width: 3px;
  }
/* 动画延迟工具类 */
/* 自定义阴影 */
/* 文字渐变动画 */
@keyframes gradient-shift {
    0%, 100% {
      background-position: 0% 50%;
    }
    50% {
      background-position: 100% 50%;
    }
  }
/* 平滑滚动 */
/* 自定义滚动条 */
.custom-scrollbar::-webkit-scrollbar {
    width: 6px;
  }
.custom-scrollbar::-webkit-scrollbar-track {
    background: hsl(var(--muted));
    border-radius: 3px;
  }
.custom-scrollbar::-webkit-scrollbar-thumb {
    background: hsl(var(--muted-foreground) / 0.3);
    border-radius: 3px;
  }
.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--muted-foreground) / 0.5);
  }
.perspective-1000 {
    perspective: 1000px;
  }
.preserve-3d {
    transform-style: preserve-3d;
  }
.backface-hidden {
    backface-visibility: hidden;
  }
.rotate-y-180 {
    transform: rotateY(180deg);
  }
.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out forwards;
  }
.animate-glow-pulse {
    animation: glowPulse 2s ease-in-out infinite;
  }
/* Timeline优化 - 防止悬停溢出 */
.timeline-container {
    overflow: visible;
    padding: 1rem;
  }
.timeline-card {
    transform-origin: center center;
    will-change: transform, box-shadow;
  }
.timeline-card:hover {
    z-index: 10;
  }
/* 安全的悬停变换 */
/* 增强的缩略图导航栏样式 */
/* 缩略图滚动条美化 - 适配新的深色背景 */
.thumbnail-scroll {
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 255, 255, 0.3) transparent;
  }
.thumbnail-scroll::-webkit-scrollbar {
    height: 6px;
  }
.thumbnail-scroll::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 3px;
  }
.thumbnail-scroll::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 3px;
    -webkit-transition: background 0.2s ease;
    transition: background 0.2s ease;
  }
.thumbnail-scroll::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.4);
  }
/* 缩略图按钮悬停效果 */
/* 缩略图导航栏渐变背景动画 */
/* 响应式缩略图尺寸和动画优化 */
@media (max-width: 640px) {
    .thumbnail-scroll {
      gap: 0.5rem;
      padding-bottom: 0.25rem;
    }

    .thumbnail-button {
      width: 3rem;
      height: 3rem;
    }

    /* 移动端优化的缩略图导航栏 */
    .thumbnail-nav-bg {
      padding: 1rem 0.5rem;
    }
  }
@media (min-width: 641px) and (max-width: 1024px) {
    .thumbnail-scroll {
      gap: 0.625rem;
      padding-bottom: 0.375rem;
    }

    .thumbnail-button {
      width: 3.5rem;
      height: 3.5rem;
    }
  }
/* 文本截断样式 */
.line-clamp-1 {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
/* 相册卡片统一样式 */
/* 相册卡片悬停优化 */
/* 防止悬停时内容被截断 */
/* 相册详情页专用样式 */
/* 3D标题效果 */
/* 滚动进度条动画 */
@keyframes gradient-shift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
  }
/* 图片加载动画 */
.dark .image-loading {
    background: linear-gradient(90deg, #374151 25%, #4B5563 50%, #374151 75%);
    background-size: 200% 100%;
  }
@keyframes loading-shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
  }
/* 响应式优化 */
/* 缩略图导航栏进入/退出动画 */
@keyframes slideUpFade {
    from {
      transform: translateY(100%);
      opacity: 0;
    }
    to {
      transform: translateY(0);
      opacity: 1;
    }
  }
@keyframes slideDownFade {
    from {
      transform: translateY(0);
      opacity: 1;
    }
    to {
      transform: translateY(100%);
      opacity: 0;
    }
  }
/* 新增Hero区域动画 */
@keyframes glow-pulse {
    0%, 100% {
      opacity: 0.5;
      transform: scale(1);
    }
    50% {
      opacity: 0.8;
      transform: scale(1.05);
    }
  }
@keyframes text-glow {
    0%, 100% {
      text-shadow: 0 0 5px rgba(var(--primary), 0.3);
    }
    50% {
      text-shadow: 0 0 20px rgba(var(--primary), 0.6), 0 0 30px rgba(var(--primary), 0.4);
    }
  }
@keyframes float-gentle {
    0%, 100% {
      transform: translateY(0px);
    }
    50% {
      transform: translateY(-3px);
    }
  }
@keyframes shimmer {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }
/* 径向渐变背景 */
.bg-gradient-radial {
    background: radial-gradient(circle, var(--tw-gradient-stops));
  }
/* 动画类 */
.animate-glow-pulse {
    animation: glow-pulse 3s ease-in-out infinite;
  }
.animate-float-gentle {
    animation: float-gentle 3s ease-in-out infinite;
  }
/* Enhanced Blog Module Animations */
.animate-fade-in-up {
    animation: fadeInUp 0.6s ease-out forwards;
  }
.animate-pulse-soft {
    animation: pulseSoft 2s ease-in-out infinite;
  }
.animate-bounce-soft {
    animation: bounceSoft 0.6s ease-in-out;
  }
.animate-scale-in {
    animation: scaleIn 0.4s ease-out forwards;
  }

/* 自定义动画效果 */

/* Gallery增强动画 */

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes glowPulse {
  0%, 100% {
    box-shadow: 0 0 20px rgba(16, 185, 129, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(16, 185, 129, 0.6);
  }
}

@keyframes breathingSoft {
  0%, 100% {
    transform: scale(1);
    opacity: 0.9;
  }
  50% {
    transform: scale(1.02);
    opacity: 1;
  }
}

/* 瀑布流布局优化 */
.masonry-grid {
  -moz-column-count: 1;
       column-count: 1;
  -moz-column-gap: 1rem;
       column-gap: 1rem;
}

@media (min-width: 640px) {
  .masonry-grid {
    -moz-column-count: 2;
         column-count: 2;
  }
}

@media (min-width: 768px) {
  .masonry-grid {
    -moz-column-count: 3;
         column-count: 3;
  }
}

@media (min-width: 1024px) {
  .masonry-grid {
    -moz-column-count: 4;
         column-count: 4;
  }
}

@media (min-width: 1280px) {
  .masonry-grid {
    -moz-column-count: 5;
         column-count: 5;
  }
}

.masonry-item {
  -moz-column-break-inside: avoid;
       break-inside: avoid;
  margin-bottom: 1rem;
}

/* 滚动条美化 */
.gallery-scroll::-webkit-scrollbar {
  width: 6px;
}

.gallery-scroll::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.1);
  border-radius: 3px;
}

.gallery-scroll::-webkit-scrollbar-thumb {
  background: rgba(16, 185, 129, 0.5);
  border-radius: 3px;
}

.gallery-scroll::-webkit-scrollbar-thumb:hover {
  background: rgba(16, 185, 129, 0.7);
}

/* 富文本编辑器样式支持 */
.rich-text-content {
  /* 恢复继承的字体以避免Tailwind的重置 */
  font-family: inherit;
  
  /* 确保编辑器中的内联样式能够生效 */
  & * {
    font-family: inherit;
  }
  
  /* 支持常见编辑器字体 */
  & [style*="font-family"] {
    font-family: var(--custom-font-family, inherit) !important;
  }
  
  /* 字体大小支持 */
  & [style*="font-size"] {
    font-size: var(--custom-font-size, inherit) !important;
  }
  
  /* 常见字体兼容性定义 */
  & [style*="Arial"], & [style*="arial"] {
    --custom-font-family: Arial, sans-serif;
  }
  
  & [style*="Helvetica"], & [style*="helvetica"] {
    --custom-font-family: Helvetica, Arial, sans-serif;
  }
  
  & [style*="Times New Roman"], & [style*="times new roman"], & [style*="Times"], & [style*="times"] {
    --custom-font-family: 'Times New Roman', Times, serif;
  }
  
  & [style*="Courier New"], & [style*="courier new"], & [style*="Courier"], & [style*="courier"] {
    --custom-font-family: 'Courier New', Courier, monospace;
  }
  
  & [style*="Georgia"], & [style*="georgia"] {
    --custom-font-family: Georgia, serif;
  }
  
  & [style*="Garamond"], & [style*="garamond"] {
    --custom-font-family: Garamond, serif;
  }
  
  & [style*="Verdana"], & [style*="verdana"] {
    --custom-font-family: Verdana, Geneva, sans-serif;
  }
  
  & [style*="Tahoma"], & [style*="tahoma"] {
    --custom-font-family: Tahoma, Geneva, sans-serif;
  }
  
  & [style*="Trebuchet MS"], & [style*="trebuchet ms"], & [style*="Trebuchet"], & [style*="trebuchet"] {
    --custom-font-family: 'Trebuchet MS', Helvetica, sans-serif;
  }
  
  & [style*="Impact"], & [style*="impact"] {
    --custom-font-family: Impact, Charcoal, sans-serif;
  }
  
  & [style*="Comic Sans MS"], & [style*="comic sans ms"], & [style*="Comic Sans"], & [style*="comic sans"] {
    --custom-font-family: 'Comic Sans MS', cursive, sans-serif;
  }

  /* 中文字体支持 */
  & [style*="宋体"], & [style*="SimSun"] {
    --custom-font-family: "宋体", SimSun, serif;
  }
  
  & [style*="黑体"], & [style*="SimHei"] {
    --custom-font-family: "黑体", SimHei, sans-serif;
  }
  
  & [style*="微软雅黑"], & [style*="Microsoft YaHei"] {
    --custom-font-family: "Microsoft YaHei", sans-serif;
  }
  
  & [style*="楷体"], & [style*="KaiTi"] {
    --custom-font-family: "楷体", KaiTi, cursive;
  }
  
  & [style*="仿宋"], & [style*="FangSong"] {
    --custom-font-family: "仿宋", FangSong, serif;
  }
  
  /* 表格样式支持 */
  & table {
    border-collapse: collapse;
    width: 100%;
    margin-bottom: 1rem;
  }
  
  & table td,
  & table th {
    border: 1px solid #e5e7eb;
    padding: 0.75rem;
    text-align: left;
  }
  
  & table th {
    background-color: #f9fafb;
    font-weight: 600;
  }
  
  & table tr:nth-child(even) {
    background-color: #f3f4f6;
  }
  
  /* 排版样式支持 */
  & .ql-align-center {
    text-align: center;
  }
  
  & .ql-align-right {
    text-align: right;
  }
  
  & .ql-align-justify {
    text-align: justify;
  }
  
  & .ql-indent-1 {
    padding-left: 3em;
  }
  
  & .ql-indent-2 {
    padding-left: 6em;
  }

  /* Markdown渲染增强样式 */
  & h1, & h2, & h3, & h4, & h5, & h6 {
    margin-top: 1.5rem;
    margin-bottom: 0.75rem;
    font-weight: 600;
    line-height: 1.25;
  }

  & h1 {
    font-size: 2rem;
    border-bottom: 2px solid #e5e7eb;
    padding-bottom: 0.5rem;
  }

  & h2 {
    font-size: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
    padding-bottom: 0.25rem;
  }

  & h3 {
    font-size: 1.25rem;
  }

  & h4 {
    font-size: 1.125rem;
  }

  & p {
    margin-bottom: 1rem;
    line-height: 1.6;
  }

  & ul, & ol {
    margin-bottom: 1rem;
    padding-left: 1.5rem;
  }

  & li {
    margin-bottom: 0.25rem;
  }

  & blockquote {
    border-left: 4px solid #e5e7eb;
    padding-left: 1rem;
    margin: 1rem 0;
    font-style: italic;
    color: #6b7280;
  }

  & code {
    background-color: #f3f4f6;
    padding: 0.125rem 0.25rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
    font-family: 'Courier New', Courier, monospace;
  }

  & pre {
    background-color: #1f2937;
    color: #f9fafb;
    padding: 1rem;
    border-radius: 0.5rem;
    overflow-x: auto;
    margin: 1rem 0;
  }

  & pre code {
    background-color: transparent;
    padding: 0;
    color: inherit;
  }

  & hr {
    border: none;
    border-top: 2px solid #e5e7eb;
    margin: 2rem 0;
  }

  & img {
    max-width: 100%;
    height: auto;
    border-radius: 0.5rem;
    margin: 1rem 0;
  }

  & a {
    color: #3b82f6;
    text-decoration: underline;
  }

  & a:hover {
    color: #1d4ed8;
  }

  /* 暗色模式支持 */
  .dark & {
    & h1, & h2 {
      border-color: #374151;
    }

    & blockquote {
      border-color: #374151;
      color: #9ca3af;
    }

    & code {
      background-color: #374151;
      color: #f9fafb;
    }

    & hr {
      border-color: #374151;
    }

    & table td,
    & table th {
      border-color: #374151;
    }

    & table th {
      background-color: #374151;
    }

    & table tr:nth-child(even) {
      background-color: #1f2937;
    }
  }
}

/* 标签悬停效果 */

/* Hero区域增强动画 */

/* Enhanced Blog Module Keyframes */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulseSoft {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.05);
  }
}

@keyframes bounceSoft {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-4px);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.file\:border-0::file-selector-button {
  border-width: 0px;
}

.file\:bg-transparent::file-selector-button {
  background-color: transparent;
}

.file\:text-sm::file-selector-button {
  font-size: 0.875rem;
  line-height: 1.5rem;
}

.file\:font-medium::file-selector-button {
  font-weight: 500;
}

.file\:text-foreground::file-selector-button {
  color: hsl(var(--foreground));
}

.placeholder\:text-muted-foreground::-moz-placeholder {
  color: hsl(var(--muted-foreground));
}

.placeholder\:text-muted-foreground::placeholder {
  color: hsl(var(--muted-foreground));
}

.after\:absolute::after {
  content: var(--tw-content);
  position: absolute;
}

.after\:right-0::after {
  content: var(--tw-content);
  right: 0px;
}

.after\:top-4::after {
  content: var(--tw-content);
  top: 1rem;
}

.after\:h-1\/2::after {
  content: var(--tw-content);
  height: 50%;
}

.after\:w-1::after {
  content: var(--tw-content);
  width: 0.25rem;
}

.after\:translate-x-1\/2::after {
  content: var(--tw-content);
  --tw-translate-x: 50%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.after\:rounded-full::after {
  content: var(--tw-content);
  border-radius: 9999px;
}

.after\:bg-blue-400::after {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(96 165 250 / var(--tw-bg-opacity, 1));
}

.after\:bg-blue-500::after {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(59 130 246 / var(--tw-bg-opacity, 1));
}

.after\:bg-blue-700::after {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(29 78 216 / var(--tw-bg-opacity, 1));
}

.after\:bg-gray-800::after {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));
}

.after\:bg-green-600::after {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1));
}

.after\:bg-indigo-600::after {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(79 70 229 / var(--tw-bg-opacity, 1));
}

.after\:bg-purple-500::after {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(168 85 247 / var(--tw-bg-opacity, 1));
}

.after\:bg-red-500::after {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(239 68 68 / var(--tw-bg-opacity, 1));
}

.after\:bg-yellow-500::after {
  content: var(--tw-content);
  --tw-bg-opacity: 1;
  background-color: rgb(234 179 8 / var(--tw-bg-opacity, 1));
}

.last\:mb-0:last-child {
  margin-bottom: 0px;
}

.focus-within\:opacity-100:focus-within {
  opacity: 1;
}

.focus-within\:outline-none:focus-within {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.hover\:z-10:hover {
  z-index: 10;
}

.hover\:-translate-y-0\.5:hover {
  --tw-translate-y: -0.125rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:-translate-y-1:hover {
  --tw-translate-y: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:-translate-y-2:hover {
  --tw-translate-y: -0.5rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:-translate-y-3:hover {
  --tw-translate-y: -0.75rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:translate-y-\[-2px\]:hover {
  --tw-translate-y: -2px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:translate-y-\[-4px\]:hover {
  --tw-translate-y: -4px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:translate-y-\[-8px\]:hover {
  --tw-translate-y: -8px;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:scale-105:hover {
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:scale-110:hover {
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:scale-\[1\.01\]:hover {
  --tw-scale-x: 1.01;
  --tw-scale-y: 1.01;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:scale-\[1\.02\]:hover {
  --tw-scale-x: 1.02;
  --tw-scale-y: 1.02;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.hover\:scale-\[1\.03\]:hover {
  --tw-scale-x: 1.03;
  --tw-scale-y: 1.03;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

@keyframes pulse-soft {

  0%, 100% {
    opacity: 1;
  }

  50% {
    opacity: 0.6;
  }
}

.hover\:animate-pulse-soft:hover {
  animation: pulse-soft 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.hover\:border-gray-300:hover {
  --tw-border-opacity: 1;
  border-color: rgb(209 213 219 / var(--tw-border-opacity, 1));
}

.hover\:border-green-500\/30:hover {
  border-color: rgb(34 197 94 / 0.3);
}

.hover\:border-muted-foreground:hover {
  border-color: hsl(var(--muted-foreground));
}

.hover\:border-primary\/20:hover {
  border-color: hsl(var(--primary) / 0.2);
}

.hover\:border-primary\/30:hover {
  border-color: hsl(var(--primary) / 0.3);
}

.hover\:border-primary\/40:hover {
  border-color: hsl(var(--primary) / 0.4);
}

.hover\:border-primary\/50:hover {
  border-color: hsl(var(--primary) / 0.5);
}

.hover\:border-primary\/60:hover {
  border-color: hsl(var(--primary) / 0.6);
}

.hover\:border-white\/50:hover {
  border-color: rgb(255 255 255 / 0.5);
}

.hover\:border-white\/60:hover {
  border-color: rgb(255 255 255 / 0.6);
}

.hover\:border-l-primary:hover {
  border-left-color: hsl(var(--primary));
}

.hover\:bg-accent:hover {
  background-color: hsl(var(--accent));
}

.hover\:bg-background:hover {
  background-color: hsl(var(--background));
}

.hover\:bg-background\/50:hover {
  background-color: hsl(var(--background) / 0.5);
}

.hover\:bg-background\/60:hover {
  background-color: hsl(var(--background) / 0.6);
}

.hover\:bg-background\/80:hover {
  background-color: hsl(var(--background) / 0.8);
}

.hover\:bg-background\/90:hover {
  background-color: hsl(var(--background) / 0.9);
}

.hover\:bg-black\/70:hover {
  background-color: rgb(0 0 0 / 0.7);
}

.hover\:bg-blue-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));
}

.hover\:bg-blue-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(37 99 235 / var(--tw-bg-opacity, 1));
}

.hover\:bg-destructive\/30:hover {
  background-color: hsl(var(--destructive) / 0.3);
}

.hover\:bg-destructive\/80:hover {
  background-color: hsl(var(--destructive) / 0.8);
}

.hover\:bg-destructive\/90:hover {
  background-color: hsl(var(--destructive) / 0.9);
}

.hover\:bg-emerald-200:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(167 243 208 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(243 244 246 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-100\/50:hover {
  background-color: rgb(243 244 246 / 0.5);
}

.hover\:bg-gray-50:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(249 250 251 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gray-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(75 85 99 / var(--tw-bg-opacity, 1));
}

.hover\:bg-green-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(22 163 74 / var(--tw-bg-opacity, 1));
}

.hover\:bg-muted:hover {
  background-color: hsl(var(--muted));
}

.hover\:bg-muted\/20:hover {
  background-color: hsl(var(--muted) / 0.2);
}

.hover\:bg-muted\/50:hover {
  background-color: hsl(var(--muted) / 0.5);
}

.hover\:bg-muted\/80:hover {
  background-color: hsl(var(--muted) / 0.8);
}

.hover\:bg-primary\/10:hover {
  background-color: hsl(var(--primary) / 0.1);
}

.hover\:bg-primary\/20:hover {
  background-color: hsl(var(--primary) / 0.2);
}

.hover\:bg-primary\/5:hover {
  background-color: hsl(var(--primary) / 0.05);
}

.hover\:bg-primary\/80:hover {
  background-color: hsl(var(--primary) / 0.8);
}

.hover\:bg-primary\/90:hover {
  background-color: hsl(var(--primary) / 0.9);
}

.hover\:bg-red-500\/20:hover {
  background-color: rgb(239 68 68 / 0.2);
}

.hover\:bg-red-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(220 38 38 / var(--tw-bg-opacity, 1));
}

.hover\:bg-secondary\/20:hover {
  background-color: hsl(var(--secondary) / 0.2);
}

.hover\:bg-secondary\/80:hover {
  background-color: hsl(var(--secondary) / 0.8);
}

.hover\:bg-white:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}

.hover\:bg-white\/10:hover {
  background-color: rgb(255 255 255 / 0.1);
}

.hover\:bg-white\/20:hover {
  background-color: rgb(255 255 255 / 0.2);
}

.hover\:bg-white\/30:hover {
  background-color: rgb(255 255 255 / 0.3);
}

.hover\:bg-white\/90:hover {
  background-color: rgb(255 255 255 / 0.9);
}

.hover\:bg-yellow-600:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(202 138 4 / var(--tw-bg-opacity, 1));
}

.hover\:bg-zinc-100:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(244 244 245 / var(--tw-bg-opacity, 1));
}

.hover\:bg-zinc-700:hover {
  --tw-bg-opacity: 1;
  background-color: rgb(63 63 70 / var(--tw-bg-opacity, 1));
}

.hover\:bg-gradient-hover:hover {
  background-image: linear-gradient(135deg, hsl(var(--primary) / 0.1) 0%, hsl(var(--primary) / 0.05) 100%);
}

.hover\:bg-gradient-to-br:hover {
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}

.hover\:from-background:hover {
  --tw-gradient-from: hsl(var(--background)) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--background) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:from-primary:hover {
  --tw-gradient-from: hsl(var(--primary)) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--primary) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:from-primary\/20:hover {
  --tw-gradient-from: hsl(var(--primary) / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--primary) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:from-primary\/30:hover {
  --tw-gradient-from: hsl(var(--primary) / 0.3) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--primary) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.hover\:to-primary\/10:hover {
  --tw-gradient-to: hsl(var(--primary) / 0.1) var(--tw-gradient-to-position);
}

.hover\:to-primary\/5:hover {
  --tw-gradient-to: hsl(var(--primary) / 0.05) var(--tw-gradient-to-position);
}

.hover\:to-primary\/80:hover {
  --tw-gradient-to: hsl(var(--primary) / 0.8) var(--tw-gradient-to-position);
}

.hover\:to-secondary\/20:hover {
  --tw-gradient-to: hsl(var(--secondary) / 0.2) var(--tw-gradient-to-position);
}

.hover\:to-secondary\/30:hover {
  --tw-gradient-to: hsl(var(--secondary) / 0.3) var(--tw-gradient-to-position);
}

.hover\:text-accent-foreground:hover {
  color: hsl(var(--accent-foreground));
}

.hover\:text-blue-500:hover {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity, 1));
}

.hover\:text-blue-800:hover {
  --tw-text-opacity: 1;
  color: rgb(30 64 175 / var(--tw-text-opacity, 1));
}

.hover\:text-foreground:hover {
  color: hsl(var(--foreground));
}

.hover\:text-foreground\/80:hover {
  color: hsl(var(--foreground) / 0.8);
}

.hover\:text-gray-300:hover {
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-600:hover {
  --tw-text-opacity: 1;
  color: rgb(75 85 99 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-700:hover {
  --tw-text-opacity: 1;
  color: rgb(55 65 81 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-800:hover {
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity, 1));
}

.hover\:text-gray-900:hover {
  --tw-text-opacity: 1;
  color: rgb(17 24 39 / var(--tw-text-opacity, 1));
}

.hover\:text-green-500:hover {
  --tw-text-opacity: 1;
  color: rgb(34 197 94 / var(--tw-text-opacity, 1));
}

.hover\:text-primary:hover {
  color: hsl(var(--primary));
}

.hover\:text-primary-foreground:hover {
  color: hsl(var(--primary-foreground));
}

.hover\:text-primary\/80:hover {
  color: hsl(var(--primary) / 0.8);
}

.hover\:text-primary\/90:hover {
  color: hsl(var(--primary) / 0.9);
}

.hover\:text-red-500:hover {
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}

.hover\:text-teal-500:hover {
  --tw-text-opacity: 1;
  color: rgb(20 184 166 / var(--tw-text-opacity, 1));
}

.hover\:text-white:hover {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.hover\:underline:hover {
  text-decoration-line: underline;
}

.hover\:decoration-primary\/60:hover {
  text-decoration-color: hsl(var(--primary) / 0.6);
}

.hover\:decoration-2:hover {
  text-decoration-thickness: 2px;
}

.hover\:opacity-100:hover {
  opacity: 1;
}

.hover\:opacity-20:hover {
  opacity: 0.2;
}

.hover\:opacity-90:hover {
  opacity: 0.9;
}

.hover\:shadow-2xl:hover {
  --tw-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-hover:hover {
  --tw-shadow: 0 8px 25px -8px rgba(0, 0, 0, 0.15);
  --tw-shadow-colored: 0 8px 25px -8px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-hover-lg:hover {
  --tw-shadow: 0 12px 35px -8px rgba(0, 0, 0, 0.2);
  --tw-shadow-colored: 0 12px 35px -8px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-lg:hover {
  --tw-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-md:hover {
  --tw-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-primary:hover {
  --tw-shadow: 0 4px 14px 0 rgba(var(--primary) / 0.25);
  --tw-shadow-colored: 0 4px 14px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-primary-lg:hover {
  --tw-shadow: 0 8px 25px -8px rgba(var(--primary) / 0.35);
  --tw-shadow-colored: 0 8px 25px -8px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-sm:hover {
  --tw-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-xl:hover {
  --tw-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --tw-shadow-colored: 0 20px 25px -5px var(--tw-shadow-color), 0 10px 10px -5px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.hover\:shadow-amber-500\/25:hover {
  --tw-shadow-color: rgb(245 158 11 / 0.25);
  --tw-shadow: var(--tw-shadow-colored);
}

.hover\:shadow-blue-500\/25:hover {
  --tw-shadow-color: rgb(59 130 246 / 0.25);
  --tw-shadow: var(--tw-shadow-colored);
}

.hover\:shadow-emerald-500\/10:hover {
  --tw-shadow-color: rgb(16 185 129 / 0.1);
  --tw-shadow: var(--tw-shadow-colored);
}

.hover\:shadow-green-500\/10:hover {
  --tw-shadow-color: rgb(34 197 94 / 0.1);
  --tw-shadow: var(--tw-shadow-colored);
}

.hover\:shadow-green-500\/25:hover {
  --tw-shadow-color: rgb(34 197 94 / 0.25);
  --tw-shadow: var(--tw-shadow-colored);
}

.hover\:shadow-orange-500\/25:hover {
  --tw-shadow-color: rgb(249 115 22 / 0.25);
  --tw-shadow: var(--tw-shadow-colored);
}

.hover\:shadow-primary:hover {
  --tw-shadow-color: hsl(var(--primary));
  --tw-shadow: var(--tw-shadow-colored);
}

.hover\:shadow-primary\/10:hover {
  --tw-shadow-color: hsl(var(--primary) / 0.1);
  --tw-shadow: var(--tw-shadow-colored);
}

.hover\:shadow-primary\/15:hover {
  --tw-shadow-color: hsl(var(--primary) / 0.15);
  --tw-shadow: var(--tw-shadow-colored);
}

.hover\:shadow-primary\/20:hover {
  --tw-shadow-color: hsl(var(--primary) / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}

.hover\:shadow-primary\/25:hover {
  --tw-shadow-color: hsl(var(--primary) / 0.25);
  --tw-shadow: var(--tw-shadow-colored);
}

.hover\:shadow-primary\/30:hover {
  --tw-shadow-color: hsl(var(--primary) / 0.3);
  --tw-shadow: var(--tw-shadow-colored);
}

.hover\:shadow-primary\/5:hover {
  --tw-shadow-color: hsl(var(--primary) / 0.05);
  --tw-shadow: var(--tw-shadow-colored);
}

.hover\:shadow-red-500\/25:hover {
  --tw-shadow-color: rgb(239 68 68 / 0.25);
  --tw-shadow: var(--tw-shadow-colored);
}

.hover\:ring-primary\/20:hover {
  --tw-ring-color: hsl(var(--primary) / 0.2);
}

.hover\:animate-pulse-soft:hover {
    animation: pulseSoft 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
    animation: pulseSoft 2s ease-in-out infinite;
  }

.focus\:border-primary:focus {
  border-color: hsl(var(--primary));
}

.focus\:border-primary\/30:focus {
  border-color: hsl(var(--primary) / 0.3);
}

.focus\:border-primary\/50:focus {
  border-color: hsl(var(--primary) / 0.5);
}

.focus\:bg-accent:focus {
  background-color: hsl(var(--accent));
}

.focus\:text-accent-foreground:focus {
  color: hsl(var(--accent-foreground));
}

.focus\:outline-none:focus {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus\:ring-2:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-4:focus {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus\:ring-primary:focus {
  --tw-ring-color: hsl(var(--primary));
}

.focus\:ring-primary\/10:focus {
  --tw-ring-color: hsl(var(--primary) / 0.1);
}

.focus\:ring-primary\/20:focus {
  --tw-ring-color: hsl(var(--primary) / 0.2);
}

.focus\:ring-primary\/50:focus {
  --tw-ring-color: hsl(var(--primary) / 0.5);
}

.focus\:ring-ring:focus {
  --tw-ring-color: hsl(var(--ring));
}

.focus\:ring-secondary\/20:focus {
  --tw-ring-color: hsl(var(--secondary) / 0.2);
}

.focus\:ring-offset-2:focus {
  --tw-ring-offset-width: 2px;
}

.focus-visible\:border-primary:focus-visible {
  border-color: hsl(var(--primary));
}

.focus-visible\:outline-none:focus-visible {
  outline: 2px solid transparent;
  outline-offset: 2px;
}

.focus-visible\:ring-1:focus-visible {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus-visible\:ring-2:focus-visible {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.focus-visible\:ring-primary\/20:focus-visible {
  --tw-ring-color: hsl(var(--primary) / 0.2);
}

.focus-visible\:ring-ring:focus-visible {
  --tw-ring-color: hsl(var(--ring));
}

.focus-visible\:ring-offset-2:focus-visible {
  --tw-ring-offset-width: 2px;
}

.active\:scale-95:active {
  --tw-scale-x: .95;
  --tw-scale-y: .95;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.active\:bg-zinc-100:active {
  --tw-bg-opacity: 1;
  background-color: rgb(244 244 245 / var(--tw-bg-opacity, 1));
}

.active\:bg-zinc-800:active {
  --tw-bg-opacity: 1;
  background-color: rgb(39 39 42 / var(--tw-bg-opacity, 1));
}

.active\:text-zinc-100\/70:active {
  color: rgb(244 244 245 / 0.7);
}

.active\:text-zinc-900\/60:active {
  color: rgb(24 24 27 / 0.6);
}

.active\:transition-none:active {
  transition-property: none;
}

.disabled\:pointer-events-none:disabled {
  pointer-events: none;
}

.disabled\:cursor-not-allowed:disabled {
  cursor: not-allowed;
}

.disabled\:opacity-50:disabled {
  opacity: 0.5;
}

.disabled\:opacity-70:disabled {
  opacity: 0.7;
}

.group:focus-within .group-focus-within\:text-primary {
  color: hsl(var(--primary));
}

.group\/card:hover .group-hover\/card\:w-full {
  width: 100%;
}

.group:hover .group-hover\:w-full {
  width: 100%;
}

.group\/article:hover .group-hover\/article\:translate-x-2 {
  --tw-translate-x: 0.5rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group\/btn:hover .group-hover\/btn\:translate-x-1 {
  --tw-translate-x: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group\/card:hover .group-hover\/card\:-translate-y-0\.5 {
  --tw-translate-y: -0.125rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group\/card:hover .group-hover\/card\:translate-x-1 {
  --tw-translate-x: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group\/card:hover .group-hover\/card\:translate-x-\[100\%\] {
  --tw-translate-x: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group\/cta:hover .group-hover\/cta\:-translate-y-0\.5 {
  --tw-translate-y: -0.125rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group\/cta:hover .group-hover\/cta\:translate-x-0\.5 {
  --tw-translate-x: 0.125rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group\/cta:hover .group-hover\/cta\:translate-x-\[100\%\] {
  --tw-translate-x: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group\/tag:hover .group-hover\/tag\:translate-x-\[100\%\] {
  --tw-translate-x: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group\/version:hover .group-hover\/version\:translate-x-2 {
  --tw-translate-x: 0.5rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:-translate-x-1 {
  --tw-translate-x: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:-translate-y-3 {
  --tw-translate-y: -0.75rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:translate-x-0\.5 {
  --tw-translate-x: 0.125rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:translate-x-1 {
  --tw-translate-x: 0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:translate-x-\[100\%\] {
  --tw-translate-x: 100%;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:rotate-1 {
  --tw-rotate: 1deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:rotate-12 {
  --tw-rotate: 12deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:rotate-180 {
  --tw-rotate: 180deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:rotate-3 {
  --tw-rotate: 3deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group\/article:hover .group-hover\/article\:scale-125 {
  --tw-scale-x: 1.25;
  --tw-scale-y: 1.25;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group\/btn:hover .group-hover\/btn\:scale-110 {
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group\/button:hover .group-hover\/button\:scale-110 {
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group\/card:hover .group-hover\/card\:scale-105 {
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group\/card:hover .group-hover\/card\:scale-110 {
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group\/header:hover .group-hover\/header\:scale-105 {
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group\/header:hover .group-hover\/header\:scale-110 {
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group\/image:hover .group-hover\/image\:scale-105 {
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group\/image:hover .group-hover\/image\:scale-110 {
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group\/item:hover .group-hover\/item\:scale-110 {
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group\/link:hover .group-hover\/link\:scale-110 {
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group\/meta:hover .group-hover\/meta\:scale-110 {
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group\/progress:hover .group-hover\/progress\:scale-110 {
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group\/version:hover .group-hover\/version\:scale-125 {
  --tw-scale-x: 1.25;
  --tw-scale-y: 1.25;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:scale-100 {
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:scale-105 {
  --tw-scale-x: 1.05;
  --tw-scale-y: 1.05;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:scale-110 {
  --tw-scale-x: 1.1;
  --tw-scale-y: 1.1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:scale-125 {
  --tw-scale-x: 1.25;
  --tw-scale-y: 1.25;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:scale-\[1\.02\] {
  --tw-scale-x: 1.02;
  --tw-scale-y: 1.02;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:scale-\[1\.03\] {
  --tw-scale-x: 1.03;
  --tw-scale-y: 1.03;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:hover .group-hover\:scale-x-100 {
  --tw-scale-x: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

@keyframes bounce-soft {

  0%, 100% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-4px);
  }
}

.group:hover .group-hover\:animate-bounce-soft {
  animation: bounce-soft 1s infinite;
}

@keyframes pulse-soft {

  0%, 100% {
    opacity: 1;
  }

  50% {
    opacity: 0.6;
  }
}

.group:hover .group-hover\:animate-pulse-soft {
  animation: pulse-soft 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.group:hover .group-hover\:cursor-pointer {
  cursor: pointer;
}

.group:hover .group-hover\:gap-2 {
  gap: 0.5rem;
}

.group\/card:hover .group-hover\/card\:border-primary\/20 {
  border-color: hsl(var(--primary) / 0.2);
}

.group\/card:hover .group-hover\/card\:border-primary\/30 {
  border-color: hsl(var(--primary) / 0.3);
}

.group\/header:hover .group-hover\/header\:border-primary\/25 {
  border-color: hsl(var(--primary) / 0.25);
}

.group\/item:hover .group-hover\/item\:border-blue-500\/30 {
  border-color: rgb(59 130 246 / 0.3);
}

.group\/item:hover .group-hover\/item\:border-green-500\/30 {
  border-color: rgb(34 197 94 / 0.3);
}

.group\/item:hover .group-hover\/item\:border-orange-500\/30 {
  border-color: rgb(249 115 22 / 0.3);
}

.group\/item:hover .group-hover\/item\:border-primary\/30 {
  border-color: hsl(var(--primary) / 0.3);
}

.group\/item:hover .group-hover\/item\:border-purple-500\/30 {
  border-color: rgb(168 85 247 / 0.3);
}

.group\/item:hover .group-hover\/item\:border-secondary\/30 {
  border-color: hsl(var(--secondary) / 0.3);
}

.group:hover .group-hover\:border-primary\/20 {
  border-color: hsl(var(--primary) / 0.2);
}

.group:hover .group-hover\:border-primary\/30 {
  border-color: hsl(var(--primary) / 0.3);
}

.group:hover .group-hover\:border-teal-400 {
  --tw-border-opacity: 1;
  border-color: rgb(45 212 191 / var(--tw-border-opacity, 1));
}

.group\/image:hover .group-hover\/image\:bg-black\/20 {
  background-color: rgb(0 0 0 / 0.2);
}

.group\/item:hover .group-hover\/item\:bg-blue-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(239 246 255 / var(--tw-bg-opacity, 1));
}

.group\/item:hover .group-hover\/item\:bg-green-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(240 253 244 / var(--tw-bg-opacity, 1));
}

.group\/item:hover .group-hover\/item\:bg-orange-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(255 247 237 / var(--tw-bg-opacity, 1));
}

.group\/item:hover .group-hover\/item\:bg-primary\/5 {
  background-color: hsl(var(--primary) / 0.05);
}

.group\/item:hover .group-hover\/item\:bg-purple-50 {
  --tw-bg-opacity: 1;
  background-color: rgb(250 245 255 / var(--tw-bg-opacity, 1));
}

.group\/item:hover .group-hover\/item\:bg-secondary\/10 {
  background-color: hsl(var(--secondary) / 0.1);
}

.group\/meta:hover .group-hover\/meta\:bg-blue-500\/20 {
  background-color: rgb(59 130 246 / 0.2);
}

.group\/meta:hover .group-hover\/meta\:bg-green-500\/20 {
  background-color: rgb(34 197 94 / 0.2);
}

.group\/meta:hover .group-hover\/meta\:bg-primary\/20 {
  background-color: hsl(var(--primary) / 0.2);
}

.group\/meta:hover .group-hover\/meta\:bg-purple-500\/20 {
  background-color: rgb(168 85 247 / 0.2);
}

.group\/version:hover .group-hover\/version\:bg-primary {
  background-color: hsl(var(--primary));
}

.group:hover .group-hover\:bg-black\/40 {
  background-color: rgb(0 0 0 / 0.4);
}

.group:hover .group-hover\:bg-green-500\/20 {
  background-color: rgb(34 197 94 / 0.2);
}

.group:hover .group-hover\:bg-muted\/5 {
  background-color: hsl(var(--muted) / 0.05);
}

.group:hover .group-hover\:bg-muted\/50 {
  background-color: hsl(var(--muted) / 0.5);
}

.group:hover .group-hover\:bg-primary {
  background-color: hsl(var(--primary));
}

.group:hover .group-hover\:bg-primary\/10 {
  background-color: hsl(var(--primary) / 0.1);
}

.group:hover .group-hover\:bg-primary\/20 {
  background-color: hsl(var(--primary) / 0.2);
}

.group:hover .group-hover\:bg-gradient-to-br {
  background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}

.group:hover .group-hover\:bg-gradient-to-r {
  background-image: linear-gradient(to right, var(--tw-gradient-stops));
}

.group\/card:hover .group-hover\/card\:from-primary\/20 {
  --tw-gradient-from: hsl(var(--primary) / 0.2) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--primary) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.group\/version:hover .group-hover\/version\:from-primary\/40 {
  --tw-gradient-from: hsl(var(--primary) / 0.4) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--primary) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.group:hover .group-hover\:from-background {
  --tw-gradient-from: hsl(var(--background)) var(--tw-gradient-from-position);
  --tw-gradient-to: hsl(var(--background) / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.group\/card:hover .group-hover\/card\:to-primary\/10 {
  --tw-gradient-to: hsl(var(--primary) / 0.1) var(--tw-gradient-to-position);
}

.group\/version:hover .group-hover\/version\:to-primary\/10 {
  --tw-gradient-to: hsl(var(--primary) / 0.1) var(--tw-gradient-to-position);
}

.group:hover .group-hover\:to-primary\/5 {
  --tw-gradient-to: hsl(var(--primary) / 0.05) var(--tw-gradient-to-position);
}

.group:hover .group-hover\:bg-clip-text {
  -webkit-background-clip: text;
          background-clip: text;
}

.group\/like:hover .group-hover\/like\:fill-red-500 {
  fill: #ef4444;
}

.group:hover .group-hover\:stroke-zinc-700 {
  stroke: #3f3f46;
}

.group:hover .group-hover\:pl-3 {
  padding-left: 0.75rem;
}

.group\/button:hover .group-hover\/button\:text-primary {
  color: hsl(var(--primary));
}

.group\/card:hover .group-hover\/card\:text-primary {
  color: hsl(var(--primary));
}

.group\/card:hover .group-hover\/card\:text-primary\/60 {
  color: hsl(var(--primary) / 0.6);
}

.group\/card:hover .group-hover\/card\:text-primary\/80 {
  color: hsl(var(--primary) / 0.8);
}

.group\/card:hover .group-hover\/card\:text-primary\/90 {
  color: hsl(var(--primary) / 0.9);
}

.group\/header:hover .group-hover\/header\:text-foreground {
  color: hsl(var(--foreground));
}

.group\/header:hover .group-hover\/header\:text-primary {
  color: hsl(var(--primary));
}

.group\/header:hover .group-hover\/header\:text-slate-900 {
  --tw-text-opacity: 1;
  color: rgb(15 23 42 / var(--tw-text-opacity, 1));
}

.group\/item:hover .group-hover\/item\:text-foreground\/70 {
  color: hsl(var(--foreground) / 0.7);
}

.group\/item:hover .group-hover\/item\:text-foreground\/80 {
  color: hsl(var(--foreground) / 0.8);
}

.group\/item:hover .group-hover\/item\:text-primary {
  color: hsl(var(--primary));
}

.group\/like:hover .group-hover\/like\:text-red-500 {
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}

.group\/meta:hover .group-hover\/meta\:text-primary {
  color: hsl(var(--primary));
}

.group\/title:hover .group-hover\/title\:text-primary {
  color: hsl(var(--primary));
}

.group\/view:hover .group-hover\/view\:text-blue-500 {
  --tw-text-opacity: 1;
  color: rgb(59 130 246 / var(--tw-text-opacity, 1));
}

.group:hover .group-hover\:text-foreground {
  color: hsl(var(--foreground));
}

.group:hover .group-hover\:text-foreground\/80 {
  color: hsl(var(--foreground) / 0.8);
}

.group:hover .group-hover\:text-gray-800 {
  --tw-text-opacity: 1;
  color: rgb(31 41 55 / var(--tw-text-opacity, 1));
}

.group:hover .group-hover\:text-green-500 {
  --tw-text-opacity: 1;
  color: rgb(34 197 94 / var(--tw-text-opacity, 1));
}

.group:hover .group-hover\:text-primary {
  color: hsl(var(--primary));
}

.group:hover .group-hover\:text-primary\/70 {
  color: hsl(var(--primary) / 0.7);
}

.group:hover .group-hover\:text-primary\/80 {
  color: hsl(var(--primary) / 0.8);
}

.group:hover .group-hover\:text-red-500 {
  --tw-text-opacity: 1;
  color: rgb(239 68 68 / var(--tw-text-opacity, 1));
}

.group:hover .group-hover\:text-transparent {
  color: transparent;
}

.group\/button:hover .group-hover\/button\:opacity-100 {
  opacity: 1;
}

.group\/card:hover .group-hover\/card\:opacity-100 {
  opacity: 1;
}

.group\/dark:hover .group-hover\/dark\:opacity-100 {
  opacity: 1;
}

.group\/header:hover .group-hover\/header\:opacity-100 {
  opacity: 1;
}

.group\/image:hover .group-hover\/image\:opacity-100 {
  opacity: 1;
}

.group\/input:hover .group-hover\/input\:opacity-100 {
  opacity: 1;
}

.group\/item:hover .group-hover\/item\:opacity-100 {
  opacity: 1;
}

.group\/light:hover .group-hover\/light\:opacity-100 {
  opacity: 1;
}

.group\/node:hover .group-hover\/node\:opacity-100 {
  opacity: 1;
}

.group\/progress:hover .group-hover\/progress\:opacity-100 {
  opacity: 1;
}

.group\/subscribe:hover .group-hover\/subscribe\:opacity-100 {
  opacity: 1;
}

.group\/tag:hover .group-hover\/tag\:opacity-20 {
  opacity: 0.2;
}

.group\/title:hover .group-hover\/title\:opacity-100 {
  opacity: 1;
}

.group\/toc:hover .group-hover\/toc\:opacity-100 {
  opacity: 1;
}

.group\/year:hover .group-hover\/year\:opacity-100 {
  opacity: 1;
}

.group:hover .group-hover\:opacity-100 {
  opacity: 1;
}

.group:hover .group-hover\:opacity-20 {
  opacity: 0.2;
}

.group:hover .group-hover\:opacity-30 {
  opacity: 0.3;
}

.group:hover .group-hover\:opacity-40 {
  opacity: 0.4;
}

.group:hover .group-hover\:opacity-80 {
  opacity: 0.8;
}

.group:hover .group-hover\:opacity-90 {
  opacity: 0.9;
}

.group\/article:hover .group-hover\/article\:shadow-2xl {
  --tw-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.group\/card:hover .group-hover\/card\:shadow-lg {
  --tw-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.group\/item:hover .group-hover\/item\:shadow-lg {
  --tw-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.group\/version:hover .group-hover\/version\:shadow-2xl {
  --tw-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.group:hover .group-hover\:shadow-2xl {
  --tw-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --tw-shadow-colored: 0 25px 50px -12px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.group:hover .group-hover\:shadow-lg {
  --tw-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.group:hover .group-hover\:shadow-md {
  --tw-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --tw-shadow-colored: 0 4px 6px -1px var(--tw-shadow-color), 0 2px 4px -1px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.group\/article:hover .group-hover\/article\:shadow-primary\/30 {
  --tw-shadow-color: hsl(var(--primary) / 0.3);
  --tw-shadow: var(--tw-shadow-colored);
}

.group\/card:hover .group-hover\/card\:shadow-primary\/20 {
  --tw-shadow-color: hsl(var(--primary) / 0.2);
  --tw-shadow: var(--tw-shadow-colored);
}

.group\/version:hover .group-hover\/version\:shadow-primary\/30 {
  --tw-shadow-color: hsl(var(--primary) / 0.3);
  --tw-shadow: var(--tw-shadow-colored);
}

.group:hover .group-hover\:shadow-primary\/10 {
  --tw-shadow-color: hsl(var(--primary) / 0.1);
  --tw-shadow: var(--tw-shadow-colored);
}

.group:hover .group-hover\:brightness-110 {
  --tw-brightness: brightness(1.1);
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.group\/card:hover .group-hover\/card\:drop-shadow-md {
  --tw-drop-shadow: drop-shadow(0 4px 3px rgb(0 0 0 / 0.07)) drop-shadow(0 2px 2px rgb(0 0 0 / 0.06));
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.group\/card:hover .group-hover\/card\:drop-shadow-sm {
  --tw-drop-shadow: drop-shadow(0 1px 1px rgb(0 0 0 / 0.05));
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.group\/header:hover .group-hover\/header\:drop-shadow-md {
  --tw-drop-shadow: drop-shadow(0 4px 3px rgb(0 0 0 / 0.07)) drop-shadow(0 2px 2px rgb(0 0 0 / 0.06));
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.group:hover .group-hover\:drop-shadow-\[0_0_15px_rgba\(var\(--primary\)\2c 0\.5\)\] {
  --tw-drop-shadow: drop-shadow(0 0 15px rgba(var(--primary),0.5));
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.group:hover .group-hover\:drop-shadow-lg {
  --tw-drop-shadow: drop-shadow(0 10px 8px rgb(0 0 0 / 0.04)) drop-shadow(0 4px 3px rgb(0 0 0 / 0.1));
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.group:hover .group-hover\:drop-shadow-sm {
  --tw-drop-shadow: drop-shadow(0 1px 1px rgb(0 0 0 / 0.05));
  filter: var(--tw-blur) var(--tw-brightness) var(--tw-contrast) var(--tw-grayscale) var(--tw-hue-rotate) var(--tw-invert) var(--tw-saturate) var(--tw-sepia) var(--tw-drop-shadow);
}

.group:hover .group-hover\:rotate-y-180 {
  transform: rotateY(180deg);
}

.group:hover .group-hover\:animate-pulse-soft {
    animation: pulseSoft 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

.group:hover .group-hover\:rotate-y-180 {
    transform: rotateY(180deg);
  }

.group:hover .group-hover\:animate-pulse-soft {
    animation: pulseSoft 2s ease-in-out infinite;
  }

.group:hover .group-hover\:animate-bounce-soft {
    animation: bounceSoft 0.6s ease-in-out;
  }

.group\/tag:active .group-active\/tag\:scale-100 {
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:active .group-active\:scale-100 {
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group:active .group-active\:scale-150 {
  --tw-scale-x: 1.5;
  --tw-scale-y: 1.5;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.group\/tag:active .group-active\/tag\:opacity-30 {
  opacity: 0.3;
}

.group:active .group-active\:opacity-20 {
  opacity: 0.2;
}

.group:active .group-active\:opacity-30 {
  opacity: 0.3;
}

.data-\[disabled\]\:pointer-events-none[data-disabled] {
  pointer-events: none;
}

.data-\[state\=active\]\:bg-background[data-state="active"] {
  background-color: hsl(var(--background));
}

.data-\[state\=open\]\:bg-accent[data-state="open"] {
  background-color: hsl(var(--accent));
}

.data-\[state\=active\]\:text-foreground[data-state="active"] {
  color: hsl(var(--foreground));
}

.data-\[disabled\]\:opacity-50[data-disabled] {
  opacity: 0.5;
}

.data-\[state\=active\]\:shadow-sm[data-state="active"] {
  --tw-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --tw-shadow-colored: 0 1px 3px 0 var(--tw-shadow-color), 0 1px 2px 0 var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.data-\[state\=open\]\:animate-in[data-state="open"] {
  animation-name: enter;
  animation-duration: 150ms;
  --tw-enter-opacity: initial;
  --tw-enter-scale: initial;
  --tw-enter-rotate: initial;
  --tw-enter-translate-x: initial;
  --tw-enter-translate-y: initial;
}

.data-\[state\=closed\]\:animate-out[data-state="closed"] {
  animation-name: exit;
  animation-duration: 150ms;
  --tw-exit-opacity: initial;
  --tw-exit-scale: initial;
  --tw-exit-rotate: initial;
  --tw-exit-translate-x: initial;
  --tw-exit-translate-y: initial;
}

.data-\[state\=closed\]\:fade-out-0[data-state="closed"] {
  --tw-exit-opacity: 0;
}

.data-\[state\=open\]\:fade-in-0[data-state="open"] {
  --tw-enter-opacity: 0;
}

.data-\[state\=closed\]\:zoom-out-95[data-state="closed"] {
  --tw-exit-scale: .95;
}

.data-\[state\=open\]\:zoom-in-95[data-state="open"] {
  --tw-enter-scale: .95;
}

.data-\[side\=bottom\]\:slide-in-from-top-2[data-side="bottom"] {
  --tw-enter-translate-y: -0.5rem;
}

.data-\[side\=left\]\:slide-in-from-right-2[data-side="left"] {
  --tw-enter-translate-x: 0.5rem;
}

.data-\[side\=right\]\:slide-in-from-left-2[data-side="right"] {
  --tw-enter-translate-x: -0.5rem;
}

.data-\[side\=top\]\:slide-in-from-bottom-2[data-side="top"] {
  --tw-enter-translate-y: 0.5rem;
}

.dark\:block:is(.dark *) {
  display: block;
}

.dark\:hidden:is(.dark *) {
  display: none;
}

.dark\:-rotate-90:is(.dark *) {
  --tw-rotate: -90deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.dark\:rotate-0:is(.dark *) {
  --tw-rotate: 0deg;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.dark\:scale-0:is(.dark *) {
  --tw-scale-x: 0;
  --tw-scale-y: 0;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.dark\:scale-100:is(.dark *) {
  --tw-scale-x: 1;
  --tw-scale-y: 1;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.dark\:divide-zinc-100\/5:is(.dark *) > :not([hidden]) ~ :not([hidden]) {
  border-color: rgb(244 244 245 / 0.05);
}

.dark\:border:is(.dark *) {
  border-width: 1px;
}

.dark\:border-emerald-700:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(4 120 87 / var(--tw-border-opacity, 1));
}

.dark\:border-gray-700:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(55 65 81 / var(--tw-border-opacity, 1));
}

.dark\:border-gray-700\/20:is(.dark *) {
  border-color: rgb(55 65 81 / 0.2);
}

.dark\:border-gray-700\/30:is(.dark *) {
  border-color: rgb(55 65 81 / 0.3);
}

.dark\:border-gray-700\/50:is(.dark *) {
  border-color: rgb(55 65 81 / 0.5);
}

.dark\:border-gray-800:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(31 41 55 / var(--tw-border-opacity, 1));
}

.dark\:border-teal-900:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(19 78 74 / var(--tw-border-opacity, 1));
}

.dark\:border-white\/10:is(.dark *) {
  border-color: rgb(255 255 255 / 0.1);
}

.dark\:border-zinc-600:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(82 82 91 / var(--tw-border-opacity, 1));
}

.dark\:border-zinc-700:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(63 63 70 / var(--tw-border-opacity, 1));
}

.dark\:border-zinc-700\/40:is(.dark *) {
  border-color: rgb(63 63 70 / 0.4);
}

.dark\:border-zinc-700\/50:is(.dark *) {
  border-color: rgb(63 63 70 / 0.5);
}

.dark\:bg-background\/80:is(.dark *) {
  background-color: hsl(var(--background) / 0.8);
}

.dark\:bg-black\/10:is(.dark *) {
  background-color: rgb(0 0 0 / 0.1);
}

.dark\:bg-black\/90:is(.dark *) {
  background-color: rgb(0 0 0 / 0.9);
}

.dark\:bg-blue-900\/20:is(.dark *) {
  background-color: rgb(30 58 138 / 0.2);
}

.dark\:bg-emerald-900\/30:is(.dark *) {
  background-color: rgb(6 78 59 / 0.3);
}

.dark\:bg-gray-700:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));
}

.dark\:bg-gray-700\/50:is(.dark *) {
  background-color: rgb(55 65 81 / 0.5);
}

.dark\:bg-gray-800:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));
}

.dark\:bg-gray-800\/20:is(.dark *) {
  background-color: rgb(31 41 55 / 0.2);
}

.dark\:bg-gray-800\/60:is(.dark *) {
  background-color: rgb(31 41 55 / 0.6);
}

.dark\:bg-gray-800\/80:is(.dark *) {
  background-color: rgb(31 41 55 / 0.8);
}

.dark\:bg-gray-800\/90:is(.dark *) {
  background-color: rgb(31 41 55 / 0.9);
}

.dark\:bg-gray-800\/95:is(.dark *) {
  background-color: rgb(31 41 55 / 0.95);
}

.dark\:bg-gray-900\/95:is(.dark *) {
  background-color: rgb(17 24 39 / 0.95);
}

.dark\:bg-green-900\/20:is(.dark *) {
  background-color: rgb(20 83 45 / 0.2);
}

.dark\:bg-muted:is(.dark *) {
  background-color: hsl(var(--muted));
}

.dark\:bg-orange-900\/20:is(.dark *) {
  background-color: rgb(124 45 18 / 0.2);
}

.dark\:bg-primary-foreground:is(.dark *) {
  background-color: hsl(var(--primary-foreground));
}

.dark\:bg-red-900\/20:is(.dark *) {
  background-color: rgb(127 29 29 / 0.2);
}

.dark\:bg-teal-950\/30:is(.dark *) {
  background-color: rgb(4 47 46 / 0.3);
}

.dark\:bg-zinc-700:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(63 63 70 / var(--tw-bg-opacity, 1));
}

.dark\:bg-zinc-800:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(39 39 42 / var(--tw-bg-opacity, 1));
}

.dark\:bg-zinc-800\/50:is(.dark *) {
  background-color: rgb(39 39 42 / 0.5);
}

.dark\:bg-zinc-800\/90:is(.dark *) {
  background-color: rgb(39 39 42 / 0.9);
}

.dark\:from-gray-600:is(.dark *) {
  --tw-gradient-from: #4b5563 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(75 85 99 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.dark\:from-gray-900:is(.dark *) {
  --tw-gradient-from: #111827 var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(17 24 39 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.dark\:from-slate-800\/40:is(.dark *) {
  --tw-gradient-from: rgb(30 41 59 / 0.4) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(30 41 59 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.dark\:from-slate-800\/50:is(.dark *) {
  --tw-gradient-from: rgb(30 41 59 / 0.5) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(30 41 59 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.dark\:from-teal-950\/30:is(.dark *) {
  --tw-gradient-from: rgb(4 47 46 / 0.3) var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(4 47 46 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.dark\:from-zinc-800:is(.dark *) {
  --tw-gradient-from: #27272a var(--tw-gradient-from-position);
  --tw-gradient-to: rgb(39 39 42 / 0) var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to);
}

.dark\:via-slate-800\/20:is(.dark *) {
  --tw-gradient-to: rgb(30 41 59 / 0)  var(--tw-gradient-to-position);
  --tw-gradient-stops: var(--tw-gradient-from), rgb(30 41 59 / 0.2) var(--tw-gradient-via-position), var(--tw-gradient-to);
}

.dark\:to-gray-800:is(.dark *) {
  --tw-gradient-to: #1f2937 var(--tw-gradient-to-position);
}

.dark\:to-slate-900\/50:is(.dark *) {
  --tw-gradient-to: rgb(15 23 42 / 0.5) var(--tw-gradient-to-position);
}

.dark\:to-transparent:is(.dark *) {
  --tw-gradient-to: transparent var(--tw-gradient-to-position);
}

.dark\:to-zinc-900:is(.dark *) {
  --tw-gradient-to: #18181b var(--tw-gradient-to-position);
}

.dark\:stroke-zinc-500:is(.dark *) {
  stroke: #71717a;
}

.dark\:text-amber-300:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(252 211 77 / var(--tw-text-opacity, 1));
}

.dark\:text-blue-300:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(147 197 253 / var(--tw-text-opacity, 1));
}

.dark\:text-blue-400:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(96 165 250 / var(--tw-text-opacity, 1));
}

.dark\:text-emerald-300:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(110 231 183 / var(--tw-text-opacity, 1));
}

.dark\:text-gray-200:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(229 231 235 / var(--tw-text-opacity, 1));
}

.dark\:text-gray-300:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity, 1));
}

.dark\:text-gray-400:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(156 163 175 / var(--tw-text-opacity, 1));
}

.dark\:text-green-300:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(134 239 172 / var(--tw-text-opacity, 1));
}

.dark\:text-green-400:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(74 222 128 / var(--tw-text-opacity, 1));
}

.dark\:text-orange-300:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(253 186 116 / var(--tw-text-opacity, 1));
}

.dark\:text-orange-400:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(251 146 60 / var(--tw-text-opacity, 1));
}

.dark\:text-primary:is(.dark *) {
  color: hsl(var(--primary));
}

.dark\:text-red-300:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(252 165 165 / var(--tw-text-opacity, 1));
}

.dark\:text-red-400:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(248 113 113 / var(--tw-text-opacity, 1));
}

.dark\:text-slate-300:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(203 213 225 / var(--tw-text-opacity, 1));
}

.dark\:text-teal-400:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(45 212 191 / var(--tw-text-opacity, 1));
}

.dark\:text-white:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.dark\:text-zinc-100:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(244 244 245 / var(--tw-text-opacity, 1));
}

.dark\:text-zinc-200:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(228 228 231 / var(--tw-text-opacity, 1));
}

.dark\:text-zinc-300:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(212 212 216 / var(--tw-text-opacity, 1));
}

.dark\:text-zinc-400:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(161 161 170 / var(--tw-text-opacity, 1));
}

.dark\:text-zinc-500:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(113 113 122 / var(--tw-text-opacity, 1));
}

.dark\:placeholder-gray-400:is(.dark *)::-moz-placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(156 163 175 / var(--tw-placeholder-opacity, 1));
}

.dark\:placeholder-gray-400:is(.dark *)::placeholder {
  --tw-placeholder-opacity: 1;
  color: rgb(156 163 175 / var(--tw-placeholder-opacity, 1));
}

.dark\:opacity-100:is(.dark *) {
  opacity: 1;
}

.dark\:ring-0:is(.dark *) {
  --tw-ring-offset-shadow: var(--tw-ring-inset) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);
  --tw-ring-shadow: var(--tw-ring-inset) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color);
  box-shadow: var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow, 0 0 #0000);
}

.dark\:ring-white\/10:is(.dark *) {
  --tw-ring-color: rgb(255 255 255 / 0.1);
}

.dark\:prose-invert:is(.dark *) {
    column-count: 1 !important;
    -moz-column-fill: auto !important;
         column-fill: auto !important;
    -moz-column-gap: 0 !important;
         column-gap: 0 !important;
    -moz-column-rule: none !important;
         column-rule: none !important;
    -moz-column-span: none !important;
         column-span: none !important;
    -moz-column-width: auto !important;
         column-width: auto !important;
    columns: 1 !important;
    -webkit-column-count: 1 !important;
    -moz-column-count: 1 !important;
    -webkit-columns: 1 !important;
    -moz-columns: 1 !important;
  }

.dark\:hover\:border-gray-600:hover:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(75 85 99 / var(--tw-border-opacity, 1));
}

.dark\:hover\:border-primary\/30:hover:is(.dark *) {
  border-color: hsl(var(--primary) / 0.3);
}

.dark\:hover\:border-zinc-700:hover:is(.dark *) {
  --tw-border-opacity: 1;
  border-color: rgb(63 63 70 / var(--tw-border-opacity, 1));
}

.dark\:hover\:bg-emerald-800\/50:hover:is(.dark *) {
  background-color: rgb(6 95 70 / 0.5);
}

.dark\:hover\:bg-gray-700:hover:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(55 65 81 / var(--tw-bg-opacity, 1));
}

.dark\:hover\:bg-gray-700\/50:hover:is(.dark *) {
  background-color: rgb(55 65 81 / 0.5);
}

.dark\:hover\:bg-gray-700\/90:hover:is(.dark *) {
  background-color: rgb(55 65 81 / 0.9);
}

.dark\:hover\:bg-gray-800:hover:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(31 41 55 / var(--tw-bg-opacity, 1));
}

.dark\:hover\:bg-zinc-600:hover:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(82 82 91 / var(--tw-bg-opacity, 1));
}

.dark\:hover\:bg-zinc-800:hover:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(39 39 42 / var(--tw-bg-opacity, 1));
}

.dark\:hover\:text-gray-200:hover:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(229 231 235 / var(--tw-text-opacity, 1));
}

.dark\:hover\:text-gray-300:hover:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(209 213 219 / var(--tw-text-opacity, 1));
}

.dark\:hover\:text-teal-500:hover:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(20 184 166 / var(--tw-text-opacity, 1));
}

.dark\:hover\:text-white:hover:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(255 255 255 / var(--tw-text-opacity, 1));
}

.dark\:hover\:text-zinc-50:hover:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(250 250 250 / var(--tw-text-opacity, 1));
}

.dark\:hover\:ring-white\/20:hover:is(.dark *) {
  --tw-ring-color: rgb(255 255 255 / 0.2);
}

.dark\:active\:bg-zinc-700:active:is(.dark *) {
  --tw-bg-opacity: 1;
  background-color: rgb(63 63 70 / var(--tw-bg-opacity, 1));
}

.dark\:active\:bg-zinc-800\/50:active:is(.dark *) {
  background-color: rgb(39 39 42 / 0.5);
}

.dark\:active\:text-zinc-100\/70:active:is(.dark *) {
  color: rgb(244 244 245 / 0.7);
}

.dark\:active\:text-zinc-50\/70:active:is(.dark *) {
  color: rgb(250 250 250 / 0.7);
}

.group\/item:hover .dark\:group-hover\/item\:bg-blue-950\/20:is(.dark *) {
  background-color: rgb(23 37 84 / 0.2);
}

.group\/item:hover .dark\:group-hover\/item\:bg-green-950\/20:is(.dark *) {
  background-color: rgb(5 46 22 / 0.2);
}

.group\/item:hover .dark\:group-hover\/item\:bg-orange-950\/20:is(.dark *) {
  background-color: rgb(67 20 7 / 0.2);
}

.group\/item:hover .dark\:group-hover\/item\:bg-purple-950\/20:is(.dark *) {
  background-color: rgb(59 7 100 / 0.2);
}

.group:hover .dark\:group-hover\:stroke-zinc-400:is(.dark *) {
  stroke: #a1a1aa;
}

.group\/header:hover .dark\:group-hover\/header\:text-slate-100:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(241 245 249 / var(--tw-text-opacity, 1));
}

.group:hover .dark\:group-hover\:text-gray-100:is(.dark *) {
  --tw-text-opacity: 1;
  color: rgb(243 244 246 / var(--tw-text-opacity, 1));
}

@media (min-width: 640px) {

  .sm\:-inset-x-6 {
    left: -1.5rem;
    right: -1.5rem;
  }

  .sm\:mt-0 {
    margin-top: 0px;
  }

  .sm\:mt-12 {
    margin-top: 3rem;
  }

  .sm\:mt-16 {
    margin-top: 4rem;
  }

  .sm\:mt-20 {
    margin-top: 5rem;
  }

  .sm\:mt-24 {
    margin-top: 6rem;
  }

  .sm\:mt-32 {
    margin-top: 8rem;
  }

  .sm\:inline {
    display: inline;
  }

  .sm\:hidden {
    display: none;
  }

  .sm\:h-10 {
    height: 2.5rem;
  }

  .sm\:h-12 {
    height: 3rem;
  }

  .sm\:h-14 {
    height: 3.5rem;
  }

  .sm\:h-5 {
    height: 1.25rem;
  }

  .sm\:w-10 {
    width: 2.5rem;
  }

  .sm\:w-12 {
    width: 3rem;
  }

  .sm\:w-14 {
    width: 3.5rem;
  }

  .sm\:w-5 {
    width: 1.25rem;
  }

  .sm\:max-w-3xl {
    max-width: 48rem;
  }

  .sm\:columns-2 {
    -moz-columns: 2;
         columns: 2;
  }

  .sm\:columns-3 {
    -moz-columns: 3;
         columns: 3;
  }

  .sm\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .sm\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .sm\:flex-row {
    flex-direction: row;
  }

  .sm\:items-start {
    align-items: flex-start;
  }

  .sm\:items-center {
    align-items: center;
  }

  .sm\:justify-start {
    justify-content: flex-start;
  }

  .sm\:justify-between {
    justify-content: space-between;
  }

  .sm\:gap-10 {
    gap: 2.5rem;
  }

  .sm\:gap-3 {
    gap: 0.75rem;
  }

  .sm\:gap-6 {
    gap: 1.5rem;
  }

  .sm\:rounded-2xl {
    border-radius: 1rem;
  }

  .sm\:p-10 {
    padding: 2.5rem;
  }

  .sm\:p-4 {
    padding: 1rem;
  }

  .sm\:px-0 {
    padding-left: 0px;
    padding-right: 0px;
  }

  .sm\:px-12 {
    padding-left: 3rem;
    padding-right: 3rem;
  }

  .sm\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .sm\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .sm\:py-6 {
    padding-top: 1.5rem;
    padding-bottom: 1.5rem;
  }

  .sm\:pt-32 {
    padding-top: 8rem;
  }

  .sm\:text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }

  .sm\:text-4xl {
    font-size: 2rem;
    line-height: 2.5rem;
  }

  .sm\:text-5xl {
    font-size: 3rem;
    line-height: 3.5rem;
  }

  .sm\:text-base {
    font-size: 1rem;
    line-height: 1.75rem;
  }

  .sm\:text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }

  .sm\:text-sm {
    font-size: 0.875rem;
    line-height: 1.5rem;
  }

  .sm\:text-xl {
    font-size: 1.25rem;
    line-height: 2rem;
  }
}

@media (min-width: 768px) {

  .md\:relative {
    position: relative;
  }

  .md\:bottom-20 {
    bottom: 5rem;
  }

  .md\:bottom-6 {
    bottom: 1.5rem;
  }

  .md\:left-6 {
    left: 1.5rem;
  }

  .md\:right-6 {
    right: 1.5rem;
  }

  .md\:top-6 {
    top: 1.5rem;
  }

  .md\:col-span-3 {
    grid-column: span 3 / span 3;
  }

  .md\:col-span-4 {
    grid-column: span 4 / span 4;
  }

  .md\:mr-2 {
    margin-right: 0.5rem;
  }

  .md\:block {
    display: block;
  }

  .md\:grid {
    display: grid;
  }

  .md\:hidden {
    display: none;
  }

  .md\:h-12 {
    height: 3rem;
  }

  .md\:h-16 {
    height: 4rem;
  }

  .md\:h-5 {
    height: 1.25rem;
  }

  .md\:h-6 {
    height: 1.5rem;
  }

  .md\:h-80 {
    height: 20rem;
  }

  .md\:w-12 {
    width: 3rem;
  }

  .md\:w-16 {
    width: 4rem;
  }

  .md\:w-5 {
    width: 1.25rem;
  }

  .md\:w-6 {
    width: 1.5rem;
  }

  .md\:max-w-4xl {
    max-width: 56rem;
  }

  .md\:max-w-\[80vw\] {
    max-width: 80vw;
  }

  .md\:max-w-none {
    max-width: none;
  }

  .md\:flex-1 {
    flex: 1 1 0%;
  }

  .md\:columns-3 {
    -moz-columns: 3;
         columns: 3;
  }

  .md\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .md\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .md\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .md\:flex-row {
    flex-direction: row;
  }

  .md\:items-start {
    align-items: flex-start;
  }

  .md\:justify-center {
    justify-content: center;
  }

  .md\:gap-2 {
    gap: 0.5rem;
  }

  .md\:gap-4 {
    gap: 1rem;
  }

  .md\:rounded-lg {
    border-radius: var(--radius);
  }

  .md\:border-l {
    border-left-width: 1px;
  }

  .md\:border-zinc-100 {
    --tw-border-opacity: 1;
    border-color: rgb(244 244 245 / var(--tw-border-opacity, 1));
  }

  .md\:px-4 {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  .md\:px-6 {
    padding-left: 1.5rem;
    padding-right: 1.5rem;
  }

  .md\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .md\:py-12 {
    padding-top: 3rem;
    padding-bottom: 3rem;
  }

  .md\:py-2 {
    padding-top: 0.5rem;
    padding-bottom: 0.5rem;
  }

  .md\:py-3 {
    padding-top: 0.75rem;
    padding-bottom: 0.75rem;
  }

  .md\:pl-6 {
    padding-left: 1.5rem;
  }

  .md\:text-3xl {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }

  .md\:text-4xl {
    font-size: 2rem;
    line-height: 2.5rem;
  }

  .md\:text-5xl {
    font-size: 3rem;
    line-height: 3.5rem;
  }

  .md\:text-6xl {
    font-size: 3.75rem;
    line-height: 1;
  }

  .md\:text-lg {
    font-size: 1.125rem;
    line-height: 1.75rem;
  }

  .md\:text-sm {
    font-size: 0.875rem;
    line-height: 1.5rem;
  }

  .md\:dark\:border-zinc-700\/40:is(.dark *) {
    border-color: rgb(63 63 70 / 0.4);
  }
}

@media (min-width: 1024px) {

  .lg\:order-first {
    order: -9999;
  }

  .lg\:order-last {
    order: 9999;
  }

  .lg\:row-span-2 {
    grid-row: span 2 / span 2;
  }

  .lg\:ml-16 {
    margin-left: 4rem;
  }

  .lg\:mt-16 {
    margin-top: 4rem;
  }

  .lg\:mt-32 {
    margin-top: 8rem;
  }

  .lg\:block {
    display: block;
  }

  .lg\:flex {
    display: flex;
  }

  .lg\:hidden {
    display: none;
  }

  .lg\:h-96 {
    height: 24rem;
  }

  .lg\:min-h-\[500px\] {
    min-height: 500px;
  }

  .lg\:w-1\/2 {
    width: 50%;
  }

  .lg\:max-w-5xl {
    max-width: 64rem;
  }

  .lg\:max-w-none {
    max-width: none;
  }

  .lg\:columns-3 {
    -moz-columns: 3;
         columns: 3;
  }

  .lg\:columns-4 {
    -moz-columns: 4;
         columns: 4;
  }

  .lg\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }

  .lg\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .lg\:grid-cols-4 {
    grid-template-columns: repeat(4, minmax(0, 1fr));
  }

  .lg\:grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }

  .lg\:grid-rows-\[auto_1fr\] {
    grid-template-rows: auto 1fr;
  }

  .lg\:flex-row {
    flex-direction: row;
  }

  .lg\:gap-12 {
    gap: 3rem;
  }

  .lg\:gap-y-12 {
    row-gap: 3rem;
  }

  .lg\:p-6 {
    padding: 1.5rem;
  }

  .lg\:px-12 {
    padding-left: 3rem;
    padding-right: 3rem;
  }

  .lg\:px-16 {
    padding-left: 4rem;
    padding-right: 4rem;
  }

  .lg\:px-8 {
    padding-left: 2rem;
    padding-right: 2rem;
  }

  .lg\:py-20 {
    padding-top: 5rem;
    padding-bottom: 5rem;
  }

  .lg\:pl-16 {
    padding-left: 4rem;
  }

  .lg\:pl-20 {
    padding-left: 5rem;
  }

  .lg\:pr-20 {
    padding-right: 5rem;
  }

  .lg\:text-4xl {
    font-size: 2rem;
    line-height: 2.5rem;
  }

  .lg\:text-5xl {
    font-size: 3rem;
    line-height: 3.5rem;
  }

  .lg\:text-6xl {
    font-size: 3.75rem;
    line-height: 1;
  }
}

@media (min-width: 1280px) {

  .xl\:relative {
    position: relative;
  }

  .xl\:block {
    display: block;
  }

  .xl\:hidden {
    display: none;
  }

  .xl\:max-w-6xl {
    max-width: 72rem;
  }

  .xl\:columns-4 {
    -moz-columns: 4;
         columns: 4;
  }

  .xl\:columns-5 {
    -moz-columns: 5;
         columns: 5;
  }

  .xl\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr));
  }

  .xl\:grid-cols-5 {
    grid-template-columns: repeat(5, minmax(0, 1fr));
  }

  .xl\:pl-24 {
    padding-left: 6rem;
  }
}

@media (min-width: 1536px) {

  .\32xl\:block {
    display: block;
  }

  .\32xl\:hidden {
    display: none;
  }

  .\32xl\:max-w-7xl {
    max-width: 80rem;
  }
}

.\[\&\>\*\:first-child\]\:mt-0>*:first-child {
  margin-top: 0px;
}

.\[\&\>\*\:last-child\]\:mb-0>*:last-child {
  margin-bottom: 0px;
}

.\[\&\>svg\]\:size-4>svg {
  width: 1rem;
  height: 1rem;
}

.\[\&\>svg\]\:shrink-0>svg {
  flex-shrink: 0;
}

.\[\&_\.project-card-content\]\:p-6 .project-card-content {
  padding: 1.5rem;
}

.\[\&_\.project-card-desc\]\:text-sm .project-card-desc {
  font-size: 0.875rem;
  line-height: 1.5rem;
}

.\[\&_\.project-card-desc\]\:text-zinc-600 .project-card-desc {
  --tw-text-opacity: 1;
  color: rgb(82 82 91 / var(--tw-text-opacity, 1));
}

.\[\&_\.project-card-desc\]\:dark\:text-zinc-400:is(.dark *) .project-card-desc {
  --tw-text-opacity: 1;
  color: rgb(161 161 170 / var(--tw-text-opacity, 1));
}

.\[\&_\.project-card-link-icon\]\:h-4 .project-card-link-icon {
  height: 1rem;
}

.\[\&_\.project-card-link-icon\]\:w-4 .project-card-link-icon {
  width: 1rem;
}

.\[\&_\.project-card-link-text\]\:mr-2 .project-card-link-text {
  margin-right: 0.5rem;
}

.\[\&_\.project-card-link\]\:inline-flex .project-card-link {
  display: inline-flex;
}

.\[\&_\.project-card-link\]\:items-center .project-card-link {
  align-items: center;
}

.\[\&_\.project-card-link\]\:text-sm .project-card-link {
  font-size: 0.875rem;
  line-height: 1.5rem;
}

.\[\&_\.project-card-link\]\:font-medium .project-card-link {
  font-weight: 500;
}

.\[\&_\.project-card-link\]\:text-primary .project-card-link {
  color: hsl(var(--primary));
}

.\[\&_\.project-card-link\]\:no-underline .project-card-link {
  text-decoration-line: none;
}

.\[\&_\.project-card-title\]\:mb-2 .project-card-title {
  margin-bottom: 0.5rem;
}

.\[\&_\.project-card-title\]\:text-xl .project-card-title {
  font-size: 1.25rem;
  line-height: 2rem;
}

.\[\&_\.project-card-title\]\:font-semibold .project-card-title {
  font-weight: 600;
}

.\[\&_\.project-card-title\]\:text-zinc-900 .project-card-title {
  --tw-text-opacity: 1;
  color: rgb(24 24 27 / var(--tw-text-opacity, 1));
}

.\[\&_\.project-card-title\]\:dark\:text-zinc-100:is(.dark *) .project-card-title {
  --tw-text-opacity: 1;
  color: rgb(244 244 245 / var(--tw-text-opacity, 1));
}

.\[\&_\.project-card\:hover\]\:-translate-y-1 .project-card:hover {
  --tw-translate-y: -0.25rem;
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.\[\&_\.project-card\:hover\]\:transform .project-card:hover {
  transform: translate(var(--tw-translate-x), var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y));
}

.\[\&_\.project-card\:hover\]\:shadow-lg .project-card:hover {
  --tw-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --tw-shadow-colored: 0 10px 15px -3px var(--tw-shadow-color), 0 4px 6px -2px var(--tw-shadow-color);
  box-shadow: var(--tw-ring-offset-shadow, 0 0 #0000), var(--tw-ring-shadow, 0 0 #0000), var(--tw-shadow);
}

.\[\&_\.project-card\]\:my-4 .project-card {
  margin-top: 1rem;
  margin-bottom: 1rem;
}

.\[\&_\.project-card\]\:overflow-hidden .project-card {
  overflow: hidden;
}

.\[\&_\.project-card\]\:rounded-lg .project-card {
  border-radius: var(--radius);
}

.\[\&_\.project-card\]\:border .project-card {
  border-width: 1px;
}

.\[\&_\.project-card\]\:border-zinc-200 .project-card {
  --tw-border-opacity: 1;
  border-color: rgb(228 228 231 / var(--tw-border-opacity, 1));
}

.\[\&_\.project-card\]\:bg-white .project-card {
  --tw-bg-opacity: 1;
  background-color: rgb(255 255 255 / var(--tw-bg-opacity, 1));
}

.\[\&_\.project-card\]\:transition-all .project-card {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.\[\&_\.project-card\]\:duration-300 .project-card {
  transition-duration: 300ms;
  animation-duration: 300ms;
}

.\[\&_\.project-card\]\:dark\:border-zinc-700:is(.dark *) .project-card {
  --tw-border-opacity: 1;
  border-color: rgb(63 63 70 / var(--tw-border-opacity, 1));
}

.\[\&_\.project-card\]\:dark\:bg-zinc-800:is(.dark *) .project-card {
  --tw-bg-opacity: 1;
  background-color: rgb(39 39 42 / var(--tw-bg-opacity, 1));
}

.\[\&_svg\]\:pointer-events-none svg {
  pointer-events: none;
}

.\[\&_svg\]\:size-4 svg {
  width: 1rem;
  height: 1rem;
}

.\[\&_svg\]\:shrink-0 svg {
  flex-shrink: 0;
}

