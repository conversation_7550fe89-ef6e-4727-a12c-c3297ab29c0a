"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/visit-stats/route";
exports.ids = ["app/api/visit-stats/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fvisit-stats%2Froute&page=%2Fapi%2Fvisit-stats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fvisit-stats%2Froute.ts&appDir=%2Fhome%2Fyao%2FCode%2Fme%2FMy-web%2Ffrontend%2Fsrc%2Fapp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&pageExtensions=mdx&rootDir=%2Fhome%2Fyao%2FCode%2Fme%2FMy-web%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fvisit-stats%2Froute&page=%2Fapi%2Fvisit-stats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fvisit-stats%2Froute.ts&appDir=%2Fhome%2Fyao%2FCode%2Fme%2FMy-web%2Ffrontend%2Fsrc%2Fapp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&pageExtensions=mdx&rootDir=%2Fhome%2Fyao%2FCode%2Fme%2FMy-web%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _home_yao_Code_me_My_web_frontend_src_app_api_visit_stats_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/visit-stats/route.ts */ \"(rsc)/./src/app/api/visit-stats/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"standalone\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/visit-stats/route\",\n        pathname: \"/api/visit-stats\",\n        filename: \"route\",\n        bundlePath: \"app/api/visit-stats/route\"\n    },\n    resolvedPagePath: \"/home/<USER>/Code/me/My-web/frontend/src/app/api/visit-stats/route.ts\",\n    nextConfigOutput,\n    userland: _home_yao_Code_me_My_web_frontend_src_app_api_visit_stats_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/visit-stats/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fvisit-stats%2Froute&page=%2Fapi%2Fvisit-stats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fvisit-stats%2Froute.ts&appDir=%2Fhome%2Fyao%2FCode%2Fme%2FMy-web%2Ffrontend%2Fsrc%2Fapp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&pageExtensions=mdx&rootDir=%2Fhome%2Fyao%2FCode%2Fme%2FMy-web%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/visit-stats/route.ts":
/*!******************************************!*\
  !*** ./src/app/api/visit-stats/route.ts ***!
  \******************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n\nconst OPENPANEL_API_URL = \"https://api.openpanel.dev\";\nconst OPENPANEL_CLIENT_ID = process.env.NEXT_PUBLIC_OPENPANEL_CLIENT_ID;\nconst OPENPANEL_SECRET_ID = process.env.OPENPANEL_API_SECRET_ID;\nconst OPENPANEL_PROJECT_ID = process.env.OPENPANEL_PROJECT_ID;\n// 默认的回退数据\nconst DEFAULT_STATS = {\n    totalUV: 1000,\n    dailyUV: 100\n};\nasync function GET() {\n    try {\n        // 检查是否所有必要的环境变量都已设置\n        if (!OPENPANEL_CLIENT_ID || !OPENPANEL_SECRET_ID || !OPENPANEL_PROJECT_ID) {\n            console.warn(\"OpenPanel API credentials not properly configured. Using default values.\");\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(DEFAULT_STATS);\n        }\n        // 获取总访问数据\n        const response = await fetch(`${OPENPANEL_API_URL}/export/events?projectId=${OPENPANEL_PROJECT_ID}&event=screen_view`, {\n            headers: {\n                \"openpanel-client-id\": OPENPANEL_CLIENT_ID,\n                \"openpanel-client-secret\": OPENPANEL_SECRET_ID\n            }\n        });\n        if (!response.ok) {\n            console.warn(\"Failed to fetch total visit stats. Using default values.\");\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(DEFAULT_STATS);\n        }\n        const data = await response.json();\n        const totalUV = data?.meta?.totalCount || DEFAULT_STATS.totalUV;\n        // 获取今日访问数据\n        // 昨天的 yyyy-MM-dd\n        const today = new Date();\n        const yesterday = new Date(today);\n        yesterday.setDate(yesterday.getDate() - 1);\n        const yesterdayStr = yesterday.toISOString().split(\"T\")[0];\n        // 今天的 yyyy-MM-dd\n        const todayStr = today.toISOString().split(\"T\")[0];\n        const todayResponse = await fetch(`${OPENPANEL_API_URL}/export/events?projectId=${OPENPANEL_PROJECT_ID}&event=screen_view&start=${yesterdayStr}&end=${todayStr}`, {\n            headers: {\n                \"openpanel-client-id\": OPENPANEL_CLIENT_ID,\n                \"openpanel-client-secret\": OPENPANEL_SECRET_ID\n            }\n        });\n        if (!todayResponse.ok) {\n            console.warn(\"Failed to fetch daily visit stats. Using default values for daily stats.\");\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                totalUV,\n                dailyUV: DEFAULT_STATS.dailyUV\n            });\n        }\n        const todayData = await todayResponse.json();\n        const dailyUV = todayData?.meta?.totalCount || DEFAULT_STATS.dailyUV;\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            totalUV,\n            dailyUV\n        });\n    } catch (error) {\n        console.error(\"Error fetching visit stats:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(DEFAULT_STATS, {\n            status: 200\n        }); // 返回200状态码和默认数据\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/visit-stats/route.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fvisit-stats%2Froute&page=%2Fapi%2Fvisit-stats%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fvisit-stats%2Froute.ts&appDir=%2Fhome%2Fyao%2FCode%2Fme%2FMy-web%2Ffrontend%2Fsrc%2Fapp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&pageExtensions=mdx&rootDir=%2Fhome%2Fyao%2FCode%2Fme%2FMy-web%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();