"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/waline/comment/count/route";
exports.ids = ["app/api/waline/comment/count/route"];
exports.modules = {

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fwaline%2Fcomment%2Fcount%2Froute&page=%2Fapi%2Fwaline%2Fcomment%2Fcount%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fwaline%2Fcomment%2Fcount%2Froute.ts&appDir=%2Fhome%2Fyao%2FCode%2Fme%2FMy-web%2Ffrontend%2Fsrc%2Fapp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&pageExtensions=mdx&rootDir=%2Fhome%2Fyao%2FCode%2Fme%2FMy-web%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fwaline%2Fcomment%2Fcount%2Froute&page=%2Fapi%2Fwaline%2Fcomment%2Fcount%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fwaline%2Fcomment%2Fcount%2Froute.ts&appDir=%2Fhome%2Fyao%2FCode%2Fme%2FMy-web%2Ffrontend%2Fsrc%2Fapp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&pageExtensions=mdx&rootDir=%2Fhome%2Fyao%2FCode%2Fme%2FMy-web%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _home_yao_Code_me_My_web_frontend_src_app_api_waline_comment_count_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/waline/comment/count/route.ts */ \"(rsc)/./src/app/api/waline/comment/count/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"standalone\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/waline/comment/count/route\",\n        pathname: \"/api/waline/comment/count\",\n        filename: \"route\",\n        bundlePath: \"app/api/waline/comment/count/route\"\n    },\n    resolvedPagePath: \"/home/<USER>/Code/me/My-web/frontend/src/app/api/waline/comment/count/route.ts\",\n    nextConfigOutput,\n    userland: _home_yao_Code_me_My_web_frontend_src_app_api_waline_comment_count_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/waline/comment/count/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fwaline%2Fcomment%2Fcount%2Froute&page=%2Fapi%2Fwaline%2Fcomment%2Fcount%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fwaline%2Fcomment%2Fcount%2Froute.ts&appDir=%2Fhome%2Fyao%2FCode%2Fme%2FMy-web%2Ffrontend%2Fsrc%2Fapp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&pageExtensions=mdx&rootDir=%2Fhome%2Fyao%2FCode%2Fme%2FMy-web%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./src/app/api/waline/comment/count/route.ts":
/*!***************************************************!*\
  !*** ./src/app/api/waline/comment/count/route.ts ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_walineConfig__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/walineConfig */ \"(rsc)/./src/lib/walineConfig.ts\");\n\n\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const path = searchParams.get(\"path\");\n        const paths = searchParams.getAll(\"paths\") // 支持批量查询\n        ;\n        if (!path && paths.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Path parameter is required\"\n            }, {\n                status: 400\n            });\n        }\n        // 获取Waline配置\n        const config = await (0,_lib_walineConfig__WEBPACK_IMPORTED_MODULE_1__.getWalineConfig)();\n        // 准备路径数组\n        const pathsToQuery = paths.length > 0 ? paths : [\n            path\n        ];\n        // 直接调用Waline API获取评论数量\n        const walineApiUrl = `${config.serverURL}/api/comment`;\n        const params = new URLSearchParams({\n            type: \"count\",\n            url: pathsToQuery.join(\",\")\n        });\n        const response = await fetch(`${walineApiUrl}?${params}`, {\n            headers: {\n                \"Accept\": \"application/json\"\n            }\n        });\n        if (!response.ok) {\n            throw new Error(`Waline API error: ${response.status}`);\n        }\n        const commentCounts = await response.json();\n        // 如果只查询单个路径，返回单个数值\n        if (path && paths.length === 0) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: true,\n                data: commentCounts[0] || 0,\n                path\n            });\n        }\n        // 批量查询返回数组\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: commentCounts,\n            paths: pathsToQuery\n        });\n    } catch (error) {\n        console.error(\"Waline comment count API error:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to fetch comment count\",\n            details: error instanceof Error ? error.message : \"Unknown error\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/waline/comment/count/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/walineConfig.ts":
/*!*********************************!*\
  !*** ./src/lib/walineConfig.ts ***!
  \*********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   clearWalineConfigCache: () => (/* binding */ clearWalineConfigCache),\n/* harmony export */   getWalineConfig: () => (/* binding */ getWalineConfig),\n/* harmony export */   getWalineLang: () => (/* binding */ getWalineLang),\n/* harmony export */   getWalineServerURL: () => (/* binding */ getWalineServerURL)\n/* harmony export */ });\n// 缓存配置以避免重复请求\nlet cachedConfig = null;\nlet configPromise = null;\n/**\n * 从后台API获取Waline配置\n */ async function fetchWalineConfig() {\n    try {\n        const response = await fetch(\"/api/system-config/waline/config\", {\n            method: \"GET\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            }\n        });\n        if (!response.ok) {\n            throw new Error(`HTTP ${response.status}: ${response.statusText}`);\n        }\n        const data = await response.json();\n        return {\n            serverURL: data.server_url,\n            lang: data.lang || \"zh-CN\"\n        };\n    } catch (error) {\n        console.error(\"获取Waline配置失败:\", error);\n        // 返回默认配置作为后备\n        return {\n            serverURL: \"https://waline.jyaochen.cn\",\n            lang: \"zh-CN\"\n        };\n    }\n}\n/**\n * 获取Waline配置（带缓存）\n */ async function getWalineConfig() {\n    // 如果已有缓存，直接返回\n    if (cachedConfig) {\n        return cachedConfig;\n    }\n    // 如果正在请求中，等待请求完成\n    if (configPromise) {\n        return configPromise;\n    }\n    // 发起新请求\n    configPromise = fetchWalineConfig();\n    try {\n        cachedConfig = await configPromise;\n        return cachedConfig;\n    } catch (error) {\n        // 请求失败时清除promise，允许重试\n        configPromise = null;\n        throw error;\n    }\n}\n/**\n * 清除配置缓存（用于配置更新后刷新）\n */ function clearWalineConfigCache() {\n    cachedConfig = null;\n    configPromise = null;\n}\n/**\n * 获取Waline服务器URL\n */ async function getWalineServerURL() {\n    const config = await getWalineConfig();\n    return config.serverURL;\n}\n/**\n * 获取Waline语言设置\n */ async function getWalineLang() {\n    const config = await getWalineConfig();\n    return config.lang;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/walineConfig.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fwaline%2Fcomment%2Fcount%2Froute&page=%2Fapi%2Fwaline%2Fcomment%2Fcount%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fwaline%2Fcomment%2Fcount%2Froute.ts&appDir=%2Fhome%2Fyao%2FCode%2Fme%2FMy-web%2Ffrontend%2Fsrc%2Fapp&pageExtensions=js&pageExtensions=jsx&pageExtensions=ts&pageExtensions=tsx&pageExtensions=mdx&rootDir=%2Fhome%2Fyao%2FCode%2Fme%2FMy-web%2Ffrontend&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();