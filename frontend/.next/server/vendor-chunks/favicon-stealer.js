"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/favicon-stealer";
exports.ids = ["vendor-chunks/favicon-stealer"];
exports.modules = {

/***/ "(ssr)/./node_modules/favicon-stealer/dist/Favicon.js":
/*!******************************************************!*\
  !*** ./node_modules/favicon-stealer/dist/Favicon.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("/* __next_internal_client_entry_do_not_use__  cjs */ \nvar __createBinding = (void 0) && (void 0).__createBinding || (Object.create ? function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n        desc = {\n            enumerable: true,\n            get: function() {\n                return m[k];\n            }\n        };\n    }\n    Object.defineProperty(o, k2, desc);\n} : function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n});\nvar __setModuleDefault = (void 0) && (void 0).__setModuleDefault || (Object.create ? function(o, v) {\n    Object.defineProperty(o, \"default\", {\n        enumerable: true,\n        value: v\n    });\n} : function(o, v) {\n    o[\"default\"] = v;\n});\nvar __importStar = (void 0) && (void 0).__importStar || function() {\n    var ownKeys = function(o) {\n        ownKeys = Object.getOwnPropertyNames || function(o) {\n            var ar = [];\n            for(var k in o)if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;\n            return ar;\n        };\n        return ownKeys(o);\n    };\n    return function(mod) {\n        if (mod && mod.__esModule) return mod;\n        var result = {};\n        if (mod != null) {\n            for(var k = ownKeys(mod), i = 0; i < k.length; i++)if (k[i] !== \"default\") __createBinding(result, mod, k[i]);\n        }\n        __setModuleDefault(result, mod);\n        return result;\n    };\n}();\nObject.defineProperty(exports, \"__esModule\", ({\n    value: true\n}));\nconst utils_1 = __webpack_require__(/*! ./lib/utils */ \"(ssr)/./node_modules/favicon-stealer/dist/lib/utils/index.js\");\nconst react_1 = __importStar(__webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\"));\nconst Favicon = ({ url, src, alt, size = 32, className = \"\", timeout = 3000, border = false, padding = 0, background = \"transparent\", borderRadius = 0, lazy = false, preferFallback = false, preferSrc = true })=>{\n    const domain = (0, utils_1.getDomain)(url);\n    const [imgSrc, setImgSrc] = (0, react_1.useState)(\"\");\n    const [fallbackIndex, setFallbackIndex] = (0, react_1.useState)(0);\n    const [isLoading, setIsLoading] = (0, react_1.useState)(true);\n    const [hasError, setHasError] = (0, react_1.useState)(false);\n    const [isInitialized, setIsInitialized] = (0, react_1.useState)(false);\n    const standardSources = [\n        `https://${domain}/favicon.ico`,\n        `https://${domain}/logo.svg`,\n        `https://${domain}/logo.png`,\n        `https://${domain}/apple-touch-icon.png`,\n        `https://${domain}/apple-touch-icon-precomposed.png`,\n        `https://${domain}/static/img/favicon.ico`,\n        `https://${domain}/static/img/favicon.png`,\n        `https://${domain}/img/favicon.png`,\n        `https://${domain}/img/favicon.ico`,\n        `https://${domain}/static/img/logo.svg`,\n        `https://${domain}/apple-touch-icon-precomposed.png`\n    ];\n    const fallbackServices = [\n        `https://favicon.im/${domain}?larger=true`,\n        `https://favicon.im/${domain}`,\n        `https://www.google.com/s2/favicons?domain=https://${domain}&sz=64`,\n        `https://www.google.com/s2/favicons?domain=http://${domain}&sz=64`\n    ];\n    const fallbackSources = preferFallback ? [\n        ...fallbackServices,\n        ...standardSources\n    ] : [\n        ...standardSources,\n        ...fallbackServices\n    ];\n    (0, react_1.useEffect)(()=>{\n        if (!isInitialized) {\n            if (src) {\n                setIsLoading(false);\n            } else {\n                setImgSrc(fallbackSources[0]);\n            }\n            setIsInitialized(true);\n        }\n    }, [\n        isInitialized,\n        fallbackSources,\n        src\n    ]);\n    (0, react_1.useEffect)(()=>{\n        let timeoutId;\n        if (isLoading && imgSrc && !src) {\n            timeoutId = setTimeout(()=>{\n                handleError();\n            }, timeout);\n        }\n        return ()=>{\n            if (timeoutId) {\n                clearTimeout(timeoutId);\n            }\n        };\n    }, [\n        imgSrc,\n        isLoading,\n        timeout,\n        src\n    ]);\n    const handleError = ()=>{\n        const nextIndex = fallbackIndex + 1;\n        if (nextIndex < fallbackSources.length) {\n            setFallbackIndex(nextIndex);\n            setImgSrc(fallbackSources[nextIndex]);\n            setIsLoading(true);\n        } else {\n            setHasError(true);\n            setIsLoading(false);\n        }\n    };\n    const handleLoad = ()=>{\n        setIsLoading(false);\n        setHasError(false);\n    };\n    return react_1.default.createElement(\"div\", {\n        className: `relative inline-block \n        ${className} \n        ${border ? \"border\" : \"\"} \n        ${hasError ? \"opacity-0\" : \"\"}\n        ${padding ? `p-[${padding}px]` : \"\"}\n        ${borderRadius ? `rounded-[${borderRadius}px]` : \"\"}\n        `,\n        style: {\n            width: size,\n            height: size,\n            background: background,\n            padding: padding ? `${padding}px` : 0,\n            borderRadius: borderRadius ? `${borderRadius}px` : 0\n        }\n    }, isLoading && react_1.default.createElement(\"div\", {\n        className: \"absolute inset-0 animate-pulse\"\n    }, react_1.default.createElement(\"div\", {\n        className: \"w-full h-full rounded-md bg-gray-200/60\"\n    })), (src || imgSrc) && react_1.default.createElement(\"img\", {\n        src: src || imgSrc,\n        alt: alt || `${domain} logo`,\n        width: size,\n        height: size,\n        loading: lazy ? \"lazy\" : \"eager\",\n        onError: handleError,\n        onLoad: handleLoad,\n        className: `inline-block transition-opacity duration-300 ${isLoading ? \"opacity-0\" : \"opacity-100\"}`,\n        style: {\n            objectFit: \"contain\",\n            display: hasError ? \"none\" : \"inline-block\"\n        }\n    }), hasError && react_1.default.createElement(\"div\", {\n        className: \"w-full h-full flex items-center justify-center bg-gray-100 rounded-md\",\n        style: {\n            fontSize: `${size * 0.5}px`\n        }\n    }, domain.charAt(0).toUpperCase()));\n};\nexports[\"default\"] = Favicon;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/favicon-stealer/dist/Favicon.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/favicon-stealer/dist/index.js":
/*!****************************************************!*\
  !*** ./node_modules/favicon-stealer/dist/index.js ***!
  \****************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __importDefault = (this && this.__importDefault) || function (mod) {\n    return (mod && mod.__esModule) ? mod : { \"default\": mod };\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.Favicon = void 0;\nvar Favicon_1 = __webpack_require__(/*! ./Favicon */ \"(ssr)/./node_modules/favicon-stealer/dist/Favicon.js\");\nObject.defineProperty(exports, \"Favicon\", ({ enumerable: true, get: function () { return __importDefault(Favicon_1).default; } }));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZmF2aWNvbi1zdGVhbGVyL2Rpc3QvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYjtBQUNBLDZDQUE2QztBQUM3QztBQUNBLDhDQUE2QyxFQUFFLGFBQWEsRUFBQztBQUM3RCxlQUFlO0FBQ2YsZ0JBQWdCLG1CQUFPLENBQUMsdUVBQVc7QUFDbkMsMkNBQTBDLEVBQUUscUNBQXFDLDhDQUE4QyxFQUFDIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vY29yZXljaGl1LXBvcnRmb2xpby10ZW1wbGF0ZS8uL25vZGVfbW9kdWxlcy9mYXZpY29uLXN0ZWFsZXIvZGlzdC9pbmRleC5qcz9kOGRkIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xudmFyIF9faW1wb3J0RGVmYXVsdCA9ICh0aGlzICYmIHRoaXMuX19pbXBvcnREZWZhdWx0KSB8fCBmdW5jdGlvbiAobW9kKSB7XG4gICAgcmV0dXJuIChtb2QgJiYgbW9kLl9fZXNNb2R1bGUpID8gbW9kIDogeyBcImRlZmF1bHRcIjogbW9kIH07XG59O1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5GYXZpY29uID0gdm9pZCAwO1xudmFyIEZhdmljb25fMSA9IHJlcXVpcmUoXCIuL0Zhdmljb25cIik7XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJGYXZpY29uXCIsIHsgZW51bWVyYWJsZTogdHJ1ZSwgZ2V0OiBmdW5jdGlvbiAoKSB7IHJldHVybiBfX2ltcG9ydERlZmF1bHQoRmF2aWNvbl8xKS5kZWZhdWx0OyB9IH0pO1xuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/favicon-stealer/dist/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/favicon-stealer/dist/lib/utils/index.js":
/*!**************************************************************!*\
  !*** ./node_modules/favicon-stealer/dist/lib/utils/index.js ***!
  \**************************************************************/
/***/ ((__unused_webpack_module, exports) => {

eval("\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.getDomain = void 0;\nconst getDomain = (url) => {\n    try {\n        // Add https:// protocol if not present\n        const urlWithProtocol = url.startsWith('http') ? url : `https://${url}`;\n        const domain = new URL(urlWithProtocol).hostname;\n        // Remove 'www.' prefix if exists\n        return domain.replace(/^www\\./, '');\n    }\n    catch (error) {\n        // Return original input if URL parsing fails\n        return url;\n    }\n};\nexports.getDomain = getDomain;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvZmF2aWNvbi1zdGVhbGVyL2Rpc3QvbGliL3V0aWxzL2luZGV4LmpzIiwibWFwcGluZ3MiOiJBQUFhO0FBQ2IsOENBQTZDLEVBQUUsYUFBYSxFQUFDO0FBQzdELGlCQUFpQjtBQUNqQjtBQUNBO0FBQ0E7QUFDQSwwRUFBMEUsSUFBSTtBQUM5RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpQkFBaUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9jb3JleWNoaXUtcG9ydGZvbGlvLXRlbXBsYXRlLy4vbm9kZV9tb2R1bGVzL2Zhdmljb24tc3RlYWxlci9kaXN0L2xpYi91dGlscy9pbmRleC5qcz85M2YzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xuT2JqZWN0LmRlZmluZVByb3BlcnR5KGV4cG9ydHMsIFwiX19lc01vZHVsZVwiLCB7IHZhbHVlOiB0cnVlIH0pO1xuZXhwb3J0cy5nZXREb21haW4gPSB2b2lkIDA7XG5jb25zdCBnZXREb21haW4gPSAodXJsKSA9PiB7XG4gICAgdHJ5IHtcbiAgICAgICAgLy8gQWRkIGh0dHBzOi8vIHByb3RvY29sIGlmIG5vdCBwcmVzZW50XG4gICAgICAgIGNvbnN0IHVybFdpdGhQcm90b2NvbCA9IHVybC5zdGFydHNXaXRoKCdodHRwJykgPyB1cmwgOiBgaHR0cHM6Ly8ke3VybH1gO1xuICAgICAgICBjb25zdCBkb21haW4gPSBuZXcgVVJMKHVybFdpdGhQcm90b2NvbCkuaG9zdG5hbWU7XG4gICAgICAgIC8vIFJlbW92ZSAnd3d3LicgcHJlZml4IGlmIGV4aXN0c1xuICAgICAgICByZXR1cm4gZG9tYWluLnJlcGxhY2UoL153d3dcXC4vLCAnJyk7XG4gICAgfVxuICAgIGNhdGNoIChlcnJvcikge1xuICAgICAgICAvLyBSZXR1cm4gb3JpZ2luYWwgaW5wdXQgaWYgVVJMIHBhcnNpbmcgZmFpbHNcbiAgICAgICAgcmV0dXJuIHVybDtcbiAgICB9XG59O1xuZXhwb3J0cy5nZXREb21haW4gPSBnZXREb21haW47XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/favicon-stealer/dist/lib/utils/index.js\n");

/***/ })

};
;