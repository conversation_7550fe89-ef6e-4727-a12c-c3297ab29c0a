"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@openpanel";
exports.ids = ["vendor-chunks/@openpanel"];
exports.modules = {

/***/ "(rsc)/./node_modules/@openpanel/nextjs/dist/index.js":
/*!******************************************************!*\
  !*** ./node_modules/@openpanel/nextjs/dist/index.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   IdentifyComponent: () => (/* binding */ k),\n/* harmony export */   OpenPanel: () => (/* reexport safe */ _openpanel_web__WEBPACK_IMPORTED_MODULE_2__.OpenPanel),\n/* harmony export */   OpenPanelBase: () => (/* reexport safe */ _openpanel_web__WEBPACK_IMPORTED_MODULE_2__.OpenPanelBase),\n/* harmony export */   OpenPanelComponent: () => (/* binding */ O),\n/* harmony export */   SetGlobalPropertiesComponent: () => (/* binding */ I),\n/* harmony export */   useOpenPanel: () => (/* binding */ S)\n/* harmony export */ });\n/* harmony import */ var next_script_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/script.js */ \"(rsc)/./node_modules/next/dist/api/script.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var _openpanel_web__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @openpanel/web */ \"(rsc)/./node_modules/@openpanel/web/dist/index.js\");\nvar a=\"https://openpanel.dev/op1.js\",d=n=>typeof n==\"object\"&&n!==null&&n!==void 0?`{${Object.entries(n).map(([r,t])=>r===\"filter\"?`\"${r}\":${t}`:`\"${r}\":${JSON.stringify(t)}`).join(\",\")}}`:JSON.stringify(n);function O({profileId:n,cdnUrl:o,globalProperties:r,...t}){let p=[{name:\"init\",value:{...t,sdk:\"nextjs\",sdkVersion:\"1.0.8\"}}];return n&&p.push({name:\"identify\",value:{profileId:n}}),r&&p.push({name:\"setGlobalProperties\",value:r}),react__WEBPACK_IMPORTED_MODULE_1__.createElement(react__WEBPACK_IMPORTED_MODULE_1__.Fragment,null,react__WEBPACK_IMPORTED_MODULE_1__.createElement(next_script_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"],{src:o??a,async:!0,defer:!0}),react__WEBPACK_IMPORTED_MODULE_1__.createElement(next_script_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"],{strategy:\"beforeInteractive\",dangerouslySetInnerHTML:{__html:`window.op = window.op || function(...args) {(window.op.q = window.op.q || []).push(args)};\n          ${p.map(s=>`window.op('${s.name}', ${d(s.value)});`).join(`\n`)}`}}))}function k(n){return react__WEBPACK_IMPORTED_MODULE_1__.createElement(react__WEBPACK_IMPORTED_MODULE_1__.Fragment,null,react__WEBPACK_IMPORTED_MODULE_1__.createElement(next_script_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"],{dangerouslySetInnerHTML:{__html:`window.op('identify', ${JSON.stringify(n)});`}}))}function I(n){return react__WEBPACK_IMPORTED_MODULE_1__.createElement(react__WEBPACK_IMPORTED_MODULE_1__.Fragment,null,react__WEBPACK_IMPORTED_MODULE_1__.createElement(next_script_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"],{dangerouslySetInnerHTML:{__html:`window.op('setGlobalProperties', ${JSON.stringify(n)});`}}))}function S(){return{track:f,screenView:c,identify:u,increment:w,decrement:m,clear:P,setGlobalProperties:l}}function l(n){window.op?.(\"setGlobalProperties\",n)}function f(n,o){window.op?.(\"track\",n,o)}function c(n,o){window.op?.(\"screenView\",n,o)}function u(n){window.op?.(\"identify\",n)}function w(n){window.op?.(\"increment\",n)}function m(n){window.op(\"decrement\",n)}function P(){window.op?.(\"clear\")}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@openpanel/nextjs/dist/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@openpanel/sdk/dist/index.js":
/*!***************************************************!*\
  !*** ./node_modules/@openpanel/sdk/dist/index.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OpenPanel: () => (/* binding */ o)\n/* harmony export */ });\nvar a=class{constructor(e){this.baseUrl=e.baseUrl,this.headers={\"Content-Type\":\"application/json\",...e.defaultHeaders},this.maxRetries=e.maxRetries??3,this.initialRetryDelay=e.initialRetryDelay??500}async resolveHeaders(){let e={};for(let[r,s]of Object.entries(this.headers)){let t=await s;t!==null&&(e[r]=t)}return e}addHeader(e,r){this.headers[e]=r}async post(e,r,s,t){try{let i=await fetch(e,{method:\"POST\",headers:await this.resolveHeaders(),body:JSON.stringify(r??{}),keepalive:!0,...s});if(i.status===401)return null;if(i.status!==200&&i.status!==202)throw new Error(`HTTP error! status: ${i.status}`);let n=await i.text();return n?JSON.parse(n):null}catch(i){if(t<this.maxRetries){let n=this.initialRetryDelay*Math.pow(2,t);return await new Promise(p=>setTimeout(p,n)),this.post(e,r,s,t+1)}return console.error(\"Max retries reached:\",i),null}}async fetch(e,r,s={}){let t=`${this.baseUrl}${e}`;return this.post(t,r,s,0)}};var o=class{constructor(e){this.options=e;this.queue=[];let r={\"openpanel-client-id\":e.clientId};e.clientSecret&&(r[\"openpanel-client-secret\"]=e.clientSecret),r[\"openpanel-sdk-name\"]=e.sdk||\"node\",r[\"openpanel-sdk-version\"]=e.sdkVersion||\"1.0.0\",this.api=new a({baseUrl:e.apiUrl||\"https://api.openpanel.dev\",defaultHeaders:r})}init(){}ready(){this.options.waitForProfile=!1,this.flush()}async send(e){return this.options.disabled||this.options.filter&&!this.options.filter(e)?Promise.resolve():this.options.waitForProfile&&!this.profileId?(this.queue.push(e),Promise.resolve()):this.api.fetch(\"/track\",e)}setGlobalProperties(e){this.global={...this.global,...e}}async track(e,r){return this.send({type:\"track\",payload:{name:e,profileId:r?.profileId??this.profileId,properties:{...this.global??{},...r??{}}}})}async identify(e){if(e.profileId&&(this.profileId=e.profileId,this.flush()),Object.keys(e).length>1)return this.send({type:\"identify\",payload:{...e,properties:{...this.global,...e.properties}}})}async alias(e){return this.send({type:\"alias\",payload:e})}async increment(e){return this.send({type:\"increment\",payload:e})}async decrement(e){return this.send({type:\"decrement\",payload:e})}clear(){this.profileId=void 0}flush(){this.queue.forEach(e=>{this.send({...e,payload:{...e.payload,profileId:e.payload.profileId??this.profileId}})}),this.queue=[]}};\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@openpanel/sdk/dist/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@openpanel/web/dist/index.js":
/*!***************************************************!*\
  !*** ./node_modules/@openpanel/web/dist/index.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OpenPanel: () => (/* binding */ l),\n/* harmony export */   OpenPanelBase: () => (/* reexport safe */ _openpanel_sdk__WEBPACK_IMPORTED_MODULE_0__.OpenPanel)\n/* harmony export */ });\n/* harmony import */ var _openpanel_sdk__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @openpanel/sdk */ \"(rsc)/./node_modules/@openpanel/sdk/dist/index.js\");\nfunction h(o){return o.replace(/([-_][a-z])/gi,c=>c.toUpperCase().replace(\"-\",\"\").replace(\"_\",\"\"))}var l=class extends _openpanel_sdk__WEBPACK_IMPORTED_MODULE_0__.OpenPanel{constructor(t){super({sdk:\"web\",sdkVersion:\"1.0.1\",...t});this.options=t;this.lastPath=\"\";this.isServer()||(this.setGlobalProperties({__referrer:document.referrer}),this.options.trackScreenViews&&this.trackScreenViews(),this.options.trackOutgoingLinks&&this.trackOutgoingLinks(),this.options.trackAttributes&&this.trackAttributes())}debounce(t,r){clearTimeout(this.debounceTimer),this.debounceTimer=setTimeout(t,r)}isServer(){return typeof document>\"u\"}trackOutgoingLinks(){this.isServer()||document.addEventListener(\"click\",t=>{let r=t.target,e=r.closest(\"a\");if(e&&r){let n=e.getAttribute(\"href\");n?.startsWith(\"http\")&&super.track(\"link_out\",{href:n,text:e.innerText||e.getAttribute(\"title\")||r.getAttribute(\"alt\")||r.getAttribute(\"title\")})}})}trackScreenViews(){if(this.isServer())return;this.screenView();let t=history.pushState;history.pushState=function(...i){let s=t.apply(this,i);return window.dispatchEvent(new Event(\"pushstate\")),window.dispatchEvent(new Event(\"locationchange\")),s};let r=history.replaceState;history.replaceState=function(...i){let s=r.apply(this,i);return window.dispatchEvent(new Event(\"replacestate\")),window.dispatchEvent(new Event(\"locationchange\")),s},window.addEventListener(\"popstate\",()=>{window.dispatchEvent(new Event(\"locationchange\"))});let e=()=>this.debounce(()=>this.screenView(),50);this.options.trackHashChanges?window.addEventListener(\"hashchange\",e):window.addEventListener(\"locationchange\",e)}trackAttributes(){this.isServer()||document.addEventListener(\"click\",t=>{let r=t.target,e=r.closest(\"button\"),n=r.closest(\"a\"),i=e?.getAttribute(\"data-track\")?e:n?.getAttribute(\"data-track\")?n:null;if(i){let s={};for(let a of i.attributes)a.name.startsWith(\"data-\")&&a.name!==\"data-track\"&&(s[h(a.name.replace(/^data-/,\"\"))]=a.value);let p=i.getAttribute(\"data-track\");p&&super.track(p,s)}})}screenView(t,r){if(this.isServer())return;let e,n;typeof t==\"string\"?(e=t,n=r):(e=window.location.href,n=t),this.lastPath!==e&&(this.lastPath=e,super.track(\"screen_view\",{...n??{},__path:e,__title:document.title}))}};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@openpanel/web/dist/index.js\n");

/***/ })

};
;