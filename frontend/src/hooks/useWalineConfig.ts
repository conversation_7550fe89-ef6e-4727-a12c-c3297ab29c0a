import { useState, useEffect } from 'react';

interface WalineConfig {
  server_url: string;
}

const DEFAULT_CONFIG: WalineConfig = {
  server_url: 'https://waline.jyaochen.cn'
};

export const useWalineConfig = () => {
  const [config, setConfig] = useState<WalineConfig>(DEFAULT_CONFIG);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchConfig = async () => {
      try {
        setLoading(true);
        setError(null);
        
        const response = await fetch('/api/public/waline/config');
        if (!response.ok) {
          throw new Error('Failed to fetch Waline config');
        }
        
        const data = await response.json();
        setConfig(data);
      } catch (err) {
        console.error('Failed to load Waline config:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
        // 使用默认配置
        setConfig(DEFAULT_CONFIG);
      } finally {
        setLoading(false);
      }
    };

    fetchConfig();
  }, []);

  return { config, loading, error };
};
