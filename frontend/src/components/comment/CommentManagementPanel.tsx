'use client'

import { useState, useEffect, useCallback } from 'react'
import { 
  MessageCircle, 
  Eye, 
  Heart, 
  Search, 
  Filter, 
  RefreshCw, 
  Settings,
  BarChart3,
  Shield,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  User,
  Calendar,
  Globe,
  Trash2,
  Edit,
  MoreHorizontal
} from 'lucide-react'
import { useWalineCache, useWalineCacheManager } from '../../hooks/useWalineCache'

interface CommentManagementPanelProps {
  className?: string
  onRefresh?: () => void
}

interface Comment {
  objectId: string
  nick: string
  mail: string
  link?: string
  comment: string
  url: string
  ua?: string
  ip?: string
  status: 'approved' | 'waiting' | 'spam' | 'deleted'
  like: number
  dislike: number
  createdAt: string
  updatedAt?: string
  children?: Comment[]
}

interface CommentFilter {
  status: 'all' | 'approved' | 'waiting' | 'spam' | 'deleted'
  search: string
  dateRange: 'all' | 'today' | 'week' | 'month'
  page: string
}

export function CommentManagementPanel({ 
  className = '',
  onRefresh
}: CommentManagementPanelProps) {
  const [comments, setComments] = useState<Comment[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [selectedComments, setSelectedComments] = useState<Set<string>>(new Set())
  const [filter, setFilter] = useState<CommentFilter>({
    status: 'all',
    search: '',
    dateRange: 'all',
    page: 'all'
  })

  const { stats, getStats, clearCache } = useWalineCacheManager()

  // 获取评论列表
  const fetchComments = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)

      // 构建查询参数
      const params = new URLSearchParams({
        page: '1',
        pageSize: '50',
        sortBy: 'createdAt',
        order: 'desc'
      })

      if (filter.status !== 'all') {
        params.append('type', filter.status)
      }

      const response = await fetch(`/api/waline/comment/recent?count=50`)
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()
      let commentsData = data.data || []

      // 客户端过滤
      if (filter.search) {
        const searchTerm = filter.search.toLowerCase()
        commentsData = commentsData.filter((comment: Comment) =>
          comment.comment.toLowerCase().includes(searchTerm) ||
          comment.nick.toLowerCase().includes(searchTerm) ||
          comment.mail.toLowerCase().includes(searchTerm)
        )
      }

      if (filter.dateRange !== 'all') {
        const now = new Date()
        const filterDate = new Date()
        
        switch (filter.dateRange) {
          case 'today':
            filterDate.setDate(now.getDate() - 1)
            break
          case 'week':
            filterDate.setDate(now.getDate() - 7)
            break
          case 'month':
            filterDate.setMonth(now.getMonth() - 1)
            break
        }

        commentsData = commentsData.filter((comment: Comment) =>
          new Date(comment.createdAt) > filterDate
        )
      }

      setComments(commentsData)
    } catch (err) {
      console.error('获取评论失败:', err)
      setError(err instanceof Error ? err.message : '获取失败')
    } finally {
      setLoading(false)
    }
  }, [filter])

  // 批量操作
  const handleBatchAction = useCallback(async (action: 'approve' | 'reject' | 'spam' | 'delete') => {
    if (selectedComments.size === 0) return

    try {
      const promises = Array.from(selectedComments).map(commentId =>
        fetch('/api/waline/admin/moderate', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ comment_id: commentId, action })
        })
      )

      await Promise.all(promises)
      
      // 更新本地状态
      setComments(prev => prev.map(comment => 
        selectedComments.has(comment.objectId)
          ? { ...comment, status: action === 'approve' ? 'approved' as const : action as any }
          : comment
      ))

      setSelectedComments(new Set())
      clearCache('comments')
      onRefresh?.()
    } catch (err) {
      console.error('批量操作失败:', err)
    }
  }, [selectedComments, clearCache, onRefresh])

  // 格式化时间
  const formatDate = useCallback((dateString: string) => {
    const date = new Date(dateString)
    const now = new Date()
    const diff = now.getTime() - date.getTime()
    const minutes = Math.floor(diff / 60000)
    const hours = Math.floor(diff / 3600000)
    const days = Math.floor(diff / 86400000)

    if (minutes < 1) return 'just now'
    if (minutes < 60) return `${minutes}m ago`
    if (hours < 24) return `${hours}h ago`
    if (days < 7) return `${days}d ago`
    return date.toLocaleDateString()
  }, [])

  // 获取状态图标
  const getStatusIcon = useCallback((status: Comment['status']) => {
    switch (status) {
      case 'approved':
        return <CheckCircle className="h-4 w-4 text-green-500" />
      case 'waiting':
        return <Clock className="h-4 w-4 text-yellow-500" />
      case 'spam':
        return <AlertTriangle className="h-4 w-4 text-red-500" />
      case 'deleted':
        return <XCircle className="h-4 w-4 text-gray-500" />
      default:
        return <MessageCircle className="h-4 w-4 text-gray-400" />
    }
  }, [])

  // 初始加载
  useEffect(() => {
    fetchComments()
  }, [fetchComments])

  // 定期刷新缓存统计
  useEffect(() => {
    const interval = setInterval(getStats, 10000) // 10秒
    return () => clearInterval(interval)
  }, [getStats])

  if (loading && comments.length === 0) {
    return (
      <div className={`space-y-6 ${className}`}>
        {/* 加载骨架屏 */}
        <div className="flex items-center justify-between">
          <div className="h-8 w-48 bg-muted animate-pulse rounded" />
          <div className="flex gap-2">
            <div className="h-9 w-24 bg-muted animate-pulse rounded" />
            <div className="h-9 w-24 bg-muted animate-pulse rounded" />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          {[1, 2, 3, 4].map(i => (
            <div key={i} className="p-4 bg-card border rounded-lg space-y-2">
              <div className="h-4 w-20 bg-muted animate-pulse rounded" />
              <div className="h-8 w-16 bg-muted animate-pulse rounded" />
            </div>
          ))}
        </div>

        <div className="space-y-4">
          {[1, 2, 3].map(i => (
            <div key={i} className="p-4 bg-card border rounded-lg space-y-3">
              <div className="flex justify-between">
                <div className="h-4 w-32 bg-muted animate-pulse rounded" />
                <div className="h-4 w-24 bg-muted animate-pulse rounded" />
              </div>
              <div className="space-y-2">
                <div className="h-4 w-full bg-muted animate-pulse rounded" />
                <div className="h-4 w-3/4 bg-muted animate-pulse rounded" />
              </div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* 标题和操作 */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <MessageCircle className="h-6 w-6 text-primary" />
          <h2 className="text-2xl font-bold">Comment Management</h2>
        </div>
        
        <div className="flex items-center gap-2">
          <button
            onClick={() => clearCache()}
            className="flex items-center gap-2 px-3 py-2 text-sm bg-secondary hover:bg-secondary/80 rounded-lg transition-colors"
          >
            <RefreshCw className="h-4 w-4" />
            Clear Cache
          </button>
          
          <button
            onClick={fetchComments}
            disabled={loading}
            className="flex items-center gap-2 px-3 py-2 text-sm bg-primary text-primary-foreground hover:bg-primary/90 rounded-lg transition-colors disabled:opacity-50"
          >
            <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
            Refresh
          </button>
        </div>
      </div>

      {/* 统计卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div className="p-4 bg-card border rounded-lg">
          <div className="flex items-center gap-2 text-muted-foreground mb-2">
            <MessageCircle className="h-4 w-4" />
            <span className="text-sm font-medium">Total Comments</span>
          </div>
          <div className="text-2xl font-bold">{comments.length}</div>
        </div>

        <div className="p-4 bg-card border rounded-lg">
          <div className="flex items-center gap-2 text-muted-foreground mb-2">
            <CheckCircle className="h-4 w-4 text-green-500" />
            <span className="text-sm font-medium">Approved</span>
          </div>
          <div className="text-2xl font-bold text-green-600">
            {comments.filter(c => c.status === 'approved').length}
          </div>
        </div>

        <div className="p-4 bg-card border rounded-lg">
          <div className="flex items-center gap-2 text-muted-foreground mb-2">
            <Clock className="h-4 w-4 text-yellow-500" />
            <span className="text-sm font-medium">Pending</span>
          </div>
          <div className="text-2xl font-bold text-yellow-600">
            {comments.filter(c => c.status === 'waiting').length}
          </div>
        </div>

        <div className="p-4 bg-card border rounded-lg">
          <div className="flex items-center gap-2 text-muted-foreground mb-2">
            <BarChart3 className="h-4 w-4" />
            <span className="text-sm font-medium">Cache Hit Rate</span>
          </div>
          <div className="text-2xl font-bold">
            {stats?.hitRate.toFixed(1) || '0'}%
          </div>
        </div>
      </div>

      {/* 过滤器 */}
      <div className="flex flex-col md:flex-row gap-4 p-4 bg-card border rounded-lg">
        <div className="flex-1">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <input
              type="text"
              placeholder="Search comments, users, emails..."
              value={filter.search}
              onChange={(e) => setFilter(prev => ({ ...prev, search: e.target.value }))}
              className="w-full pl-10 pr-4 py-2 border rounded-lg bg-background text-sm focus:outline-none focus:ring-2 focus:ring-primary"
            />
          </div>
        </div>

        <select
          value={filter.status}
          onChange={(e) => setFilter(prev => ({ ...prev, status: e.target.value as any }))}
          className="px-3 py-2 border rounded-lg bg-background text-sm focus:outline-none focus:ring-2 focus:ring-primary"
        >
          <option value="all">All Status</option>
          <option value="approved">Approved</option>
          <option value="waiting">Pending</option>
          <option value="spam">Spam</option>
          <option value="deleted">Deleted</option>
        </select>

        <select
          value={filter.dateRange}
          onChange={(e) => setFilter(prev => ({ ...prev, dateRange: e.target.value as any }))}
          className="px-3 py-2 border rounded-lg bg-background text-sm focus:outline-none focus:ring-2 focus:ring-primary"
        >
          <option value="all">All Time</option>
          <option value="today">Today</option>
          <option value="week">This Week</option>
          <option value="month">This Month</option>
        </select>
      </div>

      {/* 批量操作 */}
      {selectedComments.size > 0 && (
        <div className="flex items-center gap-2 p-4 bg-primary/10 border border-primary/20 rounded-lg">
          <span className="text-sm font-medium">
            {selectedComments.size} comment{selectedComments.size > 1 ? 's' : ''} selected
          </span>
          
          <div className="flex gap-2 ml-auto">
            <button
              onClick={() => handleBatchAction('approve')}
              className="flex items-center gap-1 px-3 py-1 text-sm bg-green-500 text-white rounded hover:bg-green-600"
            >
              <CheckCircle className="h-3 w-3" />
              Approve
            </button>
            
            <button
              onClick={() => handleBatchAction('spam')}
              className="flex items-center gap-1 px-3 py-1 text-sm bg-red-500 text-white rounded hover:bg-red-600"
            >
              <AlertTriangle className="h-3 w-3" />
              Mark Spam
            </button>
            
            <button
              onClick={() => handleBatchAction('delete')}
              className="flex items-center gap-1 px-3 py-1 text-sm bg-gray-500 text-white rounded hover:bg-gray-600"
            >
              <Trash2 className="h-3 w-3" />
              Delete
            </button>
          </div>
        </div>
      )}

      {/* 评论列表 */}
      <div className="space-y-4">
        {error && (
          <div className="p-4 bg-destructive/10 border border-destructive/20 rounded-lg text-destructive">
            <p className="font-medium">Error loading comments</p>
            <p className="text-sm mt-1">{error}</p>
          </div>
        )}

        {comments.length === 0 && !loading && !error && (
          <div className="text-center py-12 text-muted-foreground">
            <MessageCircle className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p className="text-lg font-medium">No comments found</p>
            <p className="text-sm mt-1">Try adjusting your filters or check back later.</p>
          </div>
        )}

        {comments.map((comment) => (
          <div
            key={comment.objectId}
            className={`p-4 bg-card border rounded-lg hover:shadow-md transition-all ${
              selectedComments.has(comment.objectId) ? 'ring-2 ring-primary' : ''
            }`}
          >
            <div className="flex items-start gap-3">
              {/* 选择框 */}
              <input
                type="checkbox"
                checked={selectedComments.has(comment.objectId)}
                onChange={(e) => {
                  const newSelected = new Set(selectedComments)
                  if (e.target.checked) {
                    newSelected.add(comment.objectId)
                  } else {
                    newSelected.delete(comment.objectId)
                  }
                  setSelectedComments(newSelected)
                }}
                className="mt-1 h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
              />

              {/* 用户头像 */}
              <div className="w-10 h-10 bg-muted rounded-full flex items-center justify-center text-sm font-medium">
                {comment.nick.charAt(0).toUpperCase()}
              </div>

              {/* 评论内容 */}
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-2">
                  <span className="font-medium text-sm">{comment.nick}</span>
                  <span className="text-xs text-muted-foreground">{comment.mail}</span>
                  {comment.link && (
                    <a
                      href={comment.link}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-xs text-primary hover:underline"
                    >
                      <Globe className="h-3 w-3" />
                    </a>
                  )}
                  {getStatusIcon(comment.status)}
                  <span className="text-xs text-muted-foreground ml-auto">
                    {formatDate(comment.createdAt)}
                  </span>
                </div>

                <div className="text-sm mb-3 leading-relaxed">
                  {comment.comment}
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4 text-xs text-muted-foreground">
                    <span className="flex items-center gap-1">
                      <Globe className="h-3 w-3" />
                      {comment.url}
                    </span>
                    
                    {comment.like > 0 && (
                      <span className="flex items-center gap-1">
                        <Heart className="h-3 w-3" />
                        {comment.like}
                      </span>
                    )}
                  </div>

                  <div className="flex items-center gap-1">
                    <button className="p-1 hover:bg-muted rounded">
                      <Edit className="h-3 w-3" />
                    </button>
                    <button className="p-1 hover:bg-muted rounded">
                      <MoreHorizontal className="h-3 w-3" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* 加载更多 */}
      {comments.length > 0 && (
        <div className="text-center">
          <button
            onClick={fetchComments}
            disabled={loading}
            className="px-6 py-2 bg-secondary hover:bg-secondary/80 rounded-lg transition-colors disabled:opacity-50"
          >
            {loading ? 'Loading...' : 'Load More'}
          </button>
        </div>
      )}
    </div>
  )
}

export default CommentManagementPanel