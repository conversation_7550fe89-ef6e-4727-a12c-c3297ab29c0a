'use client'

import { useEffect, useState } from 'react'
import { MessageCircle, Eye, Heart } from 'lucide-react'

interface CommentStatsProps {
  path: string
  className?: string
  showIcons?: boolean
}

interface StatsData {
  commentCount: number
  pageViews: number
  likes: number
}

export function CommentStats({ path, className = '', showIcons = true }: CommentStatsProps) {
  const [stats, setStats] = useState<StatsData>({
    commentCount: 0,
    pageViews: 0,
    likes: 0
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const abortController = new AbortController()
    
    const fetchStats = async () => {
      try {
        setLoading(true)

        // 并行获取评论数量和页面浏览量
        const [commentResponse, pageViewResponse] = await Promise.all([
          fetch(`/api/waline/comment/count?path=${encodeURIComponent(path)}`, {
            signal: abortController.signal,
            headers: {
              'Accept': 'application/json',
            }
          }),
          fetch(`/api/waline/pageview?path=${encodeURIComponent(path)}`, {
            signal: abortController.signal,
            headers: {
              'Accept': 'application/json',
            }
          })
        ])

        if (abortController.signal.aborted) return

        const [commentData, pageViewData] = await Promise.all([
          commentResponse.json(),
          pageViewResponse.json()
        ])
        
        if (abortController.signal.aborted) return

        // 解析数据
        const commentCount = commentData.success ? commentData.data : 0
        const viewCount = pageViewData.success ? pageViewData.time : 0
        const likeCount = pageViewData.success ? (pageViewData.reaction?.[0] || 0) : 0

        setStats({
          commentCount,
          pageViews: viewCount,
          likes: likeCount
        })
      } catch (error) {
        if (error instanceof Error && error.name !== 'AbortError') {
          console.error('Failed to fetch comment stats:', error)
          // 设置默认值而不是保持加载状态
          if (!abortController.signal.aborted) {
            setStats({
              commentCount: 0,
              pageViews: 0,
              likes: 0
            })
          }
        }
      } finally {
        if (!abortController.signal.aborted) {
          setLoading(false)
        }
      }
    }

    if (path) {
      // 延迟一点执行，避免快速切换页面时的问题
      const timer = setTimeout(fetchStats, 100)
      
      return () => {
        clearTimeout(timer)
        abortController.abort()
      }
    } else {
      setLoading(false)
    }

    return () => {
      abortController.abort()
    }
  }, [path])

  if (loading) {
    return (
      <div className={`flex flex-row items-center justify-center gap-6 ${className}`}>
        <div className="flex items-center gap-1.5 text-sm text-muted-foreground">
          {showIcons && <MessageCircle className="h-4 w-4" />}
          <span className="animate-pulse">-</span>
        </div>
        <div className="flex items-center gap-1.5 text-sm text-muted-foreground">
          {showIcons && <Eye className="h-4 w-4" />}
          <span className="animate-pulse">-</span>
        </div>
        <div className="flex items-center gap-1.5 text-sm text-muted-foreground">
          {showIcons && <Heart className="h-4 w-4" />}
          <span className="animate-pulse">-</span>
        </div>
      </div>
    )
  }

  return (
    <div className={`flex flex-row items-center justify-center gap-6 ${className}`}>
      <div className="flex items-center gap-1.5 text-sm text-muted-foreground hover:text-foreground transition-colors">
        {showIcons && <MessageCircle className="h-4 w-4" />}
        <span>{stats.commentCount} comments</span>
      </div>
      
      <div className="flex items-center gap-1.5 text-sm text-muted-foreground hover:text-foreground transition-colors">
        {showIcons && <Eye className="h-4 w-4" />}
        <span>{stats.pageViews} views</span>
      </div>
      
      <div className="flex items-center gap-1.5 text-sm text-muted-foreground hover:text-foreground transition-colors">
        {showIcons && <Heart className="h-4 w-4" />}
        <span>{stats.likes} likes</span>
      </div>
    </div>
  )
}

export default CommentStats