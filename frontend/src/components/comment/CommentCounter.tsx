'use client'

import { useEffect, useState, useCallback } from 'react'
import { MessageCircle, MessageSquare, Reply, Users2, Clock } from 'lucide-react'

interface CommentCounterProps {
  path: string
  url?: string
  className?: string
  showIcon?: boolean
  showLabel?: boolean
  animate?: boolean
  variant?: 'default' | 'compact' | 'detailed'
}

interface CommentCountData {
  count: number
  error?: string
}

interface DetailedCommentStats {
  total: number
  approved: number
  pending: number
  recent: number
  topUsers: Array<{
    nick: string
    count: number
  }>
}

export function CommentCounter({ 
  path, 
  url, 
  className = '', 
  showIcon = true,
  showLabel = true,
  animate = true,
  variant = 'default'
}: CommentCounterProps) {
  const [commentCount, setCommentCount] = useState<number>(0)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // 获取评论数量
  const fetchCommentCount = useCallback(async () => {
    try {
      setError(null)
      const params = new URLSearchParams({ type: 'count' })
      
      if (path) params.append('path', path)
      if (url) params.append('url', url)
      
      const response = await fetch(`/api/waline/comment/count?${params}`)
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }
      
      const result: { data: CommentCountData } = await response.json()
      
      if (result.data.error) {
        throw new Error(result.data.error)
      }
      
      // Waline API返回的可能是数字或对象
      const count = typeof result.data === 'number' ? result.data : result.data.count || 0
      setCommentCount(count)
    } catch (err) {
      console.error('获取评论数量失败:', err)
      setError(err instanceof Error ? err.message : '获取失败')
      setCommentCount(0)
    } finally {
      setLoading(false)
    }
  }, [path, url])

  // 格式化数字显示
  const formatNumber = useCallback((num: number): string => {
    if (num < 1000) return num.toString()
    if (num < 10000) return `${(num / 1000).toFixed(1)}k`
    if (num < 1000000) return `${Math.floor(num / 1000)}k`
    return `${(num / 1000000).toFixed(1)}M`
  }, [])

  // 组件挂载时获取数据
  useEffect(() => {
    fetchCommentCount()
  }, [fetchCommentCount])

  // 定期刷新数据
  useEffect(() => {
    const interval = setInterval(() => {
      fetchCommentCount()
    }, 60000) // 每分钟刷新一次

    return () => clearInterval(interval)
  }, [fetchCommentCount])

  // 渲染不同变体
  const renderContent = () => {
    // 加载状态
    if (loading) {
      return (
        <div className={`inline-flex items-center gap-1.5 text-sm text-muted-foreground ${className}`}>
          {showIcon && <MessageCircle className="h-4 w-4" />}
          <span className="animate-pulse">-</span>
          {showLabel && <span className="animate-pulse">comments</span>}
        </div>
      )
    }

    // 错误状态
    if (error) {
      return (
        <div className={`inline-flex items-center gap-1.5 text-sm text-muted-foreground ${className}`}>
          {showIcon && <MessageCircle className="h-4 w-4 opacity-50" />}
          <span className="opacity-50">-</span>
          {showLabel && <span className="opacity-50">comments</span>}
        </div>
      )
    }

    // 默认变体
    if (variant === 'default') {
      return (
        <div className={`inline-flex items-center gap-1.5 text-sm text-muted-foreground hover:text-foreground transition-colors group ${className}`}>
          {showIcon && (
            <MessageCircle className={`h-4 w-4 transition-all duration-200 ${
              animate ? 'group-hover:scale-110' : ''
            }`} />
          )}
          <span className={`font-medium tabular-nums transition-all duration-300 ${
            animate && commentCount > 0 ? 'group-hover:text-primary' : ''
          }`}>
            {formatNumber(commentCount)}
          </span>
          {showLabel && (
            <span className="text-xs">
              {commentCount === 1 ? 'comment' : 'comments'}
            </span>
          )}
        </div>
      )
    }

    // 紧凑变体
    if (variant === 'compact') {
      return (
        <div className={`inline-flex items-center gap-1 text-xs text-muted-foreground ${className}`}>
          {showIcon && <MessageCircle className="h-3 w-3" />}
          <span className="font-medium tabular-nums">{formatNumber(commentCount)}</span>
        </div>
      )
    }

    // 详细变体 - 这里暂时显示基础信息，后续可以扩展
    if (variant === 'detailed') {
      return (
        <div className={`space-y-1 ${className}`}>
          <div className="flex items-center gap-2">
            <MessageCircle className="h-4 w-4 text-muted-foreground" />
            <span className="font-medium text-sm tabular-nums">
              {commentCount.toLocaleString()}
            </span>
            <span className="text-xs text-muted-foreground">
              {commentCount === 1 ? 'comment' : 'comments'}
            </span>
          </div>
          {commentCount > 0 && (
            <div className="text-xs text-muted-foreground pl-6">
              Join the discussion
            </div>
          )}
        </div>
      )
    }
  }

  return renderContent()
}

// 增强版评论计数器（带详细统计）
export function CommentCounterPro({ 
  path, 
  url, 
  className = '',
  showBreakdown = true,
  showRecentActivity = false
}: CommentCounterProps & {
  showBreakdown?: boolean
  showRecentActivity?: boolean
}) {
  const [stats, setStats] = useState<DetailedCommentStats>({
    total: 0,
    approved: 0,
    pending: 0,
    recent: 0,
    topUsers: []
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchDetailedStats = async () => {
      try {
        // 获取基础评论数量
        const countParams = new URLSearchParams({ type: 'count' })
        if (path) countParams.append('path', path)
        if (url) countParams.append('url', url)
        
        const countResponse = await fetch(`/api/waline/comment/count?${countParams}`)
        const countData = await countResponse.json()
        const totalCount = countData.success ? countData.data : 0

        // 获取最近评论（用于分析活跃度）
        const recentResponse = await fetch('/api/waline/comment/recent?count=10')
        const recentData = await recentResponse.json()
        const recentComments = recentData.data || []

        // 计算最近24小时的评论数
        const oneDayAgo = new Date(Date.now() - 24 * 60 * 60 * 1000)
        const recentCount = recentComments.filter((comment: any) => {
          const commentDate = new Date(comment.createdAt)
          return commentDate > oneDayAgo
        }).length

        // 统计用户评论数（模拟数据）
        const userStats = recentComments.reduce((acc: any, comment: any) => {
          acc[comment.nick] = (acc[comment.nick] || 0) + 1
          return acc
        }, {})

        const topUsers = Object.entries(userStats)
          .sort(([,a], [,b]) => (b as number) - (a as number))
          .slice(0, 3)
          .map(([nick, count]) => ({ nick, count: count as number }))

        setStats({
          total: totalCount,
          approved: Math.floor(totalCount * 0.9), // 模拟已审核
          pending: Math.floor(totalCount * 0.1),  // 模拟待审核
          recent: recentCount,
          topUsers
        })
      } catch (err) {
        console.error('获取详细统计失败:', err)
      } finally {
        setLoading(false)
      }
    }

    fetchDetailedStats()
  }, [path, url])

  if (loading) {
    return (
      <div className={`space-y-3 ${className}`}>
        <div className="flex items-center gap-2">
          <MessageCircle className="h-5 w-5 text-muted-foreground" />
          <div className="h-5 w-20 bg-muted animate-pulse rounded" />
        </div>
        {showBreakdown && (
          <div className="space-y-2 pl-7">
            <div className="h-3 w-24 bg-muted animate-pulse rounded" />
            <div className="h-3 w-20 bg-muted animate-pulse rounded" />
          </div>
        )}
      </div>
    )
  }

  return (
    <div className={`space-y-3 ${className}`}>
      {/* 主要评论计数 */}
      <div className="flex items-center gap-2">
        <MessageCircle className="h-5 w-5 text-muted-foreground" />
        <span className="font-semibold text-lg tabular-nums">
          {stats.total.toLocaleString()}
        </span>
        <span className="text-sm text-muted-foreground">
          {stats.total === 1 ? 'comment' : 'comments'}
        </span>
      </div>

      {/* 详细分解 */}
      {showBreakdown && stats.total > 0 && (
        <div className="space-y-2 pl-7 text-sm text-muted-foreground">
          <div className="flex items-center gap-2">
            <div className="w-2 h-2 bg-green-500 rounded-full" />
            <span>{stats.approved} approved</span>
          </div>
          {stats.pending > 0 && (
            <div className="flex items-center gap-2">
              <div className="w-2 h-2 bg-yellow-500 rounded-full" />
              <span>{stats.pending} pending</span>
            </div>
          )}
        </div>
      )}

      {/* 最近活动 */}
      {showRecentActivity && stats.recent > 0 && (
        <div className="pl-7">
          <div className="flex items-center gap-1.5 text-xs text-muted-foreground">
            <Clock className="h-3 w-3" />
            <span>{stats.recent} in last 24h</span>
          </div>
        </div>
      )}

      {/* 活跃用户 */}
      {stats.topUsers.length > 0 && (
        <div className="pl-7">
          <div className="flex items-center gap-1.5 text-xs text-muted-foreground">
            <Users2 className="h-3 w-3" />
            <span>
              Active: {stats.topUsers.map(user => user.nick).join(', ')}
            </span>
          </div>
        </div>
      )}
    </div>
  )
}

export default CommentCounter