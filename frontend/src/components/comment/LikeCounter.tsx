'use client'

import { useEffect, useState, useCallback } from 'react'
import { Heart, ThumbsUp, ThumbsDown, TrendingUp, Zap } from 'lucide-react'

interface LikeCounterProps {
  path: string
  url?: string
  className?: string
  showIcon?: boolean
  showLabel?: boolean
  animate?: boolean
  variant?: 'heart' | 'thumbs' | 'reactions'
  interactive?: boolean
}

interface LikeData {
  like: number
  dislike: number
  reaction: number[]
  total: number
}

interface ReactionData {
  '👍': number
  '❤️': number
  '😊': number
  '🎉': number
  '🤔': number
  '😢': number
}

export function LikeCounter({ 
  path, 
  url, 
  className = '', 
  showIcon = true,
  showLabel = true,
  animate = true,
  variant = 'heart',
  interactive = false
}: LikeCounterProps) {
  const [likeData, setLikeData] = useState<LikeData>({
    like: 0,
    dislike: 0,
    reaction: [],
    total: 0
  })
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [userReaction, setUserReaction] = useState<string | null>(null)
  const [animating, setAnimating] = useState(false)

  // 获取点赞数据
  const fetchLikeData = useCallback(async () => {
    try {
      setError(null)
      const params = new URLSearchParams()
      if (path) params.append('path', path)
      if (url) params.append('url', url)
      
      const response = await fetch(`/api/waline/reaction?${params}`)

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()

      // 解析反应数据
      let like = 0, dislike = 0, reaction = []
      let total = 0

      if (data.success && data.reaction && Array.isArray(data.reaction)) {
        reaction = data.reaction
        total = reaction.reduce((sum, count) => sum + count, 0)
        like = data.like || 0
        dislike = data.dislike || 0
      }
      
      setLikeData({
        like,
        dislike,
        reaction,
        total
      })
    } catch (err) {
      console.error('获取点赞数据失败:', err)
      setError(err instanceof Error ? err.message : '获取失败')
    } finally {
      setLoading(false)
    }
  }, [path, url])

  // 处理用户点赞
  const handleReaction = useCallback(async (type: 'like' | 'dislike') => {
    if (!interactive || animating) return
    
    setAnimating(true)
    
    try {
      // 这里需要调用Waline的反应API
      // 由于Waline的反应API需要特殊处理，暂时模拟响应
      const newCount = type === 'like' ? likeData.like + 1 : likeData.dislike + 1
      
      setLikeData(prev => ({
        ...prev,
        [type]: newCount,
        total: prev.total + 1
      }))
      
      setUserReaction(type)
      
      // 动画完成后重置状态
      setTimeout(() => setAnimating(false), 300)
      
    } catch (err) {
      console.error('点赞失败:', err)
      setAnimating(false)
    }
  }, [interactive, animating, likeData])

  // 格式化数字显示
  const formatNumber = useCallback((num: number): string => {
    if (num < 1000) return num.toString()
    if (num < 10000) return `${(num / 1000).toFixed(1)}k`
    if (num < 1000000) return `${Math.floor(num / 1000)}k`
    return `${(num / 1000000).toFixed(1)}M`
  }, [])

  // 组件挂载时获取数据
  useEffect(() => {
    fetchLikeData()
  }, [fetchLikeData])

  // 渲染不同变体
  const renderContent = () => {
    // 加载状态
    if (loading) {
      return (
        <div className={`inline-flex items-center gap-1.5 text-sm text-muted-foreground ${className}`}>
          {showIcon && <Heart className="h-4 w-4" />}
          <span className="animate-pulse">-</span>
          {showLabel && <span className="animate-pulse">likes</span>}
        </div>
      )
    }

    // 错误状态
    if (error) {
      return (
        <div className={`inline-flex items-center gap-1.5 text-sm text-muted-foreground ${className}`}>
          {showIcon && <Heart className="h-4 w-4 opacity-50" />}
          <span className="opacity-50">-</span>
          {showLabel && <span className="opacity-50">likes</span>}
        </div>
      )
    }

    // Heart变体
    if (variant === 'heart') {
      return (
        <div className={`inline-flex items-center gap-1.5 text-sm text-muted-foreground hover:text-foreground transition-colors group ${className}`}>
          {showIcon && (
            <button
              onClick={() => interactive && handleReaction('like')}
              disabled={!interactive || animating}
              className={`transition-all duration-200 ${
                interactive ? 'cursor-pointer hover:scale-110' : ''
              } ${
                userReaction === 'like' ? 'text-red-500' : ''
              } ${
                animating ? 'animate-pulse scale-125' : ''
              }`}
            >
              <Heart className={`h-4 w-4 ${
                userReaction === 'like' ? 'fill-current' : ''
              }`} />
            </button>
          )}
          <span className={`font-medium tabular-nums transition-all duration-300 ${
            animate && likeData.like > 0 ? 'group-hover:text-red-500' : ''
          }`}>
            {formatNumber(likeData.like)}
          </span>
          {showLabel && (
            <span className="text-xs">
              {likeData.like === 1 ? 'like' : 'likes'}
            </span>
          )}
        </div>
      )
    }

    // Thumbs变体
    if (variant === 'thumbs') {
      return (
        <div className={`inline-flex items-center gap-3 text-sm ${className}`}>
          {/* 点赞 */}
          <div className="inline-flex items-center gap-1.5 text-muted-foreground hover:text-green-500 transition-colors group">
            {showIcon && (
              <button
                onClick={() => interactive && handleReaction('like')}
                disabled={!interactive || animating}
                className={`transition-all duration-200 ${
                  interactive ? 'cursor-pointer hover:scale-110' : ''
                } ${
                  userReaction === 'like' ? 'text-green-500' : ''
                } ${
                  animating && userReaction === 'like' ? 'animate-bounce' : ''
                }`}
              >
                <ThumbsUp className={`h-4 w-4 ${
                  userReaction === 'like' ? 'fill-current' : ''
                }`} />
              </button>
            )}
            <span className="font-medium tabular-nums">
              {formatNumber(likeData.like)}
            </span>
          </div>

          {/* 点踩 */}
          <div className="inline-flex items-center gap-1.5 text-muted-foreground hover:text-red-500 transition-colors group">
            {showIcon && (
              <button
                onClick={() => interactive && handleReaction('dislike')}
                disabled={!interactive || animating}
                className={`transition-all duration-200 ${
                  interactive ? 'cursor-pointer hover:scale-110' : ''
                } ${
                  userReaction === 'dislike' ? 'text-red-500' : ''
                } ${
                  animating && userReaction === 'dislike' ? 'animate-bounce' : ''
                }`}
              >
                <ThumbsDown className={`h-4 w-4 ${
                  userReaction === 'dislike' ? 'fill-current' : ''
                }`} />
              </button>
            )}
            <span className="font-medium tabular-nums">
              {formatNumber(likeData.dislike)}
            </span>
          </div>
        </div>
      )
    }

    // Reactions变体
    if (variant === 'reactions') {
      const reactions = ['👍', '❤️', '😊', '🎉', '🤔', '😢']
      return (
        <div className={`inline-flex items-center gap-2 ${className}`}>
          {reactions.map((emoji, index) => {
            const count = likeData.reaction[index] || 0
            if (count === 0) return null
            
            return (
              <div
                key={emoji}
                className="inline-flex items-center gap-1 px-2 py-1 bg-muted/50 rounded-full text-xs hover:bg-muted transition-colors"
              >
                <span>{emoji}</span>
                <span className="font-medium tabular-nums">{count}</span>
              </div>
            )
          })}
          
          {likeData.total === 0 && (
            <div className="text-xs text-muted-foreground">No reactions yet</div>
          )}
        </div>
      )
    }
  }

  return renderContent()
}

// 增强版点赞计数器（带趋势分析）
export function LikeCounterPro({ 
  path, 
  url, 
  className = '',
  showTrend = true,
  showRatio = false,
  interactive = true
}: LikeCounterProps & {
  showTrend?: boolean
  showRatio?: boolean
}) {
  const [stats, setStats] = useState({
    current: { like: 0, dislike: 0, total: 0 },
    previous: { like: 0, dislike: 0, total: 0 },
    trend: 0,
    ratio: 0
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchAdvancedStats = async () => {
      try {
        const params = new URLSearchParams()
        if (path) params.append('path', path)
        if (url) params.append('url', url)
        
        const response = await fetch(`/api/waline/reaction?${params}`)
        const data = await response.json()

        const like = data.success ? (data.like || 0) : 0
        const dislike = data.success ? (data.dislike || 0) : 0
        const total = like + dislike
        
        // 模拟历史数据进行趋势计算
        const previousLike = Math.floor(like * 0.8)
        const previousDislike = Math.floor(dislike * 0.8)
        const previousTotal = previousLike + previousDislike
        
        const trend = previousTotal > 0 ? ((total - previousTotal) / previousTotal) * 100 : 0
        const ratio = total > 0 ? (like / total) * 100 : 0
        
        setStats({
          current: { like, dislike, total },
          previous: { like: previousLike, dislike: previousDislike, total: previousTotal },
          trend,
          ratio
        })
      } catch (err) {
        console.error('获取高级点赞统计失败:', err)
      } finally {
        setLoading(false)
      }
    }

    fetchAdvancedStats()
  }, [path, url])

  if (loading) {
    return (
      <div className={`space-y-2 ${className}`}>
        <div className="flex items-center gap-2">
          <Heart className="h-4 w-4 text-muted-foreground" />
          <div className="h-4 w-16 bg-muted animate-pulse rounded" />
        </div>
        {(showTrend || showRatio) && (
          <div className="flex gap-4">
            {showTrend && (
              <div className="flex items-center gap-1">
                <TrendingUp className="h-3 w-3 text-muted-foreground" />
                <div className="h-3 w-12 bg-muted animate-pulse rounded" />
              </div>
            )}
            {showRatio && (
              <div className="flex items-center gap-1">
                <Zap className="h-3 w-3 text-muted-foreground" />
                <div className="h-3 w-12 bg-muted animate-pulse rounded" />
              </div>
            )}
          </div>
        )}
      </div>
    )
  }

  return (
    <div className={`space-y-2 ${className}`}>
      {/* 主要点赞显示 */}
      <div className="flex items-center gap-3">
        <div className="flex items-center gap-1.5">
          <Heart className="h-4 w-4 text-red-500" />
          <span className="font-medium text-sm tabular-nums">
            {stats.current.like.toLocaleString()}
          </span>
        </div>
        
        {stats.current.dislike > 0 && (
          <div className="flex items-center gap-1.5">
            <ThumbsDown className="h-4 w-4 text-muted-foreground" />
            <span className="font-medium text-sm tabular-nums text-muted-foreground">
              {stats.current.dislike.toLocaleString()}
            </span>
          </div>
        )}
      </div>

      {/* 趋势和比率 */}
      {(showTrend || showRatio) && (
        <div className="flex gap-4 text-xs text-muted-foreground">
          {showTrend && stats.trend !== 0 && (
            <div className="flex items-center gap-1">
              <TrendingUp className={`h-3 w-3 ${
                stats.trend > 0 ? 'text-green-500' : 'text-red-500'
              }`} />
              <span className={stats.trend > 0 ? 'text-green-600' : 'text-red-600'}>
                {stats.trend > 0 ? '+' : ''}{stats.trend.toFixed(1)}%
              </span>
            </div>
          )}
          {showRatio && stats.current.total > 0 && (
            <div className="flex items-center gap-1">
              <Zap className="h-3 w-3" />
              <span>{stats.ratio.toFixed(1)}% positive</span>
            </div>
          )}
        </div>
      )}
    </div>
  )
}

export default LikeCounter