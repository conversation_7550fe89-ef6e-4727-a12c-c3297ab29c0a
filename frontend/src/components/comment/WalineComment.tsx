'use client'

import { init } from '@waline/client'
import { useEffect, useRef, useState, useCallback } from 'react'
import { useTheme } from 'next-themes'
import { getWalineConfig } from '@/lib/walineConfig'

interface WalineCommentProps {
  path: string
  title?: string
  className?: string
}

export function WalineComment({ path, title, className = '' }: WalineCommentProps) {
  const walineRef = useRef<HTMLDivElement>(null)
  const { resolvedTheme } = useTheme()
  const [walineInstance, setWalineInstance] = useState<any>(null)
  const [isInitialized, setIsInitialized] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const initTimeoutRef = useRef<NodeJS.Timeout | null>(null)
  const abortControllerRef = useRef<AbortController | null>(null)

  // 安全销毁实例的函数
  const safeDestroy = useCallback((instance: any) => {
    if (instance && typeof instance.destroy === 'function') {
      try {
        instance.destroy()
      } catch (error) {
        console.warn('Waline destroy error:', error)
      }
    }
  }, [])

  // 生成随机头像URL
  const generateRandomAvatar = useCallback((seed: string): string => {
    const avatarStyles = [
      'adventurer',
      'adventurer-neutral',
      'avataaars',
      'avataaars-neutral',
      'bottts',
      'bottts-neutral',
      'fun-emoji',
      'icons',
      'identicon',
      'initials',
      'lorelei',
      'micah',
      'miniavs',
      'open-peeps',
      'personas',
      'pixel-art',
      'pixel-art-neutral'
    ]

    const styleIndex = seed.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0) % avatarStyles.length
    const selectedStyle = avatarStyles[styleIndex]

    return `https://api.dicebear.com/9.x/${selectedStyle}/svg?seed=${encodeURIComponent(seed)}&backgroundColor=transparent&radius=50`
  }, [])

  // 增强头像显示
  const enhanceAvatars = useCallback(() => {
    if (!walineRef.current) return

    const container = walineRef.current
    const avatars = container.querySelectorAll('.wl-avatar')

    avatars.forEach(avatar => {
      if (!avatar.classList.contains('wl-avatar-enhanced')) {
        avatar.classList.add('wl-avatar-enhanced')

        const img = avatar.querySelector('img')
        if (!img) {
          const commentCard = avatar.closest('.wl-card')
          const userNick = commentCard?.querySelector('.wl-nick')?.textContent || 'anonymous'
          const userMail = commentCard?.querySelector('.wl-mail')?.textContent || ''

          const seed = userMail || userNick || `user-${Math.random().toString(36).substr(2, 9)}`
          const avatarUrl = generateRandomAvatar(seed)

          const avatarImg = document.createElement('img')
          avatarImg.src = avatarUrl
          avatarImg.alt = `${userNick}'s avatar`
          avatarImg.style.cssText = `
            width: calc(100% - 4px) !important;
            height: calc(100% - 4px) !important;
            border-radius: 50% !important;
            object-fit: cover !important;
            position: absolute !important;
            top: 2px !important;
            left: 2px !important;
            z-index: 1 !important;
            transition: all 0.3s ease !important;
          `

          avatarImg.onerror = () => {
            avatar.setAttribute('data-no-avatar', 'true')
            avatarImg.remove()
          }

          avatar.appendChild(avatarImg)
          avatar.removeAttribute('data-no-avatar')
        }
      }
    })
  }, [generateRandomAvatar])

  // 修复表情弹窗定位和层级问题
  const fixEmojiPopups = useCallback(() => {
    if (!walineRef.current) return

    const container = walineRef.current

    // 修复表情弹窗
    const emojiButtons = container.querySelectorAll('[data-waline] .wl-action')
    emojiButtons.forEach((button) => {
      if (button.textContent?.includes('😀') || button.getAttribute('title')?.includes('emoji')) {
        button.addEventListener('click', (e) => {
          setTimeout(() => {
            const emojiPopup = container.querySelector('[data-waline] .wl-emoji-popup')
            if (emojiPopup) {
              // 添加头部和关闭按钮
              if (!emojiPopup.querySelector('.wl-popup-header')) {
                const header = document.createElement('div')
                header.className = 'wl-popup-header'
                header.innerHTML = `
                  <div class="wl-popup-title">选择表情</div>
                  <button class="wl-popup-close" type="button">×</button>
                `
                emojiPopup.insertBefore(header, emojiPopup.firstChild)

                // 添加关闭按钮事件
                const closeBtn = header.querySelector('.wl-popup-close')
                if (closeBtn) {
                  closeBtn.addEventListener('click', () => {
                    emojiPopup.classList.remove('display')
                  })
                }
              }

              // 添加背景点击关闭
              const handleBackgroundClick = (e: Event) => {
                if (e.target === emojiPopup) {
                  emojiPopup.classList.remove('display')
                  document.removeEventListener('click', handleBackgroundClick)
                }
              }
              
              setTimeout(() => {
                document.addEventListener('click', handleBackgroundClick)
              }, 100)

              // 添加ESC键关闭
              const handleEscKey = (e: KeyboardEvent) => {
                if (e.key === 'Escape') {
                  emojiPopup.classList.remove('display')
                  document.removeEventListener('keydown', handleEscKey)
                }
              }
              document.addEventListener('keydown', handleEscKey)
            }
          }, 50)
        })
      }
    })

    // 修复GIF弹窗（如果存在）
    const gifButtons = container.querySelectorAll('[data-waline] .wl-action')
    gifButtons.forEach((button) => {
      if (button.textContent?.includes('GIF') || button.getAttribute('title')?.includes('gif')) {
        button.addEventListener('click', (e) => {
          setTimeout(() => {
            const gifPopup = container.querySelector('[data-waline] .wl-gif-popup')
            if (gifPopup) {
              // 添加头部和关闭按钮（如果不存在）
              if (!gifPopup.querySelector('.wl-popup-header')) {
                const header = document.createElement('div')
                header.className = 'wl-popup-header'
                header.innerHTML = `
                  <div class="wl-popup-title">选择GIF</div>
                  <button class="wl-popup-close" type="button">×</button>
                `
                gifPopup.insertBefore(header, gifPopup.firstChild)

                // 添加关闭按钮事件
                const closeBtn = header.querySelector('.wl-popup-close')
                if (closeBtn) {
                  closeBtn.addEventListener('click', () => {
                    gifPopup.classList.remove('display')
                  })
                }
              }
            }
          }, 50)
        })
      }
    })
  }, [])

  // 初始化Waline的函数
  const initWaline = useCallback(async () => {
    if (!walineRef.current) return

    // 取消之前的请求
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }

    // 创建新的AbortController
    abortControllerRef.current = new AbortController()

    try {
      setIsLoading(true)
      setError(null)
      
      // 延迟初始化，避免快速切换导致的问题
      await new Promise(resolve => setTimeout(resolve, 100))

      // 检查组件是否仍然存在
      if (!walineRef.current || abortControllerRef.current?.signal.aborted) {
        return
      }

      // 获取Waline配置
      const config = await getWalineConfig()

      // 清理容器
      walineRef.current.innerHTML = ''

      // 初始化Waline
      const instance = init({
        el: walineRef.current,
        serverURL: config.serverURL,
        path,
        dark: resolvedTheme === 'dark',
        locale: {
          placeholder: 'Share your thoughts and join the discussion...',
          admin: 'Admin',
          level0: 'Newcomer',
          level1: 'Explorer',
          level2: 'Contributor', 
          level3: 'Expert',
          level4: 'Master',
          level5: 'Legend',
          anonymous: 'Anonymous',
          login: 'Sign In',
          logout: 'Sign Out',
          profile: 'Profile',
          nickError: 'Nickname must be at least 3 characters',
          mailError: 'Please enter a valid email address',
          wordHint: 'Please enter your comment',
          sofa: 'Be the first to share your thoughts!',
          submit: 'Publish Comment',
          reply: 'Reply',
          cancelReply: 'Cancel Reply',
          comment: 'Comment',
          refresh: 'Refresh',
          more: 'Load More Comments...',
          preview: 'Preview',
          emoji: 'Emoji',
          uploadImage: 'Upload Image',
          seconds: 'seconds ago',
          minutes: 'minutes ago',
          hours: 'hours ago',
          days: 'days ago',
          now: 'just now'
        },
        emoji: [
          '//unpkg.com/@waline/emojis@1.2.0/alus',
          '//unpkg.com/@waline/emojis@1.2.0/bilibili',
          '//unpkg.com/@waline/emojis@1.2.0/bmoji',
          '//unpkg.com/@waline/emojis@1.2.0/qq',
          '//unpkg.com/@waline/emojis@1.2.0/tieba',
          '//unpkg.com/@waline/emojis@1.2.0/tw-emoji',
          '//unpkg.com/@waline/emojis@1.2.0/weibo',
          '//unpkg.com/@waline/emojis@1.2.0/soul-emoji',
        ],
        meta: ['nick', 'mail', 'link'],
        requiredMeta: ['nick'],
        login: 'enable',
        wordLimit: [0, 1000],
        pageSize: 10,
        lang: 'en-US',
        reaction: true,
        imageUploader: false,
        texRenderer: false,
        search: false
      })

      if (!abortControllerRef.current?.signal.aborted) {
        setWalineInstance(instance)
        setIsInitialized(true)
        setIsLoading(false)
        
        // 添加加载完成的回调和修复表情弹窗
        setTimeout(() => {
          if (walineRef.current) {
            walineRef.current.classList.add('waline-loaded')
            fixEmojiPopups()
            enhanceAvatars()
          }
        }, 300)

        // 监听DOM变化以应用头像增强
        const observer = new MutationObserver(() => {
          enhanceAvatars()
        })

        observer.observe(walineRef.current, {
          childList: true,
          subtree: true
        })

        // 清理函数中断开观察器
        const originalCleanup = () => {
          observer.disconnect()
        }

        // 将清理函数添加到组件卸载时执行
        return originalCleanup
      }
    } catch (error) {
      if (error instanceof Error && error.name !== 'AbortError') {
        console.error('Waline initialization error:', error)
        setError('Failed to load comments. Please refresh the page.')
        setIsLoading(false)
      }
    }
  }, [path, resolvedTheme, safeDestroy])

  // 主useEffect - 处理初始化和清理
  useEffect(() => {
    setIsInitialized(false)
    
    // 使用setTimeout避免在React严格模式下的双重初始化
    initTimeoutRef.current = setTimeout(() => {
      initWaline()
    }, 50)

    return () => {
      // 清理timeout
      if (initTimeoutRef.current) {
        clearTimeout(initTimeoutRef.current)
      }

      // 取消请求
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }

      // 安全销毁实例
      if (walineInstance) {
        safeDestroy(walineInstance)
      }

      setIsInitialized(false)
      setWalineInstance(null)
      setIsLoading(true)
      setError(null)
    }
  }, [path, resolvedTheme, initWaline, safeDestroy])

  return (
    <div className={`waline-container-premium ${className}`}>
      {/* 错误状态 */}
      {error && (
        <div className="mb-6 p-4 bg-destructive/10 border border-destructive/20 rounded-xl text-destructive text-sm text-center">
          <p>{error}</p>
          <button 
            onClick={() => {
              setError(null)
              initWaline()
            }}
            className="mt-2 px-3 py-1 bg-destructive/20 hover:bg-destructive/30 rounded-md transition-colors"
          >
            Retry
          </button>
        </div>
      )}
      
      {/* 主要的Waline容器 */}
      <div 
        ref={walineRef} 
        className={`waline-wrapper-premium transition-all duration-500 ${
          isInitialized ? 'opacity-100' : 'opacity-0'
        }`}
        style={{
          // 自定义CSS变量以适配设计系统
          '--waline-theme-color': 'hsl(var(--primary))',
          '--waline-active-color': 'hsl(var(--primary))',
          '--waline-border-color': 'hsl(var(--border))',
          '--waline-bg-color': 'hsl(var(--background))',
          '--waline-bg-color-light': 'hsl(var(--muted))',
          '--waline-text-color': 'hsl(var(--foreground))',
          '--waline-light-grey': 'hsl(var(--muted-foreground))',
          '--waline-white': 'hsl(var(--card))',
          '--waline-color': 'hsl(var(--foreground))',
          '--waline-border-radius': '0.75rem',
          '--waline-avatar-size': '2.5rem',
          minHeight: isInitialized ? 'auto' : '300px'
        } as React.CSSProperties}
      />
      
      {/* 加载状态 */}
      {isLoading && !error && (
        <div className="flex flex-col items-center justify-center py-16 space-y-4">
          <div className="relative">
            <div className="w-12 h-12 border-4 border-muted-foreground/20 border-t-primary rounded-full animate-spin" />
            <div className="absolute inset-0 w-12 h-12 border-4 border-transparent border-r-primary/50 rounded-full animate-spin" style={{ animationDirection: 'reverse', animationDuration: '0.8s' }} />
          </div>
          <div className="text-center space-y-1">
            <p className="text-sm font-medium text-foreground">Loading Discussion</p>
            <p className="text-xs text-muted-foreground">Preparing comment system...</p>
          </div>
        </div>
      )}
    </div>
  )
}

export default WalineComment