'use client'

import { useEffect, useState, useCallback } from 'react'
import { Eye, TrendingUp, Users } from 'lucide-react'

interface PageViewCounterProps {
  path: string
  title?: string
  className?: string
  showIcon?: boolean
  showLabel?: boolean
  animate?: boolean
  autoIncrement?: boolean
}

interface PageViewData {
  time: number
  path: string
  error?: string
}

export function PageViewCounter({ 
  path, 
  title, 
  className = '', 
  showIcon = true,
  showLabel = true,
  animate = true,
  autoIncrement = true
}: PageViewCounterProps) {
  const [viewCount, setViewCount] = useState<number>(0)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [incremented, setIncremented] = useState(false)

  // 获取页面浏览量
  const fetchPageViews = useCallback(async () => {
    try {
      setError(null)
      const params = new URLSearchParams({ path })
      if (title) params.append('title', title)

      const response = await fetch(`/api/waline/pageview?${params}`)

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`)
      }

      const data = await response.json()

      if (data.error) {
        throw new Error(data.error)
      }

      setViewCount(data.success ? data.time : 0)
    } catch (err) {
      console.error('获取页面浏览量失败:', err)
      setError(err instanceof Error ? err.message : '获取失败')
      setViewCount(0)
    } finally {
      setLoading(false)
    }
  }, [path, title])

  // 增加页面浏览量
  const incrementPageView = useCallback(async () => {
    if (incremented) return
    
    try {
      const response = await fetch('/api/waline/article/pageview/increment', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          path,
          title,
          action: 'inc'
        }),
      })
      
      if (response.ok) {
        const data = await response.json()
        setViewCount(data.success ? data.time : 0)
        setIncremented(true)
      }
    } catch (err) {
      console.error('增加页面浏览量失败:', err)
      // 增加失败不影响显示
    }
  }, [path, title, incremented])

  // 格式化数字显示
  const formatNumber = useCallback((num: number): string => {
    if (num < 1000) return num.toString()
    if (num < 10000) return `${(num / 1000).toFixed(1)}k`
    if (num < 1000000) return `${Math.floor(num / 1000)}k`
    return `${(num / 1000000).toFixed(1)}M`
  }, [])

  // 组件挂载时获取数据
  useEffect(() => {
    fetchPageViews()
  }, [fetchPageViews])

  // 自动增加浏览量（页面加载时）
  useEffect(() => {
    if (autoIncrement && !loading && !error) {
      // 延迟一小段时间再增加，避免重复请求
      const timer = setTimeout(() => {
        incrementPageView()
      }, 1000)
      
      return () => clearTimeout(timer)
    }
  }, [autoIncrement, loading, error, incrementPageView])

  // 加载状态
  if (loading) {
    return (
      <div className={`inline-flex items-center gap-1.5 text-sm text-muted-foreground ${className}`}>
        {showIcon && <Eye className="h-4 w-4" />}
        <span className="animate-pulse">-</span>
        {showLabel && <span className="animate-pulse">views</span>}
      </div>
    )
  }

  // 错误状态
  if (error) {
    return (
      <div className={`inline-flex items-center gap-1.5 text-sm text-muted-foreground ${className}`}>
        {showIcon && <Eye className="h-4 w-4 opacity-50" />}
        <span className="opacity-50">-</span>
        {showLabel && <span className="opacity-50">views</span>}
      </div>
    )
  }

  return (
    <div className={`inline-flex items-center gap-1.5 text-sm text-muted-foreground hover:text-foreground transition-colors group ${className}`}>
      {showIcon && (
        <Eye className={`h-4 w-4 transition-all duration-200 ${
          animate ? 'group-hover:scale-110' : ''
        }`} />
      )}
      <span className={`font-medium tabular-nums transition-all duration-300 ${
        animate && incremented ? 'text-primary scale-105' : ''
      }`}>
        {formatNumber(viewCount)}
      </span>
      {showLabel && (
        <span className="text-xs">
          {viewCount === 1 ? 'view' : 'views'}
        </span>
      )}
    </div>
  )
}

// 增强版页面浏览量组件（带趋势指示）
export function PageViewCounterPro({ 
  path, 
  title, 
  className = '',
  showTrend = true,
  showUniqueVisitors = false
}: PageViewCounterProps & {
  showTrend?: boolean
  showUniqueVisitors?: boolean
}) {
  const [viewData, setViewData] = useState({
    current: 0,
    previous: 0,
    unique: 0,
    trend: 0
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchAdvancedStats = async () => {
      try {
        // 这里可以扩展为获取更详细的统计数据
        const response = await fetch(`/api/waline/pageview?path=${encodeURIComponent(path)}`)
        const data = await response.json()

        const currentViews = data.success ? data.time : 0
        setViewData({
          current: currentViews,
          previous: Math.floor(currentViews * 0.8), // 模拟前期数据
          unique: Math.floor(currentViews * 0.6), // 模拟独立访客
          trend: Math.random() > 0.5 ? 1 : -1 // 模拟趋势
        })
      } catch (err) {
        console.error('获取高级统计失败:', err)
      } finally {
        setLoading(false)
      }
    }

    fetchAdvancedStats()
  }, [path])

  if (loading) {
    return (
      <div className={`space-y-2 ${className}`}>
        <div className="flex items-center gap-2">
          <Eye className="h-4 w-4 text-muted-foreground" />
          <div className="h-4 w-16 bg-muted animate-pulse rounded" />
        </div>
        {(showTrend || showUniqueVisitors) && (
          <div className="flex gap-4">
            {showTrend && (
              <div className="flex items-center gap-1">
                <TrendingUp className="h-3 w-3 text-muted-foreground" />
                <div className="h-3 w-12 bg-muted animate-pulse rounded" />
              </div>
            )}
            {showUniqueVisitors && (
              <div className="flex items-center gap-1">
                <Users className="h-3 w-3 text-muted-foreground" />
                <div className="h-3 w-12 bg-muted animate-pulse rounded" />
              </div>
            )}
          </div>
        )}
      </div>
    )
  }

  return (
    <div className={`space-y-2 ${className}`}>
      {/* 主要浏览量显示 */}
      <div className="flex items-center gap-2">
        <Eye className="h-4 w-4 text-muted-foreground" />
        <span className="font-medium text-sm tabular-nums">
          {viewData.current.toLocaleString()}
        </span>
        <span className="text-xs text-muted-foreground">views</span>
      </div>

      {/* 趋势和独立访客 */}
      {(showTrend || showUniqueVisitors) && (
        <div className="flex gap-4 text-xs text-muted-foreground">
          {showTrend && (
            <div className="flex items-center gap-1">
              <TrendingUp className={`h-3 w-3 ${
                viewData.trend > 0 ? 'text-green-500' : 'text-red-500'
              }`} />
              <span className={viewData.trend > 0 ? 'text-green-600' : 'text-red-600'}>
                {viewData.trend > 0 ? '+' : ''}{((viewData.current - viewData.previous) / viewData.previous * 100).toFixed(1)}%
              </span>
            </div>
          )}
          {showUniqueVisitors && (
            <div className="flex items-center gap-1">
              <Users className="h-3 w-3" />
              <span>{viewData.unique.toLocaleString()} unique</span>
            </div>
          )}
        </div>
      )}
    </div>
  )
}

export default PageViewCounter