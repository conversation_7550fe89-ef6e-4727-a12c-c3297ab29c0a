import { NextRequest, NextResponse } from 'next/server'
import { getWalineConfig } from '@/lib/walineConfig'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const path = searchParams.get('path')
    
    if (!path) {
      return NextResponse.json(
        { error: 'Path parameter is required' },
        { status: 400 }
      )
    }

    // 获取Waline配置
    const config = await getWalineConfig()
    
    // 获取页面浏览量和反应数据
    const data = await getArticleCounter({
      serverURL: config.serverURL,
      lang: config.lang,
      paths: [path],
      type: ['time', 'reaction0', 'reaction1', 'reaction2', 'reaction3', 'reaction4', 'reaction5', 'reaction6', 'reaction7', 'reaction8']
    })

    const result = data[0] || {}
    
    return NextResponse.json({
      success: true,
      time: result.time || 0,
      reaction: [
        result.reaction0 || 0,
        result.reaction1 || 0,
        result.reaction2 || 0,
        result.reaction3 || 0,
        result.reaction4 || 0,
        result.reaction5 || 0,
        result.reaction6 || 0,
        result.reaction7 || 0,
        result.reaction8 || 0
      ],
      path
    })

  } catch (error) {
    console.error('Waline article pageview API error:', error)
    return NextResponse.json(
      { 
        error: 'Failed to fetch page views',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const body = await request.json()
    
    const path = searchParams.get('path') || body.path
    const action = body.action || 'inc'
    
    if (!path) {
      return NextResponse.json(
        { error: 'Path parameter is required' },
        { status: 400 }
      )
    }

    // 获取Waline配置
    const config = await getWalineConfig()

    // 更新页面浏览量
    const data = await updateArticleCounter({
      serverURL: config.serverURL,
      lang: config.lang,
      path,
      type: 'time',
      action: action as 'inc' | 'desc'
    })

    const result = data[0] || {}
    
    return NextResponse.json({
      success: true,
      time: result.time || 0,
      reaction: [
        result.reaction0 || 0,
        result.reaction1 || 0,
        result.reaction2 || 0,
        result.reaction3 || 0,
        result.reaction4 || 0,
        result.reaction5 || 0,
        result.reaction6 || 0,
        result.reaction7 || 0,
        result.reaction8 || 0
      ],
      path
    })

  } catch (error) {
    console.error('Waline article pageview update API error:', error)
    return NextResponse.json(
      { 
        error: 'Failed to update page views',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
