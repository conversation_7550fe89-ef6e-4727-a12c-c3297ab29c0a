import { NextRequest, NextResponse } from 'next/server'
import { getWalineConfig } from '@/lib/walineConfig'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const path = searchParams.get('path')
    const paths = searchParams.getAll('paths') // 支持批量查询
    
    if (!path && paths.length === 0) {
      return NextResponse.json(
        { error: 'Path parameter is required' },
        { status: 400 }
      )
    }

    // 获取Waline配置
    const config = await getWalineConfig()
    
    // 准备路径数组
    const pathsToQuery = paths.length > 0 ? paths : [path!]
    
    // 获取页面浏览量数据
    const data = await getPageview({
      serverURL: config.serverURL,
      lang: config.lang,
      paths: pathsToQuery,
    })

    // 如果只查询单个路径，返回单个结果
    if (path && paths.length === 0) {
      const result = data[0] || {}
      return NextResponse.json({
        success: true,
        time: result.time || 0,
        reaction: [
          result.reaction0 || 0,
          result.reaction1 || 0,
          result.reaction2 || 0,
          result.reaction3 || 0,
          result.reaction4 || 0,
          result.reaction5 || 0,
          result.reaction6 || 0,
          result.reaction7 || 0,
          result.reaction8 || 0
        ],
        path
      })
    }

    // 批量查询返回数组
    return NextResponse.json({
      success: true,
      data: data,
      paths: pathsToQuery
    })

  } catch (error) {
    console.error('Waline pageview API error:', error)
    return NextResponse.json(
      { 
        error: 'Failed to fetch page views',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const body = await request.json()
    
    const path = searchParams.get('path') || body.path
    
    if (!path) {
      return NextResponse.json(
        { error: 'Path parameter is required' },
        { status: 400 }
      )
    }

    // 获取Waline配置
    const config = await getWalineConfig()

    // 更新页面浏览量
    const data = await updatePageview({
      serverURL: config.serverURL,
      lang: config.lang,
      path,
    })

    const result = data[0] || {}
    
    return NextResponse.json({
      success: true,
      time: result.time || 0,
      reaction: [
        result.reaction0 || 0,
        result.reaction1 || 0,
        result.reaction2 || 0,
        result.reaction3 || 0,
        result.reaction4 || 0,
        result.reaction5 || 0,
        result.reaction6 || 0,
        result.reaction7 || 0,
        result.reaction8 || 0
      ],
      path
    })

  } catch (error) {
    console.error('Waline pageview update API error:', error)
    return NextResponse.json(
      { 
        error: 'Failed to update page views',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
