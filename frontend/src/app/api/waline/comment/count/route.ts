import { NextRequest, NextResponse } from 'next/server'
import { getWalineConfig } from '@/lib/walineConfig'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const path = searchParams.get('path')
    const paths = searchParams.getAll('paths') // 支持批量查询
    
    if (!path && paths.length === 0) {
      return NextResponse.json(
        { error: 'Path parameter is required' },
        { status: 400 }
      )
    }

    // 获取Waline配置
    const config = await getWalineConfig()
    
    // 准备路径数组
    const pathsToQuery = paths.length > 0 ? paths : [path!]
    
    // 获取评论数量
    const commentCounts = await fetchCommentCount({
      serverURL: config.serverURL,
      lang: config.lang,
      paths: pathsToQuery,
    })

    // 如果只查询单个路径，返回单个数值
    if (path && paths.length === 0) {
      return NextResponse.json({
        success: true,
        data: commentCounts[0] || 0,
        path
      })
    }

    // 批量查询返回数组
    return NextResponse.json({
      success: true,
      data: commentCounts,
      paths: pathsToQuery
    })

  } catch (error) {
    console.error('Waline comment count API error:', error)
    return NextResponse.json(
      { 
        error: 'Failed to fetch comment count',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
