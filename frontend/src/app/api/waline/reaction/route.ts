import { NextRequest, NextResponse } from 'next/server'
import { getWalineConfig } from '@/lib/walineConfig'

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const path = searchParams.get('path')
    const paths = searchParams.getAll('paths') // 支持批量查询
    const type = searchParams.get('type') || 'reaction0' // 默认获取点赞数
    
    if (!path && paths.length === 0) {
      return NextResponse.json(
        { error: 'Path parameter is required' },
        { status: 400 }
      )
    }

    // 获取Waline配置
    const config = await getWalineConfig()
    
    // 准备路径数组
    const pathsToQuery = paths.length > 0 ? paths : [path!]
    
    // 获取反应数据
    const data = await getArticleCounter({
      serverURL: config.serverURL,
      lang: config.lang,
      paths: pathsToQuery,
      type: ['reaction0', 'reaction1', 'reaction2', 'reaction3', 'reaction4', 'reaction5', 'reaction6', 'reaction7', 'reaction8']
    })

    // 如果只查询单个路径，返回单个结果
    if (path && paths.length === 0) {
      const result = data[0] || {}
      return NextResponse.json({
        success: true,
        reaction: [
          result.reaction0 || 0,
          result.reaction1 || 0,
          result.reaction2 || 0,
          result.reaction3 || 0,
          result.reaction4 || 0,
          result.reaction5 || 0,
          result.reaction6 || 0,
          result.reaction7 || 0,
          result.reaction8 || 0
        ],
        like: result.reaction0 || 0,
        dislike: result.reaction1 || 0,
        path
      })
    }

    // 批量查询返回数组
    return NextResponse.json({
      success: true,
      data: data,
      paths: pathsToQuery
    })

  } catch (error) {
    console.error('Waline reaction API error:', error)
    return NextResponse.json(
      { 
        error: 'Failed to fetch reactions',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}

export async function POST(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const body = await request.json()
    
    const path = searchParams.get('path') || body.path
    const type = body.type || 'reaction0' // 默认点赞
    const action = body.action || 'inc'
    
    if (!path) {
      return NextResponse.json(
        { error: 'Path parameter is required' },
        { status: 400 }
      )
    }

    // 获取Waline配置
    const config = await getWalineConfig()

    // 更新反应数据
    const data = await updateArticleCounter({
      serverURL: config.serverURL,
      lang: config.lang,
      path,
      type,
      action: action as 'inc' | 'desc'
    })

    const result = data[0] || {}
    
    return NextResponse.json({
      success: true,
      reaction: [
        result.reaction0 || 0,
        result.reaction1 || 0,
        result.reaction2 || 0,
        result.reaction3 || 0,
        result.reaction4 || 0,
        result.reaction5 || 0,
        result.reaction6 || 0,
        result.reaction7 || 0,
        result.reaction8 || 0
      ],
      like: result.reaction0 || 0,
      dislike: result.reaction1 || 0,
      path
    })

  } catch (error) {
    console.error('Waline reaction update API error:', error)
    return NextResponse.json(
      { 
        error: 'Failed to update reactions',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
