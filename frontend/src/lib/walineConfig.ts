interface WalineConfig {
  serverURL: string;
  lang: string;
}

interface WalineConfigResponse {
  server_url: string;
  lang: string;
}

// 缓存配置以避免重复请求
let cachedConfig: WalineConfig | null = null;
let configPromise: Promise<WalineConfig> | null = null;

/**
 * 从后台API获取Waline配置
 */
async function fetchWalineConfig(): Promise<WalineConfig> {
  try {
    const response = await fetch('/api/system-config/waline/config', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data: WalineConfigResponse = await response.json();
    
    return {
      serverURL: data.server_url,
      lang: data.lang || 'zh-CN',
    };
  } catch (error) {
    console.error('获取Waline配置失败:', error);
    // 返回默认配置作为后备
    return {
      serverURL: 'https://waline.jyaochen.cn',
      lang: 'zh-CN',
    };
  }
}

/**
 * 获取Waline配置（带缓存）
 */
export async function getWalineConfig(): Promise<WalineConfig> {
  // 如果已有缓存，直接返回
  if (cachedConfig) {
    return cachedConfig;
  }

  // 如果正在请求中，等待请求完成
  if (configPromise) {
    return configPromise;
  }

  // 发起新请求
  configPromise = fetchWalineConfig();
  
  try {
    cachedConfig = await configPromise;
    return cachedConfig;
  } catch (error) {
    // 请求失败时清除promise，允许重试
    configPromise = null;
    throw error;
  }
}

/**
 * 清除配置缓存（用于配置更新后刷新）
 */
export function clearWalineConfigCache(): void {
  cachedConfig = null;
  configPromise = null;
}

/**
 * 获取Waline服务器URL
 */
export async function getWalineServerURL(): Promise<string> {
  const config = await getWalineConfig();
  return config.serverURL;
}

/**
 * 获取Waline语言设置
 */
export async function getWalineLang(): Promise<string> {
  const config = await getWalineConfig();
  return config.lang;
}
