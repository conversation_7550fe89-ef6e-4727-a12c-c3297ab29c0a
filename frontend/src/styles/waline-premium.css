/* Waline Premium Design - Red Dot Award Level */

/* 主容器 */
.waline-container-premium {
  margin: 2rem 0;
  width: 100%;
  position: relative;
}

/* Waline包装器 */
.waline-wrapper-premium {
  width: 100%;
  max-width: none;
  background: linear-gradient(
    135deg,
    hsl(var(--background) / 0.95) 0%,
    hsl(var(--background) / 0.98) 50%,
    hsl(var(--background) / 0.95) 100%
  );
  border: 1px solid hsl(var(--border) / 0.3);
  border-radius: 1.5rem;
  padding: 2rem;
  box-shadow:
    0 20px 40px -12px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(255, 255, 255, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px) saturate(1.1);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

/* === 修复用户登录状态显示问题 === */
/* 只隐藏输入框上方突兀的用户头像显示，保留评论列表中的头像 */
.waline-wrapper-premium .wl-panel .wl-user-info,
.waline-wrapper-premium .wl-panel .wl-user-avatar {
  display: none !important;
}

/* 隐藏登录后在输入框上方显示的用户信息，但保留评论中的用户信息 */
.waline-wrapper-premium .wl-panel > .wl-user,
.waline-wrapper-premium .wl-panel > .wl-login-info {
  display: none !important;
}

/* 确保评论列表中的头像正常显示 */
.waline-wrapper-premium .wl-card .wl-avatar,
.waline-wrapper-premium .wl-cards .wl-avatar {
  display: block !important;
}

/* 优化用户信息在输入框内的显示 */
.waline-wrapper-premium .wl-header .wl-user-meta,
.waline-wrapper-premium .wl-login-status {
  background: linear-gradient(135deg,
    hsl(var(--primary) / 0.08) 0%,
    hsl(var(--primary) / 0.04) 100%) !important;
  border: 1px solid hsl(var(--primary) / 0.2) !important;
  border-radius: 0.5rem !important;
  padding: 0.5rem 0.75rem !important;
  font-size: 0.8rem !important;
  color: hsl(var(--primary)) !important;
  font-weight: 500 !important;
  display: inline-flex !important;
  align-items: center !important;
  gap: 0.5rem !important;
  margin-bottom: 0.75rem !important;
}

/* 确保登录按钮样式优化 */
.waline-wrapper-premium .wl-login-btn,
.waline-wrapper-premium .wl-logout-btn {
  background: linear-gradient(135deg,
    hsl(var(--primary) / 0.1) 0%,
    hsl(var(--primary) / 0.05) 100%) !important;
  border: 1px solid hsl(var(--primary) / 0.3) !important;
  color: hsl(var(--primary)) !important;
  border-radius: 0.5rem !important;
  padding: 0.5rem 1rem !important;
  font-size: 0.8rem !important;
  font-weight: 500 !important;
  transition: all 0.25s ease !important;
}

.waline-wrapper-premium .wl-login-btn:hover,
.waline-wrapper-premium .wl-logout-btn:hover {
  background: linear-gradient(135deg,
    hsl(var(--primary) / 0.15) 0%,
    hsl(var(--primary) / 0.1) 100%) !important;
  border-color: hsl(var(--primary) / 0.5) !important;
  transform: translateY(-1px) !important;
}

.waline-wrapper-premium::before {
  content: '';
  position: absolute;
  top: 0;
  left: 20%;
  right: 20%;
  height: 1px;
  background: linear-gradient(90deg, transparent, hsl(var(--primary) / 0.4), transparent);
  opacity: 0.8;
}

.waline-wrapper-premium:hover {
  transform: translateY(-2px);
  box-shadow: 
    0 32px 64px -12px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.15),
    0 0 80px hsl(var(--primary) / 0.08);
  border-color: hsl(var(--primary) / 0.2);
}

/* 重置任何默认样式 */
.waline-wrapper-premium * {
  box-sizing: border-box;
}

/* === 核心Waline组件样式 === */

/* 主要容器 */
.waline-wrapper-premium .wl-container {
  display: block !important;
  position: relative !important;
  width: 100% !important;
}

/* 输入面板 */
.waline-wrapper-premium .wl-panel {
  background: linear-gradient(
    135deg,
    hsl(var(--card) / 0.6) 0%,
    hsl(var(--card) / 0.8) 100%
  ) !important;
  border: 1px solid hsl(var(--border) / 0.2) !important;
  border-radius: 1rem !important;
  padding: 1.5rem !important;
  margin-bottom: 1.5rem !important;
  backdrop-filter: blur(8px) !important;
  box-shadow: 
    0 4px 20px rgba(0, 0, 0, 0.05),
    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  position: relative !important;
}

.waline-wrapper-premium .wl-panel:hover {
  transform: translateY(-1px) !important;
  border-color: hsl(var(--primary) / 0.3) !important;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.15) !important;
}

/* 用户信息输入区 */
.waline-wrapper-premium .wl-header {
  display: flex !important;
  flex-wrap: wrap !important;
  gap: 0.75rem !important;
  margin-bottom: 1rem !important;
}

.waline-wrapper-premium .wl-header .wl-input {
  flex: 1 !important;
  min-width: 120px !important;
  background: hsl(var(--background) / 0.8) !important;
  border: 1px solid hsl(var(--border) / 0.3) !important;
  border-radius: 0.5rem !important;
  padding: 0.75rem 1rem !important;
  font-size: 0.875rem !important;
  color: hsl(var(--foreground)) !important;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
  backdrop-filter: blur(4px) !important;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05) !important;
  pointer-events: auto !important;
  user-select: auto !important;
  position: relative !important;
  z-index: 1 !important;
}

.waline-wrapper-premium .wl-header .wl-input:focus {
  border-color: hsl(var(--primary) / 0.5) !important;
  box-shadow: 
    inset 0 1px 2px rgba(0, 0, 0, 0.05),
    0 0 0 2px hsl(var(--primary) / 0.15) !important;
  outline: none !important;
  transform: translateY(-1px) !important;
  background: hsl(var(--background)) !important;
}

.waline-wrapper-premium .wl-header .wl-input::placeholder {
  color: hsl(var(--muted-foreground)) !important;
  opacity: 0.7 !important;
}

/* 评论输入框 */
.waline-wrapper-premium .wl-editor {
  background: hsl(var(--background) / 0.6) !important;
  border: 1px solid hsl(var(--border) / 0.3) !important;
  border-radius: 0.75rem !important;
  padding: 1rem !important;
  font-size: 0.9rem !important;
  line-height: 1.6 !important;
  color: hsl(var(--foreground)) !important;
  min-height: 120px !important;
  resize: vertical !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  backdrop-filter: blur(6px) !important;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.06) !important;
  pointer-events: auto !important;
  user-select: auto !important;
  position: relative !important;
  z-index: 1 !important;
}

.waline-wrapper-premium .wl-editor:focus {
  border-color: hsl(var(--primary) / 0.4) !important;
  box-shadow: 
    inset 0 2px 4px rgba(0, 0, 0, 0.06),
    0 0 0 3px hsl(var(--primary) / 0.12),
    0 4px 20px rgba(0, 0, 0, 0.1) !important;
  outline: none !important;
  transform: translateY(-1px) !important;
  background: hsl(var(--background) / 0.8) !important;
}

.waline-wrapper-premium .wl-editor::placeholder {
  color: hsl(var(--muted-foreground)) !important;
  opacity: 0.8 !important;
}

/* 操作区域 */
.waline-wrapper-premium .wl-footer {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  flex-wrap: wrap !important;
  gap: 0.75rem !important;
  margin-top: 1rem !important;
}

/* === 工具栏按钮重新设计 === */
.waline-wrapper-premium .wl-actions {
  display: flex !important;
  align-items: center !important;
  gap: 0.5rem !important;
}

/* === 工具栏按钮优化 - Red Dot Award Design === */
.waline-wrapper-premium .wl-action {
  position: relative !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 42px !important;
  height: 42px !important;
  border-radius: 0.75rem !important;
  background: linear-gradient(135deg,
    hsl(var(--card) / 0.9) 0%,
    hsl(var(--card) / 0.95) 100%) !important;
  border: 1px solid hsl(var(--border) / 0.2) !important;
  color: hsl(var(--muted-foreground)) !important;
  cursor: pointer !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  backdrop-filter: blur(12px) !important;
  box-shadow:
    0 3px 12px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.15) !important;
  overflow: hidden !important;
  font-size: 18px !important;
  pointer-events: auto !important;
  z-index: 2 !important;
}

.waline-wrapper-premium .wl-action::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: 0.75rem;
  background: linear-gradient(135deg,
    hsl(var(--primary) / 0.15) 0%,
    hsl(var(--primary) / 0.08) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.waline-wrapper-premium .wl-action:hover::before {
  opacity: 1;
}

.waline-wrapper-premium .wl-action:hover {
  transform: translateY(-2px) scale(1.08) !important;
  border-color: hsl(var(--primary) / 0.4) !important;
  color: hsl(var(--primary)) !important;
  box-shadow:
    0 8px 24px rgba(0, 0, 0, 0.12),
    inset 0 1px 0 rgba(255, 255, 255, 0.25) !important;
  background: linear-gradient(135deg,
    hsl(var(--primary) / 0.12) 0%,
    hsl(var(--primary) / 0.08) 100%) !important;
}

.waline-wrapper-premium .wl-action:active {
  transform: translateY(0) scale(0.96) !important;
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.08) !important;
}

/* === 特殊按钮样式优化 === */
/* 文件上传按钮 */
.waline-wrapper-premium .wl-action[title*="上传图片"],
.waline-wrapper-premium .wl-action[title*="Upload"],
.waline-wrapper-premium .wl-action[title*="选择文件"] {
  background: linear-gradient(135deg,
    hsl(210, 60%, 95%) 0%,
    hsl(210, 60%, 98%) 100%) !important;
  border-color: hsl(210, 40%, 85%) !important;
  color: hsl(210, 70%, 45%) !important;
}

.waline-wrapper-premium .wl-action[title*="上传图片"]:hover,
.waline-wrapper-premium .wl-action[title*="Upload"]:hover,
.waline-wrapper-premium .wl-action[title*="选择文件"]:hover {
  background: linear-gradient(135deg,
    hsl(210, 80%, 88%) 0%,
    hsl(210, 80%, 94%) 100%) !important;
  color: hsl(210, 90%, 35%) !important;
  border-color: hsl(210, 70%, 65%) !important;
  box-shadow:
    0 8px 24px hsl(210, 60%, 85%),
    inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
}

/* 表情按钮 */
.waline-wrapper-premium .wl-action[title*="表情"],
.waline-wrapper-premium .wl-action[title*="Emoji"] {
  background: linear-gradient(135deg,
    hsl(45, 85%, 94%) 0%,
    hsl(45, 85%, 97%) 100%) !important;
  border-color: hsl(45, 50%, 80%) !important;
  color: hsl(45, 80%, 40%) !important;
}

.waline-wrapper-premium .wl-action[title*="表情"]:hover,
.waline-wrapper-premium .wl-action[title*="Emoji"]:hover {
  background: linear-gradient(135deg,
    hsl(45, 95%, 85%) 0%,
    hsl(45, 95%, 92%) 100%) !important;
  color: hsl(45, 90%, 30%) !important;
  border-color: hsl(45, 70%, 60%) !important;
  box-shadow:
    0 8px 24px hsl(45, 60%, 80%),
    inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
}

/* 隐藏的文件输入 */
.waline-wrapper-premium input[type="file"] {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
  opacity: 0 !important;
  pointer-events: none !important;
}

/* 文件拖拽区域 */
.waline-wrapper-premium .wl-panel.wl-drop-active {
  border-color: hsl(var(--primary) / 0.5) !important;
  background: linear-gradient(135deg, 
    hsl(var(--primary) / 0.05) 0%, 
    hsl(var(--primary) / 0.02) 100%) !important;
  box-shadow: 
    0 8px 32px hsl(var(--primary) / 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.15) !important;
}

.waline-wrapper-premium .wl-panel.wl-drop-active::after {
  content: '拖拽文件到此处上传';
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: hsl(var(--primary) / 0.05);
  border-radius: 1rem;
  color: hsl(var(--primary));
  font-weight: 600;
  font-size: 1.1rem;
  backdrop-filter: blur(12px);
  z-index: 10;
}

/* 提交按钮 */
.waline-wrapper-premium .wl-btn {
  background: linear-gradient(
    135deg,
    hsl(var(--primary)) 0%,
    hsl(var(--primary) / 0.9) 100%
  ) !important;
  color: hsl(var(--primary-foreground)) !important;
  border: none !important;
  border-radius: 0.5rem !important;
  padding: 0.75rem 1.5rem !important;
  font-size: 0.875rem !important;
  font-weight: 600 !important;
  letter-spacing: 0.025em !important;
  cursor: pointer !important;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1) !important;
  box-shadow: 
    0 2px 8px hsl(var(--primary) / 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
  position: relative !important;
  overflow: hidden !important;
  pointer-events: auto !important;
  z-index: 2 !important;
}

.waline-wrapper-premium .wl-btn::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, transparent 50%);
  opacity: 0;
  transition: opacity 0.25s ease;
}

.waline-wrapper-premium .wl-btn:hover::before {
  opacity: 1;
}

.waline-wrapper-premium .wl-btn:hover {
  transform: translateY(-2px) scale(1.02) !important;
  box-shadow: 
    0 6px 20px hsl(var(--primary) / 0.35),
    inset 0 1px 0 rgba(255, 255, 255, 0.25) !important;
}

.waline-wrapper-premium .wl-btn:active {
  transform: translateY(0) scale(0.98) !important;
  box-shadow: 
    0 1px 4px hsl(var(--primary) / 0.3) !important;
}

.waline-wrapper-premium .wl-btn:disabled {
  opacity: 0.5 !important;
  cursor: not-allowed !important;
  transform: none !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
}

/* 次要按钮 */
.waline-wrapper-premium .wl-btn.wl-secondary-btn {
  background: linear-gradient(
    135deg,
    hsl(var(--secondary) / 0.1) 0%,
    hsl(var(--secondary) / 0.05) 100%
  ) !important;
  color: hsl(var(--secondary-foreground)) !important;
  border: 1px solid hsl(var(--border) / 0.3) !important;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05) !important;
}

.waline-wrapper-premium .wl-btn.wl-secondary-btn:hover {
  background: linear-gradient(
    135deg,
    hsl(var(--secondary) / 0.15) 0%,
    hsl(var(--secondary) / 0.1) 100%
  ) !important;
  border-color: hsl(var(--primary) / 0.3) !important;
  box-shadow: 0 3px 12px rgba(0, 0, 0, 0.1) !important;
}

/* === 评论列表样式 === */

.waline-wrapper-premium .wl-cards {
  margin-top: 2rem !important;
}

.waline-wrapper-premium .wl-card {
  background: linear-gradient(
    135deg,
    hsl(var(--card) / 0.5) 0%,
    hsl(var(--card) / 0.8) 100%
  ) !important;
  border: 1px solid hsl(var(--border) / 0.2) !important;
  border-radius: 1rem !important;
  padding: 1.5rem !important;
  margin-bottom: 1rem !important;
  backdrop-filter: blur(8px) !important;
  box-shadow: 
    0 2px 16px rgba(0, 0, 0, 0.04),
    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  position: relative !important;
  overflow: hidden !important;
}

.waline-wrapper-premium .wl-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, hsl(var(--primary) / 0.2), transparent);
}

.waline-wrapper-premium .wl-card:hover {
  transform: translateY(-1px) !important;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.15) !important;
  border-color: hsl(var(--primary) / 0.25) !important;
}

/* === 头像系统重新设计 === */
.waline-wrapper-premium .wl-card .wl-head {
  display: flex !important;
  align-items: flex-start !important;
  gap: 1rem !important;
  margin-bottom: 1.25rem !important;
  padding-bottom: 0.75rem !important;
  border-bottom: 1px solid hsl(var(--border) / 0.15) !important;
  position: relative !important;
}

/* 头像容器 - 红点奖级设计 */
.waline-wrapper-premium .wl-avatar {
  position: relative !important;
  width: 48px !important;
  height: 48px !important;
  border-radius: 50% !important;
  background: linear-gradient(135deg,
    hsl(var(--primary) / 0.1) 0%,
    hsl(var(--primary) / 0.05) 100%) !important;
  padding: 2px !important;
  flex-shrink: 0 !important;
  overflow: hidden !important;
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
  transition: all 0.35s cubic-bezier(0.4, 0, 0.2, 1) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
}

/* 默认头像样式 - 作为随机头像的后备方案 */
.waline-wrapper-premium .wl-avatar:empty::before,
.waline-wrapper-premium .wl-avatar[data-no-avatar="true"]::before {
  content: '👤' !important;
  font-size: 20px !important;
  color: hsl(var(--primary)) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 100% !important;
  height: 100% !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  z-index: 1 !important;
}

/* 随机头像加载状态 */
.waline-wrapper-premium .wl-avatar img[src*="dicebear"] {
  background: linear-gradient(135deg,
    hsl(var(--primary) / 0.1) 0%,
    hsl(var(--primary) / 0.05) 100%) !important;
  transition: all 0.35s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* 随机头像悬停效果增强 */
.waline-wrapper-premium .wl-card:hover .wl-avatar img[src*="dicebear"] {
  transform: scale(1.05) rotate(2deg) !important;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
}

.waline-wrapper-premium .wl-avatar::before {
  content: '';
  position: absolute;
  inset: -2px;
  border-radius: 50%;
  background: linear-gradient(135deg, 
    hsl(var(--primary) / 0.3) 0%,
    transparent 30%,
    transparent 70%,
    hsl(var(--primary) / 0.3) 100%);
  z-index: -1;
  opacity: 0;
  transition: opacity 0.35s ease;
}

.waline-wrapper-premium .wl-card:hover .wl-avatar::before {
  opacity: 1;
}

.waline-wrapper-premium .wl-avatar img {
  width: calc(100% - 4px) !important;
  height: calc(100% - 4px) !important;
  border-radius: 50% !important;
  object-fit: cover !important;
  transition: all 0.35s cubic-bezier(0.4, 0, 0.2, 1) !important;
  position: absolute !important;
  top: 2px !important;
  left: 2px !important;
  z-index: 1 !important;
}

/* 确保有头像图片时隐藏默认头像 */
.waline-wrapper-premium .wl-avatar:has(img)::before {
  display: none !important;
}

/* 为没有头像的用户提供更好的默认显示 */
.waline-wrapper-premium .wl-avatar[data-no-avatar="true"]::before {
  content: '👤' !important;
  font-size: 20px !important;
  color: hsl(var(--primary)) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 100% !important;
  height: 100% !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  z-index: 1 !important;
}

/* 兼容性：如果浏览器不支持:has()选择器 */
.waline-wrapper-premium .wl-avatar:not([data-no-avatar]):empty::before {
  content: '👤' !important;
  font-size: 20px !important;
  color: hsl(var(--primary)) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 100% !important;
  height: 100% !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  z-index: 1 !important;
}

/* 确保头像容器在所有情况下都正确显示 */
.waline-wrapper-premium .wl-card .wl-avatar,
.waline-wrapper-premium .wl-cards .wl-avatar,
.waline-wrapper-premium [data-waline] .wl-avatar {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  position: relative !important;
  width: 48px !important;
  height: 48px !important;
  border-radius: 50% !important;
  background: linear-gradient(135deg,
    hsl(var(--primary) / 0.1) 0%,
    hsl(var(--primary) / 0.05) 100%) !important;
  flex-shrink: 0 !important;
  overflow: visible !important;
}

/* 覆盖可能的冲突样式 */
.waline-wrapper-premium [data-waline] .wl-card-item .wl-user .wl-user-avatar {
  width: 48px !important;
  height: 48px !important;
  border: 2px solid hsl(var(--primary) / 0.2) !important;
  background: linear-gradient(135deg,
    hsl(var(--primary) / 0.1) 0%,
    hsl(var(--primary) / 0.05) 100%) !important;
}

.waline-wrapper-premium .wl-card:hover .wl-avatar {
  transform: scale(1.05) !important;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.3) !important;
}

/* 用户信息区域 */
.waline-wrapper-premium .wl-card .wl-meta {
  flex: 1 !important;
  min-width: 0 !important;
}

.waline-wrapper-premium .wl-card .wl-author {
  display: flex !important;
  align-items: center !important;
  gap: 0.5rem !important;
  margin-bottom: 0.25rem !important;
}

.waline-wrapper-premium .wl-card .wl-nick {
  color: hsl(var(--primary)) !important;
  font-weight: 700 !important;
  font-size: 0.9rem !important;
  letter-spacing: 0.025em !important;
  transition: all 0.2s ease !important;
}

.waline-wrapper-premium .wl-card:hover .wl-nick {
  color: hsl(var(--primary) / 0.8) !important;
  transform: translateX(2px) !important;
}

.waline-wrapper-premium .wl-card .wl-time {
  color: hsl(var(--muted-foreground)) !important;
  font-size: 0.75rem !important;
  font-weight: 500 !important;
  opacity: 0.8 !important;
}

/* 评论内容 */
.waline-wrapper-premium .wl-card .wl-content {
  color: hsl(var(--foreground)) !important;
  line-height: 1.7 !important;
  font-size: 0.9rem !important;
  letter-spacing: 0.01em !important;
  transition: color 0.2s ease !important;
}

.waline-wrapper-premium .wl-card:hover .wl-content {
  color: hsl(var(--foreground) / 0.9) !important;
}

.waline-wrapper-premium .wl-card .wl-content p {
  margin: 0.75rem 0 !important;
}

.waline-wrapper-premium .wl-card .wl-content p:first-child {
  margin-top: 0 !important;
}

.waline-wrapper-premium .wl-card .wl-content p:last-child {
  margin-bottom: 0 !important;
}

.waline-wrapper-premium .wl-card .wl-content code {
  background: hsl(var(--muted)) !important;
  color: hsl(var(--foreground)) !important;
  padding: 0.2rem 0.4rem !important;
  border-radius: 0.25rem !important;
  font-size: 0.8rem !important;
  font-weight: 500 !important;
}

.waline-wrapper-premium .wl-card .wl-content pre {
  background: hsl(var(--muted)) !important;
  color: hsl(var(--foreground)) !important;
  padding: 1rem !important;
  border-radius: 0.5rem !important;
  overflow-x: auto !important;
  margin: 1rem 0 !important;
  border: 1px solid hsl(var(--border) / 0.3) !important;
}

/* 评论操作 */
.waline-wrapper-premium .wl-card .wl-action {
  display: flex !important;
  align-items: center !important;
  gap: 1rem !important;
  margin-top: 1rem !important;
  padding-top: 0.75rem !important;
  border-top: 1px solid hsl(var(--border) / 0.1) !important;
}

.waline-wrapper-premium .wl-card .wl-action button {
  color: hsl(var(--muted-foreground)) !important;
  font-size: 0.75rem !important;
  font-weight: 500 !important;
  padding: 0.4rem 0.8rem !important;
  border-radius: 0.375rem !important;
  border: none !important;
  background: none !important;
  cursor: pointer !important;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
  pointer-events: auto !important;
}

.waline-wrapper-premium .wl-card .wl-action button:hover {
  color: hsl(var(--primary)) !important;
  background: hsl(var(--primary) / 0.08) !important;
  transform: translateY(-1px) !important;
}

/* === 整体布局细节优化 === */

/* 评论区标题和统计 */
.waline-wrapper-premium .wl-comment-title {
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
  margin-bottom: 1.5rem !important;
  padding-bottom: 1rem !important;
  border-bottom: 1px solid hsl(var(--border) / 0.1) !important;
}

.waline-wrapper-premium .wl-comment-title h3 {
  font-size: 1.25rem !important;
  font-weight: 700 !important;
  color: hsl(var(--foreground)) !important;
  margin: 0 !important;
  display: flex !important;
  align-items: center !important;
  gap: 0.5rem !important;
}

.waline-wrapper-premium .wl-comment-title h3::before {
  content: '💬';
  font-size: 1.1rem;
}

.waline-wrapper-premium .wl-stats {
  display: flex !important;
  align-items: center !important;
  gap: 1rem !important;
  font-size: 0.8rem !important;
  color: hsl(var(--muted-foreground)) !important;
}

.waline-wrapper-premium .wl-stats .wl-stat {
  display: flex !important;
  align-items: center !important;
  gap: 0.25rem !important;
  padding: 0.25rem 0.5rem !important;
  background: hsl(var(--muted) / 0.3) !important;
  border-radius: 0.375rem !important;
  transition: all 0.2s ease !important;
}

.waline-wrapper-premium .wl-stats .wl-stat:hover {
  background: hsl(var(--muted) / 0.5) !important;
  transform: translateY(-1px) !important;
}

/* 间距优化系统 */
.waline-wrapper-premium .wl-container > * + * {
  margin-top: 1.5rem !important;
}

.waline-wrapper-premium .wl-card + .wl-card {
  margin-top: 1.25rem !important;
}

.waline-wrapper-premium .wl-card .wl-content + .wl-action {
  margin-top: 1rem !important;
}

/* 视觉层次系统 */
.waline-wrapper-premium {
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 0.75rem;
  --spacing-lg: 1rem;
  --spacing-xl: 1.5rem;
  --spacing-2xl: 2rem;
  --spacing-3xl: 3rem;
  
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
  
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.08);
  --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.12);
  --shadow-xl: 0 16px 48px rgba(0, 0, 0, 0.15);
}

/* 微交互优化 */
.waline-wrapper-premium .wl-card {
  transform-origin: center !important;
}

.waline-wrapper-premium .wl-card:hover {
  --shadow-color: rgba(0, 0, 0, 0.1);
  box-shadow: 
    0 8px 32px var(--shadow-color),
    inset 0 1px 0 rgba(255, 255, 255, 0.15) !important;
}

.waline-wrapper-premium .wl-btn,
.waline-wrapper-premium .wl-action,
.waline-wrapper-premium .wl-sort-btn {
  will-change: transform, box-shadow !important;
}

/* 文字排版优化 */
.waline-wrapper-premium .wl-content {
  text-rendering: optimizeLegibility !important;
  -webkit-font-smoothing: antialiased !important;
  -moz-osx-font-smoothing: grayscale !important;
}

.waline-wrapper-premium .wl-content h1,
.waline-wrapper-premium .wl-content h2,
.waline-wrapper-premium .wl-content h3,
.waline-wrapper-premium .wl-content h4,
.waline-wrapper-premium .wl-content h5,
.waline-wrapper-premium .wl-content h6 {
  font-weight: 700 !important;
  margin-top: 1.5rem !important;
  margin-bottom: 0.75rem !important;
  color: hsl(var(--foreground)) !important;
}

.waline-wrapper-premium .wl-content blockquote {
  position: relative !important;
  margin: 1.5rem 0 !important;
  padding: 1rem 1.5rem !important;
  background: linear-gradient(135deg, 
    hsl(var(--muted) / 0.3) 0%, 
    hsl(var(--muted) / 0.1) 100%) !important;
  border-left: 4px solid hsl(var(--primary)) !important;
  border-radius: 0 var(--radius-lg) var(--radius-lg) 0 !important;
  font-style: italic !important;
  color: hsl(var(--muted-foreground)) !important;
}

.waline-wrapper-premium .wl-content blockquote::before {
  content: '"';
  position: absolute;
  top: -0.5rem;
  left: 0.75rem;
  font-size: 3rem;
  color: hsl(var(--primary) / 0.3);
  font-family: serif;
  line-height: 1;
}

/* 表情和媒体内容优化 */
.waline-wrapper-premium .wl-content img {
  border-radius: var(--radius-lg) !important;
  box-shadow: var(--shadow-md) !important;
  transition: all 0.3s ease !important;
  max-width: 100% !important;
  height: auto !important;
}

.waline-wrapper-premium .wl-content img:hover {
  transform: scale(1.02) !important;
  box-shadow: var(--shadow-lg) !important;
}

/* === 响应式设计优化 === */
@media (max-width: 1024px) {
  .waline-wrapper-premium {
    padding: 1.75rem !important;
  }
  
  .waline-wrapper-premium .wl-sort-container {
    flex-wrap: wrap !important;
    justify-content: center !important;
  }
}

@media (max-width: 768px) {
  .waline-wrapper-premium {
    padding: 1.25rem !important;
    margin: 0 -0.5rem !important;
    border-radius: var(--radius-xl) !important;
    box-shadow: 
      0 8px 32px rgba(0, 0, 0, 0.08),
      inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
  }

  .waline-wrapper-premium .wl-panel {
    padding: 1.25rem !important;
  }

  .waline-wrapper-premium .wl-header {
    flex-direction: column !important;
    gap: 0.75rem !important;
  }

  .waline-wrapper-premium .wl-header .wl-input {
    width: 100% !important;
    font-size: 16px !important; /* Prevent iOS zoom */
  }

  .waline-wrapper-premium .wl-editor {
    min-height: 100px !important;
    font-size: 16px !important; /* Prevent iOS zoom */
  }

  .waline-wrapper-premium .wl-card {
    padding: 1.25rem !important;
    margin-bottom: 1rem !important;
  }

  .waline-wrapper-premium .wl-card .wl-head {
    gap: 0.75rem !important;
  }

  .waline-wrapper-premium .wl-avatar {
    width: 40px !important;
    height: 40px !important;
  }

  .waline-wrapper-premium .wl-sort-container {
    padding: 0.5rem !important;
    flex-wrap: wrap !important;
    gap: 0.25rem !important;
  }

  .waline-wrapper-premium .wl-sort-btn {
    padding: 0.375rem 0.75rem !important;
    font-size: 0.75rem !important;
  }

  .waline-wrapper-premium .wl-operation {
    flex-wrap: wrap !important;
    gap: 0.375rem !important;
  }
}

@media (max-width: 480px) {
  .waline-wrapper-premium {
    padding: 1rem !important;
    margin: 0 -1rem !important;
    border-radius: var(--radius-lg) !important;
  }

  .waline-wrapper-premium .wl-panel {
    padding: 1rem !important;
  }

  .waline-wrapper-premium .wl-card {
    padding: 1rem !important;
  }

  .waline-wrapper-premium .wl-avatar {
    width: 36px !important;
    height: 36px !important;
  }
}

/* === 加载状态增强 === */
.waline-wrapper-premium.waline-loaded {
  animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* === 可访问性改进 === */
.waline-wrapper-premium *:focus-visible {
  outline: 2px solid hsl(var(--primary)) !important;
  outline-offset: 2px !important;
}

/* === 暗色模式优化 === */
@media (prefers-color-scheme: dark) {
  .waline-wrapper-premium .wl-content img {
    opacity: 0.9 !important;
    border-radius: 0.5rem !important;
  }

  .waline-wrapper-premium .wl-content blockquote {
    border-left: 4px solid hsl(var(--primary)) !important;
    padding-left: 1rem !important;
    margin: 1rem 0 !important;
    background: hsl(var(--muted) / 0.3) !important;
    border-radius: 0 0.5rem 0.5rem 0 !important;
    padding: 0.75rem 1rem !important;
  }
}

/* === 表情包弹窗优化 - Red Dot Award Design === */
.waline-wrapper-premium .wl-emoji {
  position: fixed !important;
  top: 50% !important;
  left: 50% !important;
  transform: translate(-50%, -50%) !important;
  z-index: 9999 !important;
  width: 90vw !important;
  max-width: 420px !important;
  min-width: 320px !important;
  height: auto !important;
  max-height: 65vh !important;
  background: linear-gradient(135deg,
    hsl(var(--card) / 0.95) 0%,
    hsl(var(--card) / 0.98) 100%) !important;
  border: 1px solid hsl(var(--border) / 0.2) !important;
  border-radius: 1.25rem !important;
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
  backdrop-filter: blur(20px) saturate(1.2) !important;
  overflow: hidden !important;
  display: none !important;
  flex-direction: column !important;
  opacity: 0 !important;
  visibility: hidden !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}



@keyframes emojiModalFadeIn {
  from {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

/* 表情包弹窗头部样式 */
.waline-wrapper-premium .wl-emoji .wl-popup-header {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  padding: 1rem 1.25rem 0.75rem !important;
  border-bottom: 1px solid hsl(var(--border) / 0.2) !important;
  margin-bottom: 0 !important;
  background: linear-gradient(135deg,
    hsl(var(--muted) / 0.3) 0%,
    hsl(var(--muted) / 0.1) 100%) !important;
  border-radius: 1.25rem 1.25rem 0 0 !important;
}

.waline-wrapper-premium .wl-emoji .wl-popup-title {
  font-size: 1rem !important;
  font-weight: 600 !important;
  color: hsl(var(--foreground)) !important;
  margin: 0 !important;
}

.waline-wrapper-premium .wl-emoji .wl-popup-close {
  width: 2rem !important;
  height: 2rem !important;
  border: 1px solid hsl(var(--border) / 0.3) !important;
  border-radius: 0.5rem !important;
  background: hsl(var(--background)) !important;
  color: hsl(var(--muted-foreground)) !important;
  cursor: pointer !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  transition: all 0.2s ease !important;
  font-size: 1.2rem !important;
  line-height: 1 !important;
  padding: 0 !important;
  margin: 0 !important;
}

.waline-wrapper-premium .wl-emoji .wl-popup-close:hover {
  background: hsl(var(--destructive) / 0.1) !important;
  border-color: hsl(var(--destructive) / 0.3) !important;
  color: hsl(var(--destructive)) !important;
  transform: scale(1.05) !important;
}

/* 强制隐藏表情包弹窗的初始状态 */
.waline-wrapper-premium .wl-emoji:not(.wl-emoji-open):not([style*="display: block"]):not([style*="display: flex"]) {
  display: none !important;
  opacity: 0 !important;
  visibility: hidden !important;
}

/* 更强的隐藏规则 - 确保表情包弹窗默认隐藏 */
.waline-wrapper-premium .wl-emoji {
  display: none !important;
  opacity: 0 !important;
  visibility: hidden !important;
}

/* 只有在明确标记为打开状态时才显示 - 高优先级覆盖 */
.waline-wrapper-premium .wl-emoji.wl-emoji-open,
.waline-wrapper-premium .wl-emoji[style*="display: block"],
.waline-wrapper-premium .wl-emoji[style*="display: flex"] {
  display: flex !important;
  opacity: 1 !important;
  visibility: visible !important;
  animation: emojiModalFadeIn 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  z-index: 9999 !important;
}

/* 表情包弹窗背景遮罩 */
.waline-wrapper-premium .wl-emoji::before {
  content: '' !important;
  position: fixed !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  bottom: 0 !important;
  background: rgba(0, 0, 0, 0.4) !important;
  backdrop-filter: blur(8px) !important;
  z-index: -1 !important;
  opacity: 0 !important;
  transition: opacity 0.3s ease !important;
}

/* 显示状态下的背景遮罩 */
.waline-wrapper-premium .wl-emoji.wl-emoji-open::before,
.waline-wrapper-premium .wl-emoji[style*="display: block"]::before,
.waline-wrapper-premium .wl-emoji[style*="display: flex"]::before {
  opacity: 1 !important;
}

/* 表情包标签页优化 */
.waline-wrapper-premium .wl-emoji .wl-tab-wrapper {
  display: flex !important;
  background: hsl(var(--muted) / 0.5) !important;
  border-radius: 1rem 1rem 0 0 !important;
  padding: 0.25rem !important;
  margin: 0.75rem 0.75rem 0 0.75rem !important;
  gap: 0.25rem !important;
}

.waline-wrapper-premium .wl-emoji .wl-tab {
  flex: 1 !important;
  background: transparent !important;
  color: hsl(var(--muted-foreground)) !important;
  border: none !important;
  border-radius: 0.75rem !important;
  padding: 0.75rem 1rem !important;
  font-size: 0.8rem !important;
  font-weight: 500 !important;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1) !important;
  cursor: pointer !important;
  text-align: center !important;
  position: relative !important;
  overflow: hidden !important;
}

.waline-wrapper-premium .wl-emoji .wl-tab::before {
  content: '' !important;
  position: absolute !important;
  inset: 0 !important;
  background: linear-gradient(135deg,
    hsl(var(--primary) / 0.1) 0%,
    hsl(var(--primary) / 0.05) 100%) !important;
  opacity: 0 !important;
  transition: opacity 0.25s ease !important;
}

.waline-wrapper-premium .wl-emoji .wl-tab:hover::before {
  opacity: 1 !important;
}

.waline-wrapper-premium .wl-emoji .wl-tab:hover {
  color: hsl(var(--primary)) !important;
  transform: translateY(-1px) !important;
}

.waline-wrapper-premium .wl-emoji .wl-tab.wl-active {
  background: linear-gradient(135deg,
    hsl(var(--primary)) 0%,
    hsl(var(--primary) / 0.9) 100%) !important;
  color: hsl(var(--primary-foreground)) !important;
  box-shadow:
    0 2px 8px hsl(var(--primary) / 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
  transform: translateY(-1px) !important;
}

.waline-wrapper-premium .wl-emoji .wl-tab.wl-active::before {
  opacity: 0 !important;
}

/* 表情包内容区域 */
.waline-wrapper-premium .wl-emoji .wl-content,
.waline-wrapper-premium .wl-emoji .wl-emoji-container,
.waline-wrapper-premium .wl-emoji .wl-emoji-list {
  flex: 1 !important;
  padding: 1rem 1.25rem 1.25rem !important;
  max-height: 45vh !important;
  min-height: 200px !important;
  overflow-y: auto !important;
  display: grid !important;
  grid-template-columns: repeat(auto-fill, minmax(40px, 1fr)) !important;
  gap: 0.25rem !important;
  align-content: start !important;
  background: transparent !important;
}

/* 确保表情包内容区域的所有子元素都可见 */
.waline-wrapper-premium .wl-emoji .wl-content *,
.waline-wrapper-premium .wl-emoji .wl-emoji-container *,
.waline-wrapper-premium .wl-emoji .wl-emoji-list * {
  color: inherit !important;
  opacity: 1 !important;
  filter: none !important;
}

.waline-wrapper-premium .wl-emoji .wl-content::-webkit-scrollbar {
  width: 6px !important;
}

.waline-wrapper-premium .wl-emoji .wl-content::-webkit-scrollbar-track {
  background: hsl(var(--muted) / 0.3) !important;
  border-radius: 3px !important;
}

.waline-wrapper-premium .wl-emoji .wl-content::-webkit-scrollbar-thumb {
  background: hsl(var(--primary) / 0.4) !important;
  border-radius: 3px !important;
}

.waline-wrapper-premium .wl-emoji .wl-content::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--primary) / 0.6) !important;
}

/* 表情包按钮 */
.waline-wrapper-premium .wl-emoji .wl-emoji-item,
.waline-wrapper-premium .wl-emoji button {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  width: 40px !important;
  height: 40px !important;
  padding: 0.25rem !important;
  margin: 0 !important;
  border-radius: 0.5rem !important;
  transition: all 0.2s ease !important;
  cursor: pointer !important;
  font-size: 1.25rem !important;
  line-height: 1 !important;
  background: transparent !important;
  border: none !important;
  color: inherit !important;
  filter: none !important;
  opacity: 1 !important;
}

/* 表情包图片样式 */
.waline-wrapper-premium .wl-emoji .wl-emoji-item img,
.waline-wrapper-premium .wl-emoji button img {
  width: 100% !important;
  height: 100% !important;
  object-fit: contain !important;
  border-radius: 0.25rem !important;
  filter: none !important;
  opacity: 1 !important;
  display: block !important;
}

/* 表情包文本内容 */
.waline-wrapper-premium .wl-emoji .wl-emoji-item,
.waline-wrapper-premium .wl-emoji button {
  color: hsl(var(--foreground)) !important;
  text-decoration: none !important;
}

.waline-wrapper-premium .wl-emoji .wl-emoji-item:hover {
  background: hsl(var(--primary) / 0.1) !important;
  transform: scale(1.05) !important;
  box-shadow: 0 2px 8px hsl(var(--primary) / 0.2) !important;
}

/* 移动设备表情包弹窗优化 */
@media (max-width: 640px) {
  .waline-wrapper-premium .wl-emoji {
    width: 95vw !important;
    max-width: none !important;
    min-width: 280px !important;
    max-height: 70vh !important;
    margin: 0 !important;
  }

  .waline-wrapper-premium .wl-emoji .wl-content {
    grid-template-columns: repeat(auto-fill, minmax(35px, 1fr)) !important;
    padding: 0.75rem 1rem 1rem !important;
    max-height: 50vh !important;
  }

  .waline-wrapper-premium .wl-emoji .wl-emoji-item {
    width: 35px !important;
    height: 35px !important;
    font-size: 1.1rem !important;
  }

  .waline-wrapper-premium .wl-emoji .wl-tab-wrapper {
    margin: 0.5rem 0.75rem 0 0.75rem !important;
    padding: 0.125rem !important;
  }

  .waline-wrapper-premium .wl-emoji .wl-tab {
    padding: 0.5rem 0.75rem !important;
    font-size: 0.875rem !important;
  }
}

/* === 评论排序系统优化 - 水平布局 === */
.waline-wrapper-premium .wl-sort-container {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  flex-direction: row !important;
  flex-wrap: wrap !important;
  gap: 0.75rem !important;
  margin: 1.5rem 0 !important;
  padding: 1rem 1.5rem !important;
  background: linear-gradient(135deg,
    hsl(var(--card) / 0.8) 0%,
    hsl(var(--card) / 0.95) 100%) !important;
  border: 1px solid hsl(var(--border) / 0.2) !important;
  border-radius: 1.25rem !important;
  backdrop-filter: blur(12px) !important;
  box-shadow:
    0 4px 20px rgba(0, 0, 0, 0.06),
    inset 0 1px 0 rgba(255, 255, 255, 0.15) !important;
  position: relative !important;
  overflow: hidden !important;
}

.waline-wrapper-premium .wl-sort-container::before {
  content: '' !important;
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  height: 2px !important;
  background: linear-gradient(90deg,
    transparent 0%,
    hsl(var(--primary) / 0.3) 50%,
    transparent 100%) !important;
}

.waline-wrapper-premium .wl-sort-btn {
  position: relative !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  padding: 0.625rem 1.25rem !important;
  border-radius: 0.75rem !important;
  font-size: 0.85rem !important;
  font-weight: 600 !important;
  color: hsl(var(--muted-foreground)) !important;
  background: linear-gradient(135deg,
    hsl(var(--background) / 0.6) 0%,
    hsl(var(--background) / 0.8) 100%) !important;
  border: 1px solid hsl(var(--border) / 0.3) !important;
  cursor: pointer !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  letter-spacing: 0.025em !important;
  pointer-events: auto !important;
  z-index: 2 !important;
  min-width: 100px !important;
  text-align: center !important;
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.04),
    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
}

.waline-wrapper-premium .wl-sort-btn::before {
  content: '';
  position: absolute;
  inset: 0;
  border-radius: 0.75rem;
  background: linear-gradient(135deg,
    hsl(var(--primary) / 0.12) 0%,
    hsl(var(--primary) / 0.08) 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.waline-wrapper-premium .wl-sort-btn:hover::before {
  opacity: 1;
}

.waline-wrapper-premium .wl-sort-btn:hover {
  color: hsl(var(--primary)) !important;
  transform: translateY(-2px) scale(1.02) !important;
  border-color: hsl(var(--primary) / 0.4) !important;
  box-shadow:
    0 6px 20px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
}

.waline-wrapper-premium .wl-sort-btn.wl-active {
  color: hsl(var(--primary-foreground)) !important;
  background: linear-gradient(135deg,
    hsl(var(--primary)) 0%,
    hsl(var(--primary) / 0.85) 100%) !important;
  border-color: hsl(var(--primary) / 0.6) !important;
  box-shadow:
    0 4px 16px hsl(var(--primary) / 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.25) !important;
  transform: translateY(-2px) scale(1.02) !important;
}

.waline-wrapper-premium .wl-sort-btn.wl-active::before {
  opacity: 0;
}

/* 排序按钮图标 */
.waline-wrapper-premium .wl-sort-btn::after {
  content: '';
  margin-left: 0.25rem;
  width: 0;
  height: 0;
  border-left: 3px solid transparent;
  border-right: 3px solid transparent;
  border-top: 4px solid currentColor;
  opacity: 0.6;
  transition: all 0.25s ease;
}

.waline-wrapper-premium .wl-sort-btn[data-sort="latest"]::after {
  border-top: 4px solid currentColor;
  border-bottom: none;
}

.waline-wrapper-premium .wl-sort-btn[data-sort="oldest"]::after {
  border-bottom: 4px solid currentColor;
  border-top: none;
}

.waline-wrapper-premium .wl-sort-btn[data-sort="hottest"]::after {
  content: '🔥';
  border: none;
  font-size: 0.7rem;
  margin-left: 0.125rem;
}

.waline-wrapper-premium .wl-sort-btn[data-sort="comment"]::after {
  content: '💬';
  border: none;
  font-size: 0.7rem;
  margin-left: 0.125rem;
}

/* === 分页器重新设计 === */
.waline-wrapper-premium .wl-operation {
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 0.5rem !important;
  margin-top: 2rem !important;
  padding: 1rem !important;
}

.waline-wrapper-premium .wl-operation .wl-btn {
  position: relative !important;
  display: inline-flex !important;
  align-items: center !important;
  justify-content: center !important;
  min-width: 40px !important;
  height: 40px !important;
  padding: 0.5rem 1rem !important;
  border-radius: 0.5rem !important;
  font-size: 0.85rem !important;
  font-weight: 500 !important;
  color: hsl(var(--muted-foreground)) !important;
  background: linear-gradient(135deg, 
    hsl(var(--card) / 0.5) 0%, 
    hsl(var(--card) / 0.8) 100%) !important;
  border: 1px solid hsl(var(--border) / 0.2) !important;
  cursor: pointer !important;
  transition: all 0.25s cubic-bezier(0.4, 0, 0.2, 1) !important;
  backdrop-filter: blur(8px) !important;
  box-shadow: 
    0 2px 8px rgba(0, 0, 0, 0.04),
    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
  margin: 0 !important;
  pointer-events: auto !important;
}

.waline-wrapper-premium .wl-operation .wl-btn:hover {
  color: hsl(var(--primary)) !important;
  border-color: hsl(var(--primary) / 0.3) !important;
  transform: translateY(-1px) !important;
  box-shadow: 
    0 4px 16px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.15) !important;
}

.waline-wrapper-premium .wl-operation .wl-current {
  color: hsl(var(--primary-foreground)) !important;
  background: linear-gradient(135deg, 
    hsl(var(--primary)) 0%, 
    hsl(var(--primary) / 0.9) 100%) !important;
  border-color: hsl(var(--primary)) !important;
  box-shadow: 
    0 4px 16px hsl(var(--primary) / 0.25),
    inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
  transform: translateY(-1px) !important;
}

.waline-wrapper-premium .wl-operation .wl-current:hover {
  transform: translateY(-1px) !important;
  box-shadow: 
    0 6px 20px hsl(var(--primary) / 0.35),
    inset 0 1px 0 rgba(255, 255, 255, 0.25) !important;
}

/* 加载更多按钮 */
.waline-wrapper-premium .wl-load-more {
  display: block !important;
  width: 100% !important;
  max-width: 200px !important;
  margin: 2rem auto !important;
  padding: 0.875rem 2rem !important;
  border-radius: 0.75rem !important;
  background: linear-gradient(135deg, 
    hsl(var(--secondary) / 0.8) 0%, 
    hsl(var(--secondary) / 0.6) 100%) !important;
  color: hsl(var(--secondary-foreground)) !important;
  border: 1px solid hsl(var(--border) / 0.3) !important;
  font-weight: 600 !important;
  font-size: 0.9rem !important;
  letter-spacing: 0.025em !important;
  cursor: pointer !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
  backdrop-filter: blur(8px) !important;
  box-shadow: 
    0 4px 16px rgba(0, 0, 0, 0.08),
    inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
}

.waline-wrapper-premium .wl-load-more:hover {
  transform: translateY(-2px) !important;
  border-color: hsl(var(--primary) / 0.3) !important;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.12),
    inset 0 1px 0 rgba(255, 255, 255, 0.15) !important;
}

/* === 错误和空状态 === */
.waline-wrapper-premium .wl-error {
  background: hsl(var(--destructive) / 0.1) !important;
  color: hsl(var(--destructive)) !important;
  padding: 1rem !important;
  border-radius: 0.75rem !important;
  border: 1px solid hsl(var(--destructive) / 0.2) !important;
  margin: 1rem 0 !important;
  text-align: center !important;
}

.waline-wrapper-premium .wl-empty {
  text-align: center !important;
  padding: 3rem 1rem !important;
  color: hsl(var(--muted-foreground)) !important;
}

.waline-wrapper-premium .wl-empty::before {
  content: '💬';
  display: block;
  font-size: 3rem;
  margin-bottom: 1rem;
  opacity: 0.3;
}

/* === 表情包显示修复 === */
/* 调试和修复表情包显示问题 */
.waline-wrapper-premium .wl-emoji * {
  box-sizing: border-box !important;
}

/* 确保表情包弹窗内的所有元素都可见 */
.waline-wrapper-premium .wl-emoji,
.waline-wrapper-premium .wl-emoji * {
  color: hsl(var(--foreground)) !important;
}

/* 修复可能的灰色覆盖问题 */
.waline-wrapper-premium .wl-emoji button,
.waline-wrapper-premium .wl-emoji .wl-emoji-item,
.waline-wrapper-premium .wl-emoji .wl-content button {
  background-color: transparent !important;
  color: hsl(var(--foreground)) !important;
  border: none !important;
  outline: none !important;
  filter: none !important;
  opacity: 1 !important;
}

/* 确保表情包图片和文本都能正确显示 */
.waline-wrapper-premium .wl-emoji img,
.waline-wrapper-premium .wl-emoji .wl-emoji-item img {
  opacity: 1 !important;
  filter: none !important;
  visibility: visible !important;
  display: block !important;
}

/* 表情包文本显示 */
.waline-wrapper-premium .wl-emoji button:not(:has(img)) {
  font-family: "Apple Color Emoji", "Segoe UI Emoji", "Noto Color Emoji", sans-serif !important;
  font-size: 1.5rem !important;
  line-height: 1 !important;
}

/* 强制修复表情包颜色问题 */
.waline-wrapper-premium .wl-emoji .wl-content button,
.waline-wrapper-premium .wl-emoji .wl-content .wl-emoji-item,
.waline-wrapper-premium .wl-emoji button,
.waline-wrapper-premium .wl-emoji .wl-emoji-item {
  color: #000 !important;
  background: transparent !important;
  border: none !important;
  filter: none !important;
  opacity: 1 !important;
  text-shadow: none !important;
  -webkit-text-fill-color: initial !important;
  -webkit-background-clip: initial !important;
}

/* 确保表情包在暗色主题下也能正确显示 */
[data-theme="dark"] .waline-wrapper-premium .wl-emoji .wl-content button,
[data-theme="dark"] .waline-wrapper-premium .wl-emoji .wl-content .wl-emoji-item,
[data-theme="dark"] .waline-wrapper-premium .wl-emoji button,
[data-theme="dark"] .waline-wrapper-premium .wl-emoji .wl-emoji-item {
  color: #fff !important;
}

/* 表情包图片确保可见 */
.waline-wrapper-premium .wl-emoji img {
  opacity: 1 !important;
  filter: none !important;
  visibility: visible !important;
  display: inline-block !important;
  max-width: 100% !important;
  max-height: 100% !important;
}